# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/backend/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/dist
/backend/dist

# misc
.DS_Store
*.pem
.idea/
.vscode/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env
/backend/.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# backup files
*.backup
*.bak

# Logs
logs
*.log

# Temporary files
tmp/
temp/