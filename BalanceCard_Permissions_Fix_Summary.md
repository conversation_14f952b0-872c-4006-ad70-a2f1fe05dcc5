# BalanceCard Permissions Fix Summary

## 🚨 **Problem Identified**

### **Error Details:**
```
Error: Forbidden: Insufficient permissions
    at handleResponse (webpack-internal:///(app-pages-browser)/./lib/api.ts:53:15)
    at async fetchSimulatedBalance (webpack-internal:///(app-pages-browser)/./components/balance-card.tsx:64:30)
```

### **Root Cause Analysis:**
The BalanceCard component was failing to fetch the simulated account balance due to **incorrect token prioritization** in the API authentication:

1. **Context**: BalanceCard is used in the admin dashboard (`app/admin/dashboard/page.tsx`)
2. **Endpoint**: `/loans/simulated-balance` requires ADMIN or STAFF role
3. **Issue**: `getSimulatedBalance()` function was checking for `token` first, then `adminToken`
4. **Problem**: Admin users authenticate with `adminToken`, not `token`

## ✅ **Solution Implemented**

### **1. Fixed Token Prioritization in getSimulatedBalance**

**File**: `lib/api.ts`

**Before:**
```typescript
const token = localStorage.getItem('token') || localStorage.getItem('adminToken');
```

**After:**
```typescript
// Prioritize adminToken for admin functions, fallback to regular token
const token = localStorage.getItem('adminToken') || localStorage.getItem('token');
```

### **2. Standardized Admin API Token Usage**

**File**: `lib/api.ts`

**Problem**: `adminApi` functions were inconsistently using `localStorage.getItem('token')` instead of the proper `getAuthToken()` helper.

**Solution**: Updated all `adminApi` functions to use `getAuthToken()`:

```typescript
// Before
const token = localStorage.getItem('token');

// After  
const token = getAuthToken();
```

**Functions Updated:**
- `getPendingLoans()`
- `approveLoan()`
- `rejectLoan()`
- `disburseLoan()`
- `updateTransactionStatus()`

### **3. Reorganized Helper Function**

**File**: `lib/api.ts`

**Change**: Moved `getAuthToken()` helper function before `adminApi` definition to ensure proper usage.

**Helper Function:**
```typescript
const getAuthToken = (): string | null => {
  // Check for admin token first (for admin users)
  const adminToken = localStorage.getItem('adminToken');
  if (adminToken) return adminToken;

  // Fall back to regular token (for regular users)
  const token = localStorage.getItem('token');
  return token;
};
```

## 📋 **Files Modified**

### **1. lib/api.ts**
- ✅ Fixed `getSimulatedBalance()` token prioritization
- ✅ Updated all `adminApi` functions to use `getAuthToken()`
- ✅ Moved `getAuthToken()` helper function for proper scope
- ✅ Removed duplicate `getAuthToken()` definition

## 🔍 **Technical Details**

### **Authentication Flow:**
1. **Admin Login**: Stores token as `adminToken` in localStorage
2. **Regular User Login**: Stores token as `token` in localStorage
3. **Admin Functions**: Should prioritize `adminToken` first
4. **Regular Functions**: Can use either token type

### **Backend Authorization:**
- **Endpoint**: `GET /loans/simulated-balance`
- **Middleware**: `authenticate` + `authorize(UserRole.ADMIN, UserRole.STAFF)`
- **Required Roles**: ADMIN or STAFF
- **Token Validation**: JWT verification with role checking

### **Frontend Token Strategy:**
- **Admin Dashboard**: Uses `adminToken` for authentication
- **User Dashboard**: Uses `token` for authentication
- **Hybrid Functions**: Use `getAuthToken()` helper for flexibility

## 🧪 **Testing Instructions**

### **1. Verify Admin Authentication**
1. Login to admin dashboard (`/admin/login`)
2. Navigate to admin dashboard (`/admin/dashboard`)
3. Check that BalanceCard loads without errors
4. Verify simulated balance displays correctly

### **2. Check Browser Storage**
1. Open browser DevTools → Application → Local Storage
2. Verify `adminToken` is present after admin login
3. Confirm token contains valid JWT with admin role

### **3. Monitor Network Requests**
1. Open DevTools → Network tab
2. Refresh admin dashboard
3. Check `/loans/simulated-balance` request
4. Verify `Authorization: Bearer <adminToken>` header

### **4. Test Error Scenarios**
1. Clear `adminToken` from localStorage
2. Refresh page - should redirect to login
3. Login with non-admin user - should not access admin dashboard

## 🎯 **Expected Results**

### **Before Fix:**
- ❌ "Forbidden: Insufficient permissions" error
- ❌ BalanceCard fails to load simulated balance
- ❌ Admin dashboard shows error state

### **After Fix:**
- ✅ BalanceCard loads successfully
- ✅ Simulated balance displays (E25,000.00 SZL)
- ✅ No permission errors in console
- ✅ Admin dashboard functions properly

## 🔧 **Additional Improvements**

### **1. Consistent Token Usage**
All admin functions now use the same token prioritization strategy:
1. Check for `adminToken` first
2. Fallback to `token` if needed
3. Consistent error handling

### **2. Better Error Messages**
Admin functions now have clearer error messages indicating admin authentication requirements.

### **3. Code Organization**
- Helper functions defined before usage
- Consistent API patterns across all modules
- Removed duplicate code

## 🚨 **Prevention Measures**

### **1. Token Usage Guidelines**
- **Admin Functions**: Always use `getAuthToken()` or prioritize `adminToken`
- **User Functions**: Use `token` for regular user operations
- **Hybrid Functions**: Use `getAuthToken()` helper

### **2. Testing Checklist**
- Test admin functions with admin authentication
- Verify token prioritization in new admin endpoints
- Check localStorage token storage after login

### **3. Code Review Points**
- Ensure admin endpoints use correct token strategy
- Verify role-based authorization middleware
- Check frontend token usage consistency

## 🎉 **Conclusion**

The permissions error was caused by incorrect token prioritization in admin functions. The fix ensures that:

1. **Admin functions prioritize `adminToken`** for proper authentication
2. **Consistent token usage** across all admin API calls
3. **Proper role-based access control** for simulated balance endpoint
4. **Better error handling** and code organization

The BalanceCard component should now work correctly in the admin dashboard without permission errors.
