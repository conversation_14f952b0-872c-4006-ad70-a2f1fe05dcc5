# Umlamleli Admin Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Admin Access](#admin-access)
   - [Admin Login](#admin-login)
   - [Admin Dashboard Overview](#admin-dashboard-overview)
3. [User Management](#user-management)
   - [Viewing Users](#viewing-users)
   - [Managing User Accounts](#managing-user-accounts)
   - [User Verification](#user-verification)
4. [Loan Management](#loan-management)
   - [Reviewing Loan Applications](#reviewing-loan-applications)
   - [Approving Loans](#approving-loans)
   - [Rejecting Loans](#rejecting-loans)
   - [Disbursing Loans](#disbursing-loans)
   - [Monitoring Active Loans](#monitoring-active-loans)
5. [Notification Management](#notification-management)
   - [Creating Notifications](#creating-notifications)
   - [Managing Notifications](#managing-notifications)
   - [Notification Templates](#notification-templates)
6. [System Settings](#system-settings)
   - [General Settings](#general-settings)
   - [Loan Parameters](#loan-parameters)
   - [Security Settings](#security-settings)
7. [Reports and Analytics](#reports-and-analytics)
   - [Loan Performance](#loan-performance)
   - [User Statistics](#user-statistics)
   - [Financial Reports](#financial-reports)
8. [Troubleshooting](#troubleshooting)
   - [Common Admin Issues](#common-admin-issues)
   - [System Maintenance](#system-maintenance)

## Introduction

Welcome to the Umlamleli Admin Guide. This document provides comprehensive instructions for administrators to effectively manage the Umlamleli loan management platform. As an administrator, you have access to powerful tools for overseeing user accounts, loan applications, system settings, and more.

This guide will walk you through the various administrative functions and best practices for maintaining the system.

## Admin Access

### Admin Login

To access the admin portal:

1. Navigate to the admin login page at `/admin/login`
2. Enter your admin username (full name) and password
3. Click "Log In"
4. For security reasons, admin sessions expire after 2 hours of inactivity

Note: Admin accounts can only be created by existing administrators or through the backend setup process.

### Admin Dashboard Overview

The admin dashboard provides a comprehensive overview of the system:

1. **Key Metrics**: View important statistics at a glance
   - Total users
   - Pending loan applications
   - Active loans
   - Overdue loans
   - Total loan amount disbursed

2. **Recent Activity**: See the latest system activities
   - New user registrations
   - Recent loan applications
   - Recent loan approvals/rejections
   - Recent payments

3. **Quick Actions**: Access common administrative tasks
   - Review pending loans
   - Manage users
   - Create notifications
   - Generate reports

4. **Navigation**: Access all admin functions through the sidebar menu

## User Management

### Viewing Users

To view and manage user accounts:

1. Navigate to "Users" in the admin sidebar
2. The user list displays:
   - User name
   - Email
   - Phone number
   - Registration date
   - Account status
   - Verification status

3. Use the search bar to find specific users by name, email, or student ID
4. Filter users by status, verification status, or registration date
5. Click on a user to view their detailed profile

### Managing User Accounts

From the user detail page, you can:

1. **View User Information**:
   - Personal details
   - Contact information
   - Verification status
   - Account activity
   - Loan history

2. **Update User Status**:
   - Activate or deactivate accounts
   - Lock accounts in case of suspicious activity
   - Reset password (generates a new default password)

3. **Verification Management**:
   - View verification status for email, phone, and face
   - Manually verify users if needed
   - Reset verification status

### User Verification

To manage user verification:

1. Navigate to the "Verification" tab in the user detail page
2. View the status of each verification method:
   - Email verification
   - Phone verification
   - Face verification

3. For face verification issues:
   - View the stored face image if available
   - Reset face verification to allow the user to try again
   - Manually approve verification in exceptional cases

## Loan Management

### Reviewing Loan Applications

To review pending loan applications:

1. Navigate to "Loans" > "Pending Applications" in the admin sidebar
2. The list shows all pending applications with:
   - Applicant name
   - Loan amount
   - Purpose
   - Application date
   - Risk assessment (if available)

3. Click on an application to view details:
   - Applicant information and verification status
   - Loan details (amount, purpose, term)
   - Calculated interest and repayment schedule
   - Applicant's loan history and repayment record

### Approving Loans

To approve a loan application:

1. Review all application details thoroughly
2. Check the applicant's verification status and loan history
3. Ensure the loan amount and term are appropriate
4. Click the "Approve" button
5. Confirm your decision in the popup dialog
6. The system will:
   - Update the loan status to "Approved"
   - Create a notification for the user
   - Move the loan to the "Approved" queue for disbursement

### Rejecting Loans

To reject a loan application:

1. Review the application and identify reasons for rejection
2. Click the "Reject" button
3. Enter the reason for rejection in the provided field
4. Confirm your decision
5. The system will:
   - Update the loan status to "Rejected"
   - Record the rejection reason
   - Create a notification for the user with the rejection reason

### Disbursing Loans

To disburse approved loans:

1. Navigate to "Loans" > "Approved Loans"
2. Select the loans to disburse
3. Click "Disburse Selected"
4. Review the disbursement details
5. Confirm the disbursement
6. The system will:
   - Update the loan status to "Disbursed"
   - Record the disbursement date
   - Calculate the due date
   - Create a notification for the user
   - Generate a transaction record

### Monitoring Active Loans

To monitor active loans:

1. Navigate to "Loans" > "Active Loans"
2. View all currently active loans with:
   - Borrower information
   - Loan amount and terms
   - Disbursement date
   - Due date
   - Repayment status
   - Overdue status

3. Filter loans by:
   - Disbursement date
   - Due date
   - Amount
   - Overdue status

4. Click on a loan to view detailed information and repayment history

## Notification Management

### Creating Notifications

To create a new notification:

1. Navigate to "Notifications" > "Create New" in the admin sidebar
2. Select the notification type:
   - System notification
   - Loan notification
   - Payment notification
   - Alert

3. Choose the recipients:
   - All users
   - Specific user(s)
   - Users with specific criteria (e.g., users with active loans)

4. Enter the notification content:
   - Title
   - Message
   - Additional details (if applicable)

5. Set delivery options:
   - Send immediately
   - Schedule for later
   - Save as draft

6. Click "Create Notification" to send or save

### Managing Notifications

To manage existing notifications:

1. Navigate to "Notifications" > "All Notifications"
2. View all notifications with:
   - Title
   - Type
   - Recipients
   - Status
   - Creation date
   - Scheduled date (if applicable)

3. Filter notifications by:
   - Type
   - Status
   - Date range
   - Recipient

4. Actions available:
   - Edit draft notifications
   - Delete draft notifications
   - View notification details
   - Check read status

### Notification Templates

To use notification templates:

1. Navigate to "Notifications" > "Templates"
2. Select a template category:
   - Loan approval
   - Loan rejection
   - Payment reminder
   - System update
   - Welcome message

3. Choose a specific template
4. Customize the template content if needed
5. Select recipients
6. Set delivery options
7. Preview and send the notification

## System Settings

### General Settings

To manage general system settings:

1. Navigate to "Settings" > "General"
2. Update company information:
   - Company name
   - Support email
   - Support phone number
   - System logo

3. Configure email settings:
   - Email templates
   - Sender information
   - SMTP settings

4. Set system preferences:
   - Date and time format
   - Default currency
   - Default language

### Loan Parameters

To configure loan parameters:

1. Navigate to "Settings" > "Loan Parameters"
2. Set interest rates:
   - Base interest rate
   - Term-specific rates
   - Special rates for specific user categories

3. Configure loan terms:
   - Minimum and maximum loan amounts
   - Available loan terms
   - Early repayment options

4. Set up late payment rules:
   - Grace period
   - Late payment fees
   - Interest rate adjustments for late payments

### Security Settings

To manage security settings:

1. Navigate to "Settings" > "Security"
2. Configure password policies:
   - Minimum password length
   - Password complexity requirements
   - Password expiration period

3. Set session policies:
   - Session timeout
   - Concurrent session limits
   - IP restrictions

4. Manage verification requirements:
   - Email verification settings
   - Phone verification settings
   - Face verification settings

## Reports and Analytics

### Loan Performance

To view loan performance reports:

1. Navigate to "Reports" > "Loan Performance"
2. View key metrics:
   - Total loan volume
   - Average loan amount
   - Approval rate
   - Disbursement rate
   - Repayment rate
   - Default rate

3. Filter data by:
   - Date range
   - Loan purpose
   - Loan amount
   - User demographics

4. Export reports in CSV or PDF format

### User Statistics

To view user statistics:

1. Navigate to "Reports" > "User Statistics"
2. View key metrics:
   - Total users
   - Active users
   - New user registrations
   - Verification rates
   - User activity

3. Filter data by:
   - Date range
   - User status
   - Verification status
   - Loan activity

4. Export reports in CSV or PDF format

### Financial Reports

To view financial reports:

1. Navigate to "Reports" > "Financial"
2. View key metrics:
   - Total disbursed amount
   - Total repaid amount
   - Outstanding balance
   - Interest earned
   - Late payment fees collected

3. Filter data by:
   - Date range
   - Loan status
   - Loan purpose
   - User demographics

4. Export reports in CSV or PDF format

## Troubleshooting

### Common Admin Issues

**User Login Problems**
- Reset user passwords if they're having trouble logging in
- Check account status to ensure the account is active
- Verify that the user's email and phone are correctly entered

**Loan Application Issues**
- If users report problems with loan applications, check the application logs
- Verify that all verification steps are working correctly
- Ensure loan parameters are correctly configured

**Notification Delivery Problems**
- Check notification logs for delivery status
- Verify that notification settings are correctly configured
- Ensure user contact information is up to date

### System Maintenance

**Regular Maintenance Tasks**
- Review and clean up old notifications
- Archive completed loans
- Check for inactive user accounts
- Monitor system performance and database size

**Backup Procedures**
- Ensure regular database backups are configured
- Verify backup integrity periodically
- Document backup restoration procedures

**Error Monitoring**
- Check system logs regularly for errors
- Address recurring issues promptly
- Report persistent problems to the development team
