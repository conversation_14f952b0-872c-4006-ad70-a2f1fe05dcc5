# Umlamleli Deployment Guide

## Table of Contents
1. [Introduction](#introduction)
2. [System Requirements](#system-requirements)
3. [Development Environment Setup](#development-environment-setup)
4. [Production Environment Setup](#production-environment-setup)
5. [Database Setup](#database-setup)
6. [Environment Configuration](#environment-configuration)
7. [Deployment Process](#deployment-process)
8. [SSL Configuration](#ssl-configuration)
9. [Monitoring and Logging](#monitoring-and-logging)
10. [Backup and Recovery](#backup-and-recovery)
11. [Troubleshooting](#troubleshooting)

## Introduction

This guide provides detailed instructions for deploying the Umlamleli loan management application in both development and production environments. It covers all necessary steps from initial setup to ongoing maintenance.

## System Requirements

### Development Environment
- **Operating System**: Windows 10/11, macOS, or Linux
- **Node.js**: v18.x or higher
- **npm**: v9.x or higher (or Yarn v1.22.x or higher)
- **PostgreSQL**: v14.x or higher
- **Git**: Latest version
- **Memory**: Minimum 8GB RAM
- **Storage**: Minimum 1GB free space (excluding database)

### Production Environment
- **Operating System**: Ubuntu 20.04 LTS or higher (recommended)
- **Node.js**: v18.x LTS
- **PostgreSQL**: v14.x or higher
- **Nginx**: Latest stable version
- **PM2**: Latest version
- **Memory**: Minimum 16GB RAM
- **CPU**: 4+ cores recommended
- **Storage**: Minimum 20GB SSD (excluding database)
- **Network**: 100Mbps connection with low latency

## Development Environment Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-organization/umlamleli.git
cd umlamleli
```

### 2. Install Dependencies

```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 3. Set Up Local Database

```bash
# Create PostgreSQL database
psql -U postgres
CREATE DATABASE umlamleli;
CREATE USER umlamleli_user WITH ENCRYPTED PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE umlamleli TO umlamleli_user;
\q
```

### 4. Configure Environment Variables

Create `.env` files in both the backend and frontend directories:

**Backend .env**
```
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=umlamleli
DB_USER=umlamleli_user
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=8h

# Server
PORT=3001
NODE_ENV=development

# Twilio (for OTP)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_VERIFY_SERVICE_SID=your_twilio_verify_service_sid
```

**Frontend .env.local**
```
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

### 5. Initialize Database

```bash
cd backend
npm run migration:run
```

### 6. Start Development Servers

```bash
# Start backend server
cd backend
npm run dev

# Start frontend server (in a new terminal)
cd frontend
npm run dev
```

The application should now be running at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001/api

## Production Environment Setup

### 1. Server Preparation

```bash
# Update system
sudo apt update
sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Install Nginx
sudo apt install -y nginx

# Install PM2
sudo npm install -g pm2
```

### 2. Create Application Directory

```bash
sudo mkdir -p /var/www/umlamleli
sudo chown -R $USER:$USER /var/www/umlamleli
```

### 3. Deploy Application Code

```bash
# Clone repository
cd /var/www/umlamleli
git clone https://github.com/your-organization/umlamleli.git .

# Install dependencies
cd backend
npm install --production

cd ../frontend
npm install
npm run build
```

## Database Setup

### 1. Create Production Database

```bash
sudo -u postgres psql

CREATE DATABASE umlamleli_prod;
CREATE USER umlamleli_prod_user WITH ENCRYPTED PASSWORD 'secure_production_password';
GRANT ALL PRIVILEGES ON DATABASE umlamleli_prod TO umlamleli_prod_user;
\q
```

### 2. Configure Database Connection

Update the backend `.env` file with production database credentials:

```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=umlamleli_prod
DB_USER=umlamleli_prod_user
DB_PASSWORD=secure_production_password
```

### 3. Run Migrations

```bash
cd /var/www/umlamleli/backend
npm run migration:run
```

## Environment Configuration

### 1. Configure Production Environment Variables

Create production environment files:

**Backend .env.production**
```
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=umlamleli_prod
DB_USER=umlamleli_prod_user
DB_PASSWORD=secure_production_password

# JWT
JWT_SECRET=long_random_secure_string
JWT_EXPIRATION=8h

# Server
PORT=3001
NODE_ENV=production

# Twilio (for OTP)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_VERIFY_SERVICE_SID=your_twilio_verify_service_sid
```

**Frontend .env.production**
```
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api
```

### 2. Set Up PM2 Configuration

Create a `ecosystem.config.js` file in the backend directory:

```javascript
module.exports = {
  apps: [{
    name: 'umlamleli-api',
    script: 'dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

## Deployment Process

### 1. Build the Application

```bash
# Build backend
cd /var/www/umlamleli/backend
npm run build

# Build frontend
cd /var/www/umlamleli/frontend
npm run build
```

### 2. Configure Nginx

Create an Nginx configuration file:

```bash
sudo nano /etc/nginx/sites-available/umlamleli
```

Add the following configuration:

```nginx
# Frontend configuration
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/umlamleli/frontend/out;
    index index.html;

    location / {
        try_files $uri $uri.html $uri/ /index.html;
    }

    # Redirect server error pages to static page
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# Backend API configuration
server {
    listen 80;
    server_name api.yourdomain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the configuration:

```bash
sudo ln -s /etc/nginx/sites-available/umlamleli /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 3. Start the Application

```bash
cd /var/www/umlamleli/backend
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## SSL Configuration

### 1. Install Certbot

```bash
sudo apt install -y certbot python3-certbot-nginx
```

### 2. Obtain SSL Certificates

```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
sudo certbot --nginx -d api.yourdomain.com
```

### 3. Configure Auto-renewal

```bash
sudo systemctl status certbot.timer
```

## Monitoring and Logging

### 1. PM2 Monitoring

```bash
# View application status
pm2 status

# Monitor logs
pm2 logs

# Monitor resources
pm2 monit
```

### 2. Nginx Logs

```bash
# Access logs
sudo tail -f /var/log/nginx/access.log

# Error logs
sudo tail -f /var/log/nginx/error.log
```

### 3. Application Logs

```bash
# View application logs
cd /var/www/umlamleli/backend
tail -f logs/app.log
```

## Backup and Recovery

### 1. Database Backup

Create a backup script:

```bash
sudo nano /usr/local/bin/backup-umlamleli-db.sh
```

Add the following content:

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/umlamleli"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/umlamleli_db_$TIMESTAMP.sql"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup
sudo -u postgres pg_dump umlamleli_prod > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Remove backups older than 30 days
find $BACKUP_DIR -type f -name "umlamleli_db_*.sql.gz" -mtime +30 -delete
```

Make the script executable:

```bash
sudo chmod +x /usr/local/bin/backup-umlamleli-db.sh
```

Set up a cron job to run daily:

```bash
sudo crontab -e
```

Add the following line:

```
0 2 * * * /usr/local/bin/backup-umlamleli-db.sh
```

### 2. Application Backup

Create a backup script for the application:

```bash
sudo nano /usr/local/bin/backup-umlamleli-app.sh
```

Add the following content:

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/umlamleli"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/umlamleli_app_$TIMESTAMP.tar.gz"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup
tar -czf $BACKUP_FILE -C /var/www umlamleli

# Remove backups older than 7 days
find $BACKUP_DIR -type f -name "umlamleli_app_*.tar.gz" -mtime +7 -delete
```

Make the script executable and set up a cron job to run weekly.

## Troubleshooting

### Common Issues

**Application Not Starting**
- Check logs: `pm2 logs`
- Verify environment variables are set correctly
- Ensure database connection is working

**Database Connection Issues**
- Verify PostgreSQL is running: `sudo systemctl status postgresql`
- Check database credentials in `.env` file
- Ensure database user has proper permissions

**Nginx Configuration Problems**
- Test configuration: `sudo nginx -t`
- Check Nginx logs: `sudo tail -f /var/log/nginx/error.log`
- Verify server blocks are correctly configured

**SSL Certificate Issues**
- Renew certificates: `sudo certbot renew --dry-run`
- Check certificate expiration: `sudo certbot certificates`
- Verify Nginx is properly configured for SSL
