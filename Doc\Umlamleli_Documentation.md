# Umlamleli Web Application Documentation

## Table of Contents
1. [Introduction](#introduction)
2. [System Requirements](#system-requirements)
   - [Software Requirements](#software-requirements)
   - [Hardware Requirements](#hardware-requirements)
3. [System Architecture](#system-architecture)
   - [Frontend Architecture](#frontend-architecture)
   - [Backend Architecture](#backend-architecture)
   - [Database Architecture](#database-architecture)
4. [User Flow](#user-flow)
   - [Authentication Flow](#authentication-flow)
   - [Loan Application Flow](#loan-application-flow)
   - [User Dashboard Flow](#user-dashboard-flow)
   - [Admin Flow](#admin-flow)
5. [Key Features](#key-features)
6. [Security Considerations](#security-considerations)
7. [Deployment Guide](#deployment-guide)
8. [Troubleshooting](#troubleshooting)

## Introduction

Umlamleli is a comprehensive loan management web application designed to streamline the loan application process, verification, and management. The application provides separate interfaces for end-users and administrators, with features including user authentication, loan application processing, face verification, payment management, and administrative controls.

This documentation provides a detailed overview of the system architecture, requirements, user flows, and other important aspects to assist developers, administrators, and stakeholders in understanding, maintaining, and extending the application.

## System Requirements

### Software Requirements

#### Development Environment
- **Node.js**: v18.17 or higher (updated minimum requirement)
- **npm**: v9.x or higher (or Yarn v1.22.x or higher)
- **Git**: For version control

#### Frontend
- **Next.js**: v15.x (App Router) (updated from v14.x)
- **React**: v19.x (updated from v18.x)
- **TypeScript**: v5.x
- **Tailwind CSS**: v3.x
- **shadcn/ui**: For UI components
- **Framer Motion**: For animations
- **face-api.js**: For facial recognition

#### Backend
- **Node.js**: v18.x
- **Express.js**: v4.x
- **TypeORM**: For database interactions
- **PostgreSQL**: v14.x or higher
- **JWT**: For authentication
- **Twilio API**: For OTP verification

#### Deployment
- **Docker**: For containerization (optional)
- **Nginx**: For reverse proxy (production)
- **PM2**: For process management (production)

### Hardware Requirements

#### Development
- **CPU**: Dual-core processor, 2.0 GHz or higher
- **RAM**: 8 GB minimum, 16 GB recommended
- **Storage**: 1 GB for application code, plus additional space for dependencies and database
- **Display**: 1366x768 resolution or higher

#### Production Server
- **CPU**: Quad-core processor, 2.5 GHz or higher
- **RAM**: 16 GB minimum, 32 GB recommended for high traffic
- **Storage**: 20 GB SSD minimum, plus space for database growth
- **Network**: 100 Mbps connection, with low latency
- **Backup**: Regular backup system for database

#### Client (End User)
- **Any modern device** with a web browser (desktop, laptop, tablet, or smartphone)
- **Camera**: Required for facial recognition features
- **Internet Connection**: Stable connection, minimum 1 Mbps

## System Architecture

### Frontend Architecture

The Umlamleli frontend is built using a component-based architecture with Next.js, following the App Router pattern. The application is structured to provide a clear separation of concerns, with reusable components, page-specific components, and utility functions organized in a logical directory structure.

#### Directory Structure
```
/app                      # Next.js App Router pages
  /admin                  # Admin interface pages
  /dashboard              # User dashboard pages
  /loan                   # Loan-related pages
  /profile                # User profile pages
  /login                  # Authentication pages
  /change-password        # Password management
  /layout.tsx             # Root layout
  /page.tsx               # Landing page
/components               # Reusable components
  /ui                     # UI components (buttons, cards, etc.)
  /admin-*                # Admin-specific components
  /auth-*                 # Authentication components
  /loan-*                 # Loan-related components
/hooks                    # Custom React hooks
/lib                      # Utility functions and API clients
/public                   # Static assets
```

#### Key Frontend Technologies
- **Next.js App Router**: For server-side rendering and routing
- **React Context**: For state management
- **Tailwind CSS**: For styling
- **shadcn/ui**: For UI components
- **face-api.js**: For facial recognition and liveness detection

### Backend Architecture

The backend follows a layered architecture with clear separation of concerns:

#### Layers
1. **Routes Layer**: Defines API endpoints and routes requests to controllers
2. **Controller Layer**: Handles request/response logic and delegates to services
3. **Service Layer**: Contains business logic and interacts with repositories
4. **Repository Layer**: Handles data access and database operations
5. **Model Layer**: Defines data structures and relationships

#### Directory Structure
```
/src
  /controllers           # Request handlers
  /services              # Business logic
  /models                # Data models
  /routes                # API routes
  /middleware            # Custom middleware
  /utils                 # Utility functions
  /config                # Configuration files
  /tasks                 # Scheduled tasks
  /data-source.ts        # Database connection
  /server.ts             # Main application entry
```

### Database Architecture

The application uses PostgreSQL with TypeORM for object-relational mapping.

#### Key Entities
1. **User**: Stores user information, credentials, and verification status
2. **Loan**: Stores loan applications, terms, and status
3. **Transaction**: Records financial transactions
4. **Notification**: Manages system and user notifications
5. **OTP**: Stores one-time passwords for verification

#### Entity Relationships
- A User can have multiple Loans (one-to-many)
- A User can have multiple Transactions (one-to-many)
- A User can have multiple Notifications (one-to-many)
- A Loan can have multiple Transactions (one-to-many)

## User Flow

### Authentication Flow

1. **Registration**
   - User navigates to the registration page
   - Fills in personal details (name, email, phone, student ID)
   - System creates account with default password
   - User receives confirmation

2. **Login**
   - User navigates to login page
   - Enters student ID/email and password
   - If first login, system prompts to change default password
   - System authenticates and redirects to dashboard

3. **Password Change**
   - User navigates to change password page
   - Enters current password and new password
   - System validates and updates credentials
   - User receives confirmation

4. **Face Verification**
   - During loan application, system prompts for face scan
   - For first-time users, system stores face descriptor
   - For returning users, system compares with stored descriptor
   - If verification passes, user proceeds to next step

### Loan Application Flow

1. **Start Application**
   - User navigates to loan application from dashboard
   - System presents multi-step form

2. **OTP Verification**
   - System sends OTP to user's phone
   - User enters OTP for verification
   - System validates OTP

3. **Face Verification**
   - System performs face recognition
   - Verifies user identity

4. **Loan Details**
   - User enters loan amount, purpose, term
   - System calculates interest and repayment schedule

5. **Review & Submit**
   - User reviews all information
   - Accepts terms and conditions
   - Submits application

6. **Confirmation**
   - System confirms submission
   - Redirects to dashboard with status

### User Dashboard Flow

1. **Overview**
   - User sees summary of active loans
   - Views recent transactions
   - Sees notifications

2. **Loan Management**
   - Views all loans and their status
   - Makes payments on active loans
   - Views payment history

3. **Profile Management**
   - Updates personal information
   - Changes password
   - Manages security settings

4. **Notifications**
   - Views all notifications
   - Marks notifications as read
   - Filters by category

### Admin Flow

1. **Dashboard**
   - Views system statistics
   - Sees pending applications
   - Monitors recent activities

2. **Loan Management**
   - Reviews loan applications
   - Approves or rejects applications
   - Disburses approved loans
   - Monitors loan repayments

3. **User Management**
   - Views all users
   - Manages user accounts
   - Handles verification issues

4. **Notification Management**
   - Creates system notifications
   - Sends targeted notifications to users
   - Schedules notifications

5. **Settings**
   - Configures system parameters
   - Manages interest rates
   - Sets up security policies

## Key Features

1. **Facial Recognition**: Secure identity verification using face-api.js
2. **OTP Verification**: Two-factor authentication via SMS
3. **Loan Management**: Complete lifecycle from application to repayment
4. **Admin Dashboard**: Comprehensive tools for loan and user management
5. **Notifications**: Real-time alerts for users and administrators
6. **Responsive Design**: Optimized for all devices from mobile to desktop

## Security Considerations

1. **Authentication**: JWT-based authentication with token expiration
2. **Password Security**: Bcrypt hashing with appropriate salt rounds
3. **Face Verification**: Secure storage of face descriptors
4. **Input Validation**: Server-side validation of all user inputs
5. **HTTPS**: Secure communication with TLS/SSL
6. **Rate Limiting**: Protection against brute force attacks
7. **Data Protection**: Proper handling of sensitive user information

## Deployment Guide

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Start development server: `npm run dev`

### Production Deployment
1. Build the application: `npm run build`
2. Set up production environment variables
3. Start the server: `npm start` or use PM2
4. Configure Nginx as a reverse proxy
5. Set up SSL certificates

## Troubleshooting

### Common Issues
1. **Authentication Failures**: Check token expiration and credentials
2. **Face Verification Issues**: Ensure proper lighting and camera access
3. **Database Connection Problems**: Verify connection strings and credentials
4. **Performance Issues**: Monitor server resources and optimize queries
5. **API Errors**: Check request formats and server logs

