# Umlamleli Loan Management System - User Installation Manual

## Table of Contents
1. [System Requirements](#system-requirements)
2. [Prerequisites Installation](#prerequisites-installation)
3. [Database Setup](#database-setup)
4. [Application Installation](#application-installation)
5. [Running the Application](#running-the-application)
6. [Verification](#verification)
7. [Troubleshooting](#troubleshooting)
8. [Default Login Credentials](#default-login-credentials)

---

## System Requirements

### Minimum Hardware Requirements
- **CPU**: Dual-core processor, 2.0 GHz or higher
- **RAM**: 8 GB minimum (16 GB recommended)
- **Storage**: 5 GB free disk space
- **Display**: 1366x768 resolution or higher
- **Network**: Internet connection for initial setup and dependencies

### Supported Operating Systems
- **Windows**: Windows 10 or Windows 11
- **macOS**: macOS 10.15 (Catalina) or later
- **Linux**: Ubuntu 18.04 LTS or later, CentOS 7+, or equivalent distributions

---

## Prerequisites Installation

### 1. Install Node.js (Required)

**For Windows:**
1. Download Node.js v18.x LTS from: https://nodejs.org/
2. Run the installer (.msi file)
3. Follow the installation wizard
4. Restart your computer

**For macOS:**
1. Download Node.js v18.x LTS from: https://nodejs.org/
2. Run the installer (.pkg file)
3. Follow the installation wizard

**For Linux (Ubuntu/Debian):**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

**Verify Installation:**
Open Command Prompt/Terminal and run:
```bash
node --version
npm --version
```
You should see version numbers (e.g., v18.17.0 and 9.6.7).

### 2. Install PostgreSQL Database (Required)

**For Windows:**
1. Download PostgreSQL 14+ from: https://www.postgresql.org/download/windows/
2. Run the installer
3. During installation:
   - Set password for 'postgres' user (remember this password!)
   - Use default port 5432
   - Select default locale
4. Complete the installation

**For macOS:**
1. Download PostgreSQL from: https://www.postgresql.org/download/macosx/
2. Or use Homebrew: `brew install postgresql@14`
3. Start PostgreSQL service

**For Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

**Verify PostgreSQL Installation:**
```bash
psql --version
```

### 3. Install Git (Optional but Recommended)
Download from: https://git-scm.com/downloads

---

## Database Setup

### 1. Create Database and User

**Windows/macOS:**
1. Open Command Prompt/Terminal
2. Connect to PostgreSQL:
```bash
psql -U postgres
```
3. Enter the password you set during PostgreSQL installation

**Linux:**
```bash
sudo -u postgres psql
```

### 2. Execute Database Commands
Once connected to PostgreSQL, run these commands:

```sql
-- Create the database
CREATE DATABASE loan_app;

-- Create a user for the application
CREATE USER loan_app_user WITH ENCRYPTED PASSWORD 'loan_app_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE loan_app TO loan_app_user;

-- Exit PostgreSQL
\q
```

### 3. Import Database Schema
1. Navigate to the application folder on the disc
2. Find the `database` folder
3. Import the database schema:

**Windows:**
```cmd
cd "D:\path\to\disc\database"
psql -U loan_app_user -d loan_app -f "Umlamleli_loan_app_DB(final).sql"
```

**macOS/Linux:**
```bash
cd /path/to/disc/database
psql -U loan_app_user -d loan_app -f "Umlamleli_loan_app_DB(final).sql"
```

Enter the password: `loan_app_password`

---

## Application Installation

### 1. Copy Application Files
1. Insert the application disc
2. Copy the entire application folder to your computer:
   - **Windows**: Copy to `C:\Umlamleli\`
   - **macOS**: Copy to `/Applications/Umlamleli/`
   - **Linux**: Copy to `/opt/umlamleli/` or your preferred location

### 2. Install Application Dependencies

Open Command Prompt/Terminal and navigate to the application directory:

**Windows:**
```cmd
cd C:\Umlamleli
```

**macOS/Linux:**
```bash
cd /Applications/Umlamleli  # or your chosen path
```

Install frontend dependencies:
```bash
npm install
```

Install backend dependencies:
```bash
cd backend
npm install
cd ..
```

### 3. Configure Environment Variables

**Backend Configuration:**
1. Navigate to the `backend` folder
2. Create a file named `.env` (note the dot at the beginning)
3. Add the following content:

```env
# Server Configuration
PORT=3001
NODE_ENV=production

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=loan_app_user
DB_PASSWORD=loan_app_password
DB_NAME=loan_app

# JWT Configuration
JWT_SECRET=your_secure_jwt_secret_key_here_make_it_long_and_random
JWT_EXPIRES_IN=24h

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_DIR=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
```

**Frontend Configuration:**
1. In the main application folder, create a file named `.env.local`
2. Add the following content:

```env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

### 4. Build the Application

Build the frontend:
```bash
npm run build
```

Build the backend:
```bash
cd backend
npm run build
cd ..
```

---

## Running the Application

### 1. Start the Backend Server
Open a Command Prompt/Terminal window and run:

**Windows:**
```cmd
cd C:\Umlamleli\backend
npm start
```

**macOS/Linux:**
```bash
cd /Applications/Umlamleli/backend  # or your path
npm start
```

You should see: "Server is running on port 3001" and "Database connection established"

### 2. Start the Frontend Server
Open a **new** Command Prompt/Terminal window and run:

**Windows:**
```cmd
cd C:\Umlamleli
npm start
```

**macOS/Linux:**
```bash
cd /Applications/Umlamleli  # or your path
npm start
```

### 3. Access the Application
1. Open your web browser
2. Navigate to: `http://localhost:3000`
3. The Umlamleli login page should appear

---

## Verification

### Test the Installation
1. **Homepage Loading**: Verify the application loads without errors
2. **Database Connection**: Try to register a new user account
3. **Face Recognition**: Test the face verification feature (requires webcam)
4. **Admin Access**: Login with admin credentials (see below)

### Check System Status
- Backend API should be accessible at: `http://localhost:3001/api`
- Frontend should be running at: `http://localhost:3000`
- Database should be accepting connections on port 5432

---

## Default Login Credentials

### Admin Account
- **Username**: Razan
- **Password**: admin123
- **Role**: Administrator


---

## Troubleshooting

### Common Issues and Solutions

#### 1. "Database connection failed"
**Solution:**
- Verify PostgreSQL is running
- Check database credentials in `.env` file
- Ensure database `loan_app` exists
- Test connection: `psql -U loan_app_user -d loan_app`

#### 2. "Port 3000 already in use"
**Solution:**
- Close other applications using port 3000
- Or change the port in package.json: `"start": "next start -p 3001"`

#### 3. "Node.js command not found"
**Solution:**
- Restart Command Prompt/Terminal after Node.js installation
- Add Node.js to system PATH manually
- Reinstall Node.js with "Add to PATH" option checked

#### 4. Face recognition not working
**Solution:**
- Allow camera access in browser
- Ensure good lighting
- Use Chrome or Firefox browser
- Check if webcam is working in other applications

#### 5. "npm install" fails
**Solution:**
- Clear npm cache: `npm cache clean --force`
- Delete `node_modules` folder and `package-lock.json`
- Run `npm install` again
- Check internet connection

#### 6. Application loads but shows errors
**Solution:**
- Check browser console for JavaScript errors (F12)
- Verify both frontend and backend are running
- Check `.env` files for correct configuration
- Restart both servers

### Getting Help
If you encounter issues not covered here:
1. Check the browser console (F12) for error messages
2. Check the terminal/command prompt for server error messages
3. Verify all prerequisites are correctly installed
4. Contact technical support with specific error messages

---

## Performance Tips

1. **Close Unused Applications**: Free up RAM for better performance
2. **SSD Storage**: Install on SSD for faster loading times
3. **Regular Maintenance**: Clear browser cache periodically
4. **Monitor Resources**: Use Task Manager to monitor CPU/RAM usage

---

## Advanced Configuration (Optional)

### Custom Port Configuration
If you need to use different ports:

1. **Backend Port**: Edit `backend/.env` file:
```env
PORT=3002  # Change from 3001 to desired port
```

2. **Frontend Port**: Edit `.env.local` file:
```env
NEXT_PUBLIC_API_URL=http://localhost:3002/api  # Match backend port
```

3. **Start with custom frontend port**:
```bash
npm start -- -p 3001  # Runs frontend on port 3001
```

### Database Configuration for Different Systems

#### Using Different Database Credentials
Edit `backend/.env` file:
```env
DB_HOST=your_database_host
DB_PORT=5432
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_NAME=your_database_name
```

#### Using Remote Database
If your PostgreSQL is on a different machine:
```env
DB_HOST=*************  # IP address of database server
DB_PORT=5432
# ... other settings
```

### File Upload Configuration
Adjust file upload limits in `backend/.env`:
```env
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads    # Directory for uploaded files
```

---


### Application Updates
When updating the application:
1. Stop both frontend and backend servers
2. Backup your database
3. Copy new application files
4. Run `npm install` in both root and backend directories
5. Run `npm run build` for both frontend and backend
6. Restart the servers

### Log Files
Monitor application logs for issues:
- Backend logs: Check terminal where backend is running
- Frontend logs: Check browser console (F12)
- Database logs: Check PostgreSQL log files

---

## Uninstallation

### Complete Removal
To completely remove the application:

1. **Stop all services**:
   - Close Command Prompt/Terminal windows running the application
   - Stop PostgreSQL service if not needed for other applications

2. **Remove database** (if not needed):
```sql
psql -U postgres
DROP DATABASE loan_app;
DROP USER loan_app_user;
\q
```

3. **Remove application files**:
   - Delete the application folder (e.g., `C:\Umlamleli\`)

4. **Remove Node.js** (if not needed for other applications):
   - Windows: Use "Add or Remove Programs"
   - macOS: Use the Node.js uninstaller or manually remove
   - Linux: `sudo apt remove nodejs npm`

5. **Remove PostgreSQL** (if not needed):
   - Windows: Use "Add or Remove Programs"
   - macOS: Use Homebrew `brew uninstall postgresql` or manual removal
   - Linux: `sudo apt remove postgresql postgresql-contrib`

---

## Appendix A: Required Software Download Links

### Essential Downloads
1. **Node.js v18.x LTS**: https://nodejs.org/en/download/
2. **PostgreSQL 14+**: https://www.postgresql.org/download/
3. **Git**: https://git-scm.com/downloads (optional)

### Browser Recommendations
- **Google Chrome**: https://www.google.com/chrome/
- **Mozilla Firefox**: https://www.mozilla.org/firefox/
- **Microsoft Edge**: Pre-installed on Windows 10/11

### Text Editors (for configuration files)
- **Notepad++** (Windows): https://notepad-plus-plus.org/
- **Visual Studio Code**: https://code.visualstudio.com/
- **Sublime Text**: https://www.sublimetext.com/

---

## Appendix B: System Architecture

### Application Components
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Next.js)     │◄──►│   (Node.js)     │◄──►│  (PostgreSQL)   │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Features
- **User Authentication**: JWT-based authentication with face verification
- **Loan Management**: Apply for loans, track status, make payments
- **Admin Dashboard**: Manage users, loans, and system settings
- **Document Upload**: Secure document storage and verification
- **Mobile Money Integration**: Payment processing capabilities
- **Real-time Notifications**: System alerts and updates

---

## Appendix C: Frequently Asked Questions

### Q: Can I install this on multiple computers?
A: Yes, you can install the application on multiple computers, but each installation will need its own database setup.

### Q: Do I need an internet connection to run the application?
A: After initial installation, the application can run offline. However, some features like mobile money integration require internet connectivity.

### Q: Can I change the database password after installation?
A: Yes, but you'll need to update the password in both PostgreSQL and the application's `.env` file.

### Q: What browsers are supported?
A: Modern browsers including Chrome, Firefox, Safari, and Edge. Chrome is recommended for best face recognition performance.

### Q: Can I run this on a server for multiple users?
A: Yes, but additional configuration for production deployment is required. Consult the technical documentation for server deployment.

### Q: How do I update the application?
A: Follow the "Application Updates" section in the Backup and Maintenance chapter.

---

*This manual covers installation for local/development use. For production deployment on a server, additional security and performance configurations are required.*

**Version**: 1.0
**Last Updated**: June 2024
**Application**: Umlamleli Loan Management System
