"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Upload, MoreHorizontal, FileText, FilePlus, Download, Eye, Trash2, Loader2, AlertCircle, CheckCircle, XCircle, Clock } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>oot<PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "sonner"
import { documentApi } from "@/lib/api"
import DocumentUploadModal from "@/components/document-upload-modal"

interface Document {
  id: string
  userId: string
  userName: string
  loanId?: string
  documentType: "contract" | "id" | "collateral" | "statement" | "other"
  fileName: string
  fileSize: string
  mimeType: string
  documentStatus: "pending" | "approved" | "rejected" | "expired"
  uploadedBy: string
  uploadDate: string
  statusUpdatedAt?: string
  statusUpdatedBy?: string
  rejectionReason?: string
}

export default function DocumentsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null)

  // Fetch documents from API
  const fetchDocuments = async () => {
    try {
      setLoading(true)
      const response = await documentApi.getAllDocuments()
      if (response.success) {
        setDocuments(response.data)
      } else {
        toast.error("Failed to load documents")
      }
    } catch (error) {
      console.error('Error fetching documents:', error)
      toast.error("Failed to load documents", {
        description: error instanceof Error ? error.message : "Unknown error"
      })
    } finally {
      setLoading(false)
    }
  }

  // Load documents on component mount
  useEffect(() => {
    fetchDocuments()
  }, [])

  // Handle document deletion
  const handleDeleteDocument = async (documentId: string) => {
    try {
      await documentApi.deleteDocument(documentId)
      toast.success("Document deleted successfully")
      fetchDocuments() // Refresh the list
    } catch (error) {
      console.error('Error deleting document:', error)
      toast.error("Failed to delete document", {
        description: error instanceof Error ? error.message : "Unknown error"
      })
    } finally {
      setDeleteDialogOpen(false)
      setDocumentToDelete(null)
    }
  }

  // Handle document download
  const handleDownloadDocument = async (documentId: string, fileName: string) => {
    try {
      const { blob, filename } = await documentApi.downloadDocument(documentId)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename || fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success("Document downloaded successfully")
    } catch (error) {
      console.error('Error downloading document:', error)
      toast.error("Failed to download document", {
        description: error instanceof Error ? error.message : "Unknown error"
      })
    }
  }

  // Handle document status update
  const handleUpdateDocumentStatus = async (documentId: string, status: string, rejectionReason?: string) => {
    try {
      await documentApi.updateDocumentStatus(documentId, status, rejectionReason)
      toast.success(`Document ${status} successfully`)
      fetchDocuments() // Refresh the list
    } catch (error) {
      console.error('Error updating document status:', error)
      toast.error("Failed to update document status", {
        description: error instanceof Error ? error.message : "Unknown error"
      })
    }
  }

  const filteredDocuments = documents
    .filter(
      (doc) =>
        doc.fileName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (doc.loanId && doc.loanId.toLowerCase().includes(searchQuery.toLowerCase())),
    )
    .filter((doc) => {
      if (activeTab === "all") return true
      return doc.documentType === activeTab
    })

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "contract":
        return <FileText className="h-4 w-4 text-blue-600" />
      case "id":
        return <FileText className="h-4 w-4 text-green-600" />
      case "collateral":
        return <FileText className="h-4 w-4 text-purple-600" />
      case "statement":
        return <FileText className="h-4 w-4 text-amber-600" />
      default:
        return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "contract":
        return <Badge className="bg-blue-500">Contract</Badge>
      case "id":
        return <Badge className="bg-green-500">ID Document</Badge>
      case "collateral":
        return <Badge className="bg-purple-500">Collateral</Badge>
      case "statement":
        return <Badge className="bg-amber-500">Statement</Badge>
      default:
        return <Badge className="bg-gray-500">Other</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="text-amber-600 border-amber-600"><Clock className="h-3 w-3 mr-1" />Pending</Badge>
      case "approved":
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case "rejected":
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      case "expired":
        return <Badge variant="outline" className="text-gray-600 border-gray-600"><AlertCircle className="h-3 w-3 mr-1" />Expired</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Documents</h1>
        <Button
          className="bg-blue-600 hover:bg-blue-700"
          onClick={() => setIsUploadModalOpen(true)}
        >
          <FilePlus className="mr-2 h-4 w-4" />
          Upload Document
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Document Management</CardTitle>
          <CardDescription>View and manage all documents in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
              <TabsList className="grid grid-cols-5 w-full md:w-auto">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="contract">Contracts</TabsTrigger>
                <TabsTrigger value="id">ID Documents</TabsTrigger>
                <TabsTrigger value="collateral">Collateral</TabsTrigger>
                <TabsTrigger value="statement">Statements</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex gap-4 w-full md:w-auto">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search documents..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                Upload
              </Button>
            </div>
          </div>

          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Document ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Related To</TableHead>
                  <TableHead>Uploaded By</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDocuments.length > 0 ? (
                  filteredDocuments.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell className="font-medium">{doc.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTypeIcon(doc.documentType)}
                          <span>{doc.fileName}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getTypeBadge(doc.documentType)}</TableCell>
                      <TableCell>{getStatusBadge(doc.documentStatus)}</TableCell>
                      <TableCell>{doc.loanId || doc.userName}</TableCell>
                      <TableCell>{doc.uploadedBy}</TableCell>
                      <TableCell>{new Date(doc.uploadDate).toLocaleString()}</TableCell>
                      <TableCell>{doc.fileSize}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleDownloadDocument(doc.id, doc.fileName)}>
                              <Download className="h-4 w-4 mr-2" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {doc.documentStatus === 'pending' && (
                              <>
                                <DropdownMenuItem onClick={() => handleUpdateDocumentStatus(doc.id, 'approved')}>
                                  <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                                  Approve
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleUpdateDocumentStatus(doc.id, 'rejected', 'Document does not meet requirements')}>
                                  <XCircle className="h-4 w-4 mr-2 text-red-600" />
                                  Reject
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                              </>
                            )}
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => {
                                setDocumentToDelete(doc.id)
                                setDeleteDialogOpen(true)
                              }}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-6 text-muted-foreground">
                      {loading ? "Loading documents..." : "No documents found matching your search criteria"}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Upload Modal */}
      <DocumentUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUploadSuccess={fetchDocuments}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this document? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setDeleteDialogOpen(false)
              setDocumentToDelete(null)
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (documentToDelete) {
                  handleDeleteDocument(documentToDelete)
                }
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-muted-foreground">Loading documents...</span>
        </div>
      )}
    </div>
  )
}

