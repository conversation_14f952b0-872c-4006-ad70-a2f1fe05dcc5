"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { AdminAnimatedSidebar } from "@/components/admin-animated-sidebar"
import { AdminHeader } from "@/components/admin-header"
import { adminApi } from "@/lib/admin-api"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const pathname = usePathname()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkAuthentication = () => {
      try {
        // Check for admin token first (API-based auth)
        const adminToken = localStorage.getItem("adminToken")
        const adminUser = localStorage.getItem("adminUser")

        // Fallback to simple flag-based auth
        const adminAuthenticated = localStorage.getItem("adminAuthenticated") === "true"

        // User is authenticated if they have a token and user data, or if the flag is set
        const isAuth = !!(adminToken && adminUser) || adminAuthenticated

        setIsAuthenticated(isAuth)

        // If not authenticated and not on login or signup page, redirect to login
        if (!isAuth && pathname !== "/admin/login" && pathname !== "/admin/signup") {
          router.push("/admin/login")
        }

        // If authenticated, check if the user is actually an admin
        if (isAuth && adminUser) {
          try {
            const user = JSON.parse(adminUser)
            if (user.role !== "admin") {
              console.error("User is not an admin:", user.role)
              // Clear auth data and redirect to login
              adminApi.logout()
              localStorage.removeItem("adminAuthenticated")
              router.push("/admin/login")
              return
            }
          } catch (parseError) {
            console.error("Error parsing admin user data:", parseError)
          }
        }
      } catch (error) {
        console.error("Error checking admin authentication:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthentication()

    // TEMPORARILY DISABLED: Redirect away from Reports and Documents pages
    //if (pathname.includes("/admin/reports") || pathname.includes("/admin/documents")) {
     // router.push("/admin/dashboard")
    //}
  }, [pathname, router])

  // Don't apply layout to login or signup page
  if (pathname === "/admin/login" || pathname === "/admin/signup" || isLoading) {
    return <>{children}</>
  }

  // If not authenticated, don't render anything (will redirect)
  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <AdminHeader />
      <div className="flex">
        <AdminAnimatedSidebar />
        <main className="flex-1 p-6 overflow-auto">{children}</main>
      </div>
    </div>
  )
}

