"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Download, MoreHorizontal, Filter, Loader2, Pencil, Trash2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { adminApi } from "@/lib/admin-api"
import { toast } from "sonner"
import { ConfirmationDialog } from "@/components/dialogs/confirmation-dialog"
import { EditLoanForm } from "@/components/forms/edit-loan-form"

interface Loan {
  id: string
  userId: string
  userName: string
  userPhoneNumber?: string // Add phone number field
  amount: number
  purpose: string
  status: "approved" | "pending" | "rejected" | "disbursed" | "paid"
  applicationDate: string
  approvalDate?: string
  dueDate?: string
  interestRate: number
  principalAmount: number
  totalPayment: number
  amountDue: number // Keeping for backward compatibility
  isPaid: boolean
  paidDate?: string
}

export default function LoansPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [loans, setLoans] = useState<Loan[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isActionLoading, setIsActionLoading] = useState(false)
  const [statusFilter, setStatusFilter] = useState("")

  // State for CRUD operations
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteLoading, setIsDeleteLoading] = useState(false)

  // Fetch loans data
  useEffect(() => {
    const fetchLoans = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await adminApi.getAllLoans(currentPage, 10, searchQuery, statusFilter);

        if (response.success) {
          // Log the loan data to check if totalPayment is correctly calculated
          console.log('Loan data from API:', response.data);

          // Ensure totalPayment is correctly calculated for each loan
          const processedLoans = response.data.map((loan: Loan) => {
            // If totalPayment is missing or equal to principalAmount, recalculate it
            if (!loan.totalPayment || loan.totalPayment === loan.principalAmount) {
              const interestAmount = (loan.amount * loan.interestRate) / 100;
              return {
                ...loan,
                principalAmount: loan.amount,
                totalPayment: loan.amount + interestAmount
              };
            }
            return loan;
          });

          setLoans(processedLoans);
          setTotalPages(response.pagination?.pages || 1);
        } else {
          setError('Failed to fetch loans');
          toast.error('Failed to fetch loans');
          // Fallback to empty array
          setLoans([]);
        }
      } catch (err) {
        console.error('Error fetching loans:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching loans');
        toast.error('Error loading loans');
        // Keep empty array as fallback
        setLoans([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLoans();
  }, [currentPage, searchQuery, statusFilter]);

  // Handle loan actions
  const handleLoanAction = async (loanId: string, action: 'approve' | 'reject' | 'disburse' | 'markPaid') => {
    try {
      setIsActionLoading(true);

      let response;

      switch (action) {
        case 'approve':
          response = await adminApi.approveLoan(loanId);
          break;
        case 'reject':
          response = await adminApi.rejectLoan(loanId);
          break;
        case 'disburse':
          response = await adminApi.disburseLoan(loanId);
          break;
        case 'markPaid':
          // This would need to be implemented in the backend
          toast.error('Mark as paid functionality not implemented yet');
          return;
      }

      if (response && response.success) {
        toast.success(`Loan ${action}d successfully`);
        // Refresh loans
        const updatedResponse = await adminApi.getAllLoans(currentPage, 10, searchQuery, statusFilter);
        if (updatedResponse.success) {
          setLoans(updatedResponse.data);
        }
      } else {
        toast.error(response?.message || `Failed to ${action} loan`);
      }
    } catch (err) {
      console.error(`Error ${action}ing loan:`, err);
      toast.error(`Error ${action}ing loan`);
    } finally {
      setIsActionLoading(false);
    }
  };

  // Handle export report
  const handleExportReport = () => {
    toast.info('Export functionality coming soon');
  };

  // Handle edit loan
  const handleEditLoan = (loan: Loan) => {
    setSelectedLoan(loan);
    setIsEditDialogOpen(true);
  };

  // Handle delete loan
  const handleDeleteLoan = (loan: Loan) => {
    setSelectedLoan(loan);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete loan
  const confirmDeleteLoan = async () => {
    if (!selectedLoan) return;

    try {
      setIsDeleteLoading(true);

      // Call the deleteLoan API function with error handling
      const response = await adminApi.deleteLoan(selectedLoan.id);

      if (response.success) {
        toast.success(response.message || "Loan deleted successfully");

        // Remove the deleted loan from the state or mark it as rejected
        // depending on the backend implementation
        if (response.data && response.data.status === "rejected") {
          // If using the fallback method (marking as rejected), update the loan status
          setLoans(loans.map(loan =>
            loan.id === selectedLoan.id
              ? { ...loan, status: "rejected" }
              : loan
          ));

          toast.info("Note: The loan was marked as rejected since a delete endpoint is not available");
        } else {
          // If using the actual delete endpoint, remove the loan from the list
          setLoans(loans.filter(loan => loan.id !== selectedLoan.id));
        }

        // Close the dialog
        setIsDeleteDialogOpen(false);
      } else {
        toast.error(response.message || "Failed to delete loan");
      }
    } catch (error: any) {
      console.error("Error deleting loan:", error);

      // Show a more detailed error message
      const errorMessage = error.message || "An error occurred while deleting the loan";
      toast.error(errorMessage);

      if (errorMessage.includes("404")) {
        toast.error("The delete endpoint doesn't exist. Please contact the backend team to implement it.");
      }
    } finally {
      setIsDeleteLoading(false);
    }
  };

  // Sample loans data for fallback (will be replaced by real data)
  const fallbackLoans: Loan[] = [
    {
      id: "LOAN-123456",
      userId: "USR-001",
      userName: "John Doe",
      userPhoneNumber: "+268 7612 3456",
      amount: 2500,
      purpose: "Business Investment",
      status: "disbursed",
      applicationDate: "2025-02-15",
      approvalDate: "2025-02-16",
      dueDate: "2025-03-15",
      interestRate: 10,
      principalAmount: 2500,
      totalPayment: 2750,
      amountDue: 2750,
      isPaid: false,
      paidDate: undefined,
    },
    {
      id: "LOAN-789012",
      userId: "USR-002",
      userName: "Sarah Smith",
      userPhoneNumber: "+268 7698 7654",
      amount: 1000,
      purpose: "Personal Expenses",
      status: "paid",
      applicationDate: "2025-01-10",
      approvalDate: "2025-01-11",
      dueDate: "2025-02-10",
      interestRate: 10,
      principalAmount: 1000,
      totalPayment: 1100,
      amountDue: 1100,
      isPaid: true,
      paidDate: "2025-02-08",
    },
    {
      id: "LOAN-345678",
      userId: "USR-005",
      userName: "David Wilson",
      userPhoneNumber: "+268 7634 5678",
      amount: 5000,
      purpose: "Home Improvement",
      status: "approved",
      applicationDate: "2025-03-12",
      approvalDate: "2025-03-13",
      dueDate: "2025-04-12",
      interestRate: 10,
      principalAmount: 5000,
      totalPayment: 5500,
      amountDue: 5500,
      isPaid: false,
      paidDate: undefined,
    },
    {
      id: "LOAN-901234",
      userId: "USR-003",
      userName: "Michael Johnson",
      userPhoneNumber: "+268 7690 1234",
      amount: 1500,
      purpose: "Medical Expenses",
      status: "pending",
      applicationDate: "2025-03-14",
      approvalDate: undefined,
      dueDate: undefined,
      interestRate: 10,
      principalAmount: 1500,
      totalPayment: 1650,
      amountDue: 1650,
      isPaid: false,
      paidDate: undefined,
    },
    {
      id: "LOAN-567890",
      userId: "USR-004",
      userName: "Emily Brown",
      userPhoneNumber: undefined, // Test case for missing phone number
      amount: 3000,
      purpose: "Education",
      status: "rejected",
      applicationDate: "2025-03-08",
      approvalDate: undefined,
      dueDate: undefined,
      interestRate: 0,
      principalAmount: 3000,
      totalPayment: 3000,
      amountDue: 0,
      isPaid: false,
      paidDate: undefined,
    },
  ]

  // Use real data or fallback to empty array
  const displayLoans = loans.length > 0 ? loans : (isLoading ? [] : [])

  // Format currency values with Emalangeni symbol and exactly two decimal places
  const formatCurrency = (amount: any): string => {
    // Check if amount is a valid number
    if (amount === null || amount === undefined) {
      console.warn('Received null or undefined amount for formatting');
      return 'E0.00';
    }

    // Log the amount for debugging
    console.log('Formatting amount:', amount, 'Type:', typeof amount);

    // Convert to number if it's a string or other type
    const numericAmount = typeof amount === 'number' ? amount : Number(amount);

    // Check if conversion resulted in a valid number
    if (isNaN(numericAmount)) {
      console.warn('Failed to convert amount to number:', amount);
      return 'E0.00';
    }

    // Format with 2 decimal places
    return `E${numericAmount.toFixed(2)}`;
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-500">Approved</Badge>
      case "pending":
        return <Badge className="bg-amber-500">Pending</Badge>
      case "rejected":
        return <Badge className="bg-red-500">Rejected</Badge>
      case "disbursed":
        return <Badge className="bg-blue-500">Disbursed</Badge>
      case "paid":
        return <Badge className="bg-purple-500">Paid</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Loans</h1>
        <Button
          className="bg-blue-600 hover:bg-blue-700"
          onClick={handleExportReport}
          disabled={isLoading || loans.length === 0}
        >
          <Download className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Loan Management</CardTitle>
          <CardDescription>View and manage all loan applications and active loans</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search loans..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div className="flex gap-2">
              <select
                className="border rounded-md px-3 py-2 text-sm"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                disabled={isLoading}
                aria-label="Filter loans by status"
              >
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="disbursed">Disbursed</option>
                <option value="paid">Paid</option>
              </select>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('');
                }}
                disabled={isLoading || (!searchQuery && !statusFilter)}
              >
                <Filter className="mr-2 h-4 w-4" />
                Clear Filters
              </Button>
            </div>
          </div>

          {isLoading && (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-lg">Loading loans...</span>
            </div>
          )}

          {!isLoading && error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
              <p className="font-medium">Error loading loans</p>
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Loan ID</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Phone Number</TableHead>
                  <TableHead>Purpose</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Application Date</TableHead>
                  <TableHead>Approval Date</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Interest Rate</TableHead>
                  <TableHead>Principal Amount</TableHead>
                  <TableHead>Total Payment</TableHead>
                  <TableHead>Paid</TableHead>
                  <TableHead>Paid Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {!isLoading && displayLoans.length > 0 ? (
                  displayLoans.map((loan) => (
                    <TableRow key={loan.id}>
                      <TableCell className="font-medium">{loan.id}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{loan.userName}</span>
                          <span className="text-xs text-muted-foreground">{loan.userId}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm">
                          {loan.userPhoneNumber || "Not provided"}
                        </span>
                      </TableCell>
                      <TableCell>{loan.purpose}</TableCell>
                      <TableCell>{getStatusBadge(loan.status)}</TableCell>
                      <TableCell>{new Date(loan.applicationDate).toLocaleDateString()}</TableCell>
                      <TableCell>
                        {loan.approvalDate ? new Date(loan.approvalDate).toLocaleDateString() : "-"}
                      </TableCell>
                      <TableCell>{loan.dueDate ? new Date(loan.dueDate).toLocaleDateString() : "-"}</TableCell>
                      <TableCell>{loan.interestRate}%</TableCell>
                      <TableCell>{formatCurrency(loan.principalAmount)}</TableCell>
                      <TableCell>
                        {(() => {
                          // Calculate the correct total payment directly in the render
                          const interestAmount = (loan.amount * loan.interestRate) / 100;
                          const calculatedTotal = loan.amount + interestAmount;

                          // Use the calculated value if totalPayment is missing or incorrect
                          const displayValue = (!loan.totalPayment || loan.totalPayment === loan.principalAmount)
                            ? calculatedTotal
                            : loan.totalPayment;

                          return formatCurrency(displayValue);
                        })()}
                      </TableCell>
                      <TableCell>{loan.isPaid ? "Yes" : "No"}</TableCell>
                      <TableCell>{loan.paidDate ? new Date(loan.paidDate).toLocaleDateString() : "-"}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" disabled={isActionLoading}>
                              {isActionLoading ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <MoreHorizontal className="h-4 w-4" />
                              )}
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => toast.info(`View details for loan ${loan.id} - Coming soon`)}>View Details</DropdownMenuItem>
                            {loan.status === "pending" && (
                              <>
                                <DropdownMenuItem
                                  className="text-green-600"
                                  onClick={() => handleLoanAction(loan.id, 'approve')}
                                >
                                  Approve
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => handleLoanAction(loan.id, 'reject')}
                                >
                                  Reject
                                </DropdownMenuItem>
                              </>
                            )}
                            {loan.status === "approved" && (
                              <DropdownMenuItem
                                className="text-blue-600"
                                onClick={() => handleLoanAction(loan.id, 'disburse')}
                              >
                                Disburse
                              </DropdownMenuItem>
                            )}
                            {loan.status === "disbursed" && !loan.isPaid && (
                              <DropdownMenuItem
                                className="text-purple-600"
                                onClick={() => handleLoanAction(loan.id, 'markPaid')}
                              >
                                Mark as Paid
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleEditLoan(loan)}
                              className="text-blue-600"
                            >
                              <Pencil className="mr-2 h-4 w-4" />
                              Edit Loan
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteLoan(loan)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Loan
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => toast.info('Download contract - Coming soon')}>Download Contract</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : !isLoading && (
                  <TableRow>
                    <TableCell colSpan={14} className="text-center py-6 text-muted-foreground">
                      {error ? 'Error loading loans' : 'No loans found matching your search criteria'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {!isLoading && displayLoans.length > 0 && totalPages > 1 && (
            <div className="flex justify-center items-center mt-6 space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1 || isLoading || isActionLoading}
              >
                Previous
              </Button>

              <div className="text-sm">
                Page {currentPage} of {totalPages}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || isLoading || isActionLoading}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={confirmDeleteLoan}
        title="Delete Loan"
        description={`Are you sure you want to delete the loan for ${selectedLoan?.userName || 'this user'}? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        isLoading={isDeleteLoading}
      />

      {/* Edit Loan Form */}
      <EditLoanForm
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        loan={selectedLoan}
        onSuccess={() => {
          // Refresh loans after successful edit
          const fetchLoans = async () => {
            try {
              const response = await adminApi.getAllLoans(currentPage, 10, searchQuery, statusFilter);
              if (response.success) {
                setLoans(response.data);
              }
            } catch (error) {
              console.error('Error refreshing loans:', error);
            }
          };
          fetchLoans();
        }}
      />
    </div>
  )
}

