"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AdminAuthNavbar } from "@/components/admin-auth-navbar"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { AnimatedButton, LabelInputContainer } from "@/components/ui/animated-button"
import { motion } from "framer-motion"
import { adminApi } from "@/lib/admin-api"

export default function AdminLoginPage() {
  const router = useRouter()
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [formState, setFormState] = useState<"idle" | "error" | "success">("idle")

  useEffect(() => {
    if (formState === "error") {
      const timer = setTimeout(() => setFormState("idle"), 600)
      return () => clearTimeout(timer)
    }
    if (formState === "success") {
      const timer = setTimeout(() => setFormState("idle"), 1500)
      return () => clearTimeout(timer)
    }
  }, [formState])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      // Validate form inputs
      if (!username) {
        setError("Please enter your username")
        setFormState("error")
        setIsLoading(false)
        return
      }

      if (!password) {
        setError("Please enter your password")
        setFormState("error")
        setIsLoading(false)
        return
      }

      if (password.length < 8) {
        setError("Password must be at least 8 characters long")
        setFormState("error")
        setIsLoading(false)
        return
      }

      // Call the admin login API
      try {
        console.log('Attempting admin login with API...')
        const response = await adminApi.login(username, password)

        if (response.success) {
          console.log('Admin login successful')
          setFormState("success")

          // adminApi.login already stores the token and user in localStorage
          // Wait a moment before redirecting
          setTimeout(() => {
            router.push("/admin/dashboard")
          }, 1000)
        } else {
          console.error('Admin login failed:', response.message)
          setError(response.message || "Invalid admin credentials")
          setFormState("error")
        }
      } catch (apiError: any) {
        console.error('Admin API error:', apiError)

        // If API fails, fall back to hardcoded credentials for development
        if (process.env.NODE_ENV === 'development' && username === "admin" && password === "admin123") {
          console.log('Falling back to hardcoded admin credentials in development mode')
          setFormState("success")
          localStorage.setItem("adminAuthenticated", "true")
          setTimeout(() => {
            router.push("/admin/dashboard")
          }, 1000)
        } else {
          setError(apiError.message || "Authentication failed. Please try again.")
          setFormState("error")
        }
      }
    } catch (err: any) {
      console.error('Unexpected error during login:', err)
      setError(err.message || "Authentication failed. Please try again.")
      setFormState("error")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <BackgroundWrapper>
      <AdminAuthNavbar />

      <div className="flex-1 flex items-center justify-center px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card
            className={`bg-white/95 ${
              formState === "error" ? "error-animation" : formState === "success" ? "success-animation" : ""
            }`}
          >
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Admin Login</CardTitle>
              <CardDescription className="text-center">
                Enter your credentials to access the admin panel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <LabelInputContainer>
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Enter your username"
                    required
                  />
                </LabelInputContainer>
                <LabelInputContainer>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    minLength={8}
                    maxLength={32}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Password must be 8-32 characters</p>
                </LabelInputContainer>
              </form>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <AnimatedButton
                onClick={handleSubmit}
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
                variant="gradient"
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </AnimatedButton>
              <div className="text-center text-sm flex justify-between w-full">
                <Link href="/" className="text-blue-600 hover:underline">
                  Back to Main Site
                </Link>
                {/*<Link href="/admin/signup" className="text-blue-600 hover:underline">
                  Create Admin Account
                </Link>*/}
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </BackgroundWrapper>
  )
}

