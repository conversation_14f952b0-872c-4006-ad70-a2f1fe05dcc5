"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Bell, MoreHorizontal, Send, AlertCircle, Info, Eye, Edit, Trash2, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CreateNotificationForm } from "@/components/create-notification-form"
import { toast } from "sonner"
import { adminApi } from "@/lib/admin-api"

interface Notification {
  id: string
  title: string
  message: string
  type: "system" | "payment" | "loan" | "user" | "alert"
  recipientId?: string
  isAllUsers: boolean
  status: "sent" | "scheduled" | "draft"
  scheduledFor?: string
  isRead: boolean
  createdAt: string
  updatedAt: string
}

export default function NotificationsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isCreateNotificationOpen, setIsCreateNotificationOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isActionLoading, setIsActionLoading] = useState(false)

  // Fetch notifications from API
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Determine filter parameters based on activeTab
        let typeFilter = '';
        let statusFilter = '';

        if (activeTab === 'system' || activeTab === 'payment' || activeTab === 'loan' || activeTab === 'user' || activeTab === 'alert') {
          typeFilter = activeTab;
        } else if (activeTab === 'sent' || activeTab === 'scheduled' || activeTab === 'draft') {
          statusFilter = activeTab;
        }

        const response = await adminApi.getNotifications(currentPage, 10, searchQuery, typeFilter, statusFilter);

        if (response.success) {
          setNotifications(response.data);
          setTotalPages(response.pagination?.pages || 1);
        } else {
          setError('Failed to fetch notifications');
          toast.error('Failed to fetch notifications');
          // Fallback to empty array
          setNotifications([]);
        }
      } catch (err) {
        console.error('Error fetching notifications:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching notifications');
        toast.error('Error loading notifications');
        // Keep empty array as fallback
        setNotifications([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotifications();
  }, [currentPage, searchQuery, activeTab]);

  // Handle notification actions
  const handleNotificationAction = async (notificationId: string, action: 'view' | 'send' | 'edit' | 'delete') => {
    try {
      setIsActionLoading(true);

      switch (action) {
        case 'view':
          // Get notification details
          const viewResponse = await adminApi.getNotificationById(notificationId);
          if (viewResponse.success) {
            // In a real app, you would show a modal with the details
            toast.info(`Viewing notification: ${viewResponse.data.title}`);
          } else {
            toast.error(viewResponse.message || 'Failed to get notification details');
          }
          break;

        case 'send':
          // Send notification now
          const sendResponse = await adminApi.sendNotificationNow(notificationId);
          if (sendResponse.success) {
            toast.success('Notification sent successfully');
            // Refresh the list
            const refreshResponse = await adminApi.getNotifications(currentPage, 10, searchQuery);
            if (refreshResponse.success) {
              setNotifications(refreshResponse.data);
            }
          } else {
            toast.error(sendResponse.message || 'Failed to send notification');
          }
          break;

        case 'edit':
          // In a real app, you would open an edit form
          toast.info('Edit functionality coming soon');
          break;

        case 'delete':
          // Delete notification
          const deleteResponse = await adminApi.deleteNotification(notificationId);
          if (deleteResponse.success) {
            toast.success('Notification deleted successfully');
            // Refresh the list
            const refreshResponse = await adminApi.getNotifications(currentPage, 10, searchQuery);
            if (refreshResponse.success) {
              setNotifications(refreshResponse.data);
            }
          } else {
            toast.error(deleteResponse.message || 'Failed to delete notification');
          }
          break;
      }
    } catch (err) {
      console.error(`Error performing ${action} action:`, err);
      toast.error(`Error performing ${action} action`);
    } finally {
      setIsActionLoading(false);
    }
  };

  // We don't need to filter here since we're doing it on the server side
  const displayNotifications = notifications

  const handleCreateNotification = async (formData: any) => {
    try {
      setIsActionLoading(true);

      // Call API to create notification
      const response = await adminApi.createNotification({
        title: formData.title,
        message: formData.message,
        type: formData.type,
        status: formData.status,
        recipientId: formData.recipients !== 'All Users' ? formData.recipients : undefined,
        isAllUsers: formData.recipients === 'All Users',
        scheduledFor: formData.status === 'scheduled' ? formData.date : undefined
      });

      if (response.success) {
        setIsCreateNotificationOpen(false);

        const statusMessage =
          formData.status === "sent"
            ? "sent to recipients"
            : formData.status === "scheduled"
              ? "scheduled for delivery"
              : "saved as draft";

        toast.success(`Notification has been ${statusMessage}`);

        // Refresh the list
        const refreshResponse = await adminApi.getNotifications(currentPage, 10, searchQuery);
        if (refreshResponse.success) {
          setNotifications(refreshResponse.data);
        }
      } else {
        toast.error(response.message || 'Failed to create notification');
      }
    } catch (err) {
      console.error('Error creating notification:', err);
      toast.error('Error creating notification');
    } finally {
      setIsActionLoading(false);
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "system":
        return <Info className="h-4 w-4 text-blue-600" />
      case "payment":
        return <Bell className="h-4 w-4 text-green-600" />
      case "loan":
        return <Bell className="h-4 w-4 text-purple-600" />
      case "user":
        return <Bell className="h-4 w-4 text-amber-600" />
      case "alert":
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Bell className="h-4 w-4 text-gray-600" />
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "system":
        return <Badge className="bg-blue-500">System</Badge>
      case "payment":
        return <Badge className="bg-green-500">Payment</Badge>
      case "loan":
        return <Badge className="bg-purple-500">Loan</Badge>
      case "user":
        return <Badge className="bg-amber-500">User</Badge>
      case "alert":
        return <Badge className="bg-red-500">Alert</Badge>
      default:
        return <Badge className="bg-gray-500">{type}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "sent":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
            Sent
          </Badge>
        )
      case "scheduled":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">
            Scheduled
          </Badge>
        )
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-300">
            Draft
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
        <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setIsCreateNotificationOpen(true)}>
          <Send className="mr-2 h-4 w-4" />
          Create Notification
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Notification Management</CardTitle>
          <CardDescription>Create, view and manage all system notifications</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
              <TabsList className="grid grid-cols-7 w-full md:w-auto">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="system">System</TabsTrigger>
                <TabsTrigger value="payment">Payment</TabsTrigger>
                <TabsTrigger value="loan">Loan</TabsTrigger>
                <TabsTrigger value="sent">Sent</TabsTrigger>
                <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
                <TabsTrigger value="draft">Draft</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="relative w-full md:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search notifications..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Recipients</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6">
                      <div className="flex justify-center items-center">
                        <Loader2 className="h-6 w-6 animate-spin text-blue-600 mr-2" />
                        <span>Loading notifications...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-red-500">
                      Error: {error}
                    </TableCell>
                  </TableRow>
                ) : displayNotifications.length > 0 ? (
                  displayNotifications.map((notif: Notification) => (
                    <TableRow key={notif.id}>
                      <TableCell className="font-medium">{notif.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTypeIcon(notif.type)}
                          <span>{notif.title}</span>
                        </div>
                        <p className="text-xs text-muted-foreground truncate max-w-xs">{notif.message}</p>
                      </TableCell>
                      <TableCell>{getTypeBadge(notif.type)}</TableCell>
                      <TableCell>{notif.isAllUsers ? 'All Users' : (notif.recipientId || '-')}</TableCell>
                      <TableCell>{getStatusBadge(notif.status)}</TableCell>
                      <TableCell>{new Date(notif.scheduledFor || notif.createdAt).toLocaleString()}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" disabled={isActionLoading}>
                              {isActionLoading ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <MoreHorizontal className="h-4 w-4" />
                              )}
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleNotificationAction(notif.id, 'view')}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            {notif.status === "draft" && (
                              <DropdownMenuItem onClick={() => handleNotificationAction(notif.id, 'send')}>
                                <Send className="h-4 w-4 mr-2" />
                                Send Now
                              </DropdownMenuItem>
                            )}
                            {notif.status === "scheduled" && (
                              <DropdownMenuItem onClick={() => handleNotificationAction(notif.id, 'edit')}>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit Schedule
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleNotificationAction(notif.id, 'delete')}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                      No notifications found matching your search criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {!isLoading && displayNotifications.length > 0 && totalPages > 1 && (
            <div className="flex justify-center items-center mt-6 space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1 || isLoading || isActionLoading}
              >
                Previous
              </Button>

              <div className="text-sm">
                Page {currentPage} of {totalPages}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || isLoading || isActionLoading}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <CreateNotificationForm
        isOpen={isCreateNotificationOpen}
        onClose={() => setIsCreateNotificationOpen(false)}
        onSubmit={handleCreateNotification}
      />
    </div>
  )
}

