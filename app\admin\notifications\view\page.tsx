"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Bell, CheckCircle, Loader2 } from "lucide-react"
import { toast } from "sonner"
import { adminNotificationApi } from "@/lib/admin-notification-api"
import { formatRelativeTime } from "@/lib/utils/date"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Pagination } from "@/components/ui/custom-pagination"

interface Notification {
  id: string
  title: string
  message: string
  type: "system" | "payment" | "loan" | "user" | "alert"
  recipientId?: string
  isAllUsers: boolean
  status: "sent" | "scheduled" | "draft"
  scheduledFor?: string
  isRead: boolean
  createdAt: string
  updatedAt: string
}

export default function AdminNotificationsViewPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [activeTab, setActiveTab] = useState("all")
  const [isMarkingAllAsRead, setIsMarkingAllAsRead] = useState(false)

  // Fetch notifications
  const fetchNotifications = async (page = 1, read?: boolean) => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await adminNotificationApi.getAdminNotifications(page, 10, read)

      if (response.success) {
        setNotifications(response.data)
        setTotalPages(response.pagination?.pages || 1)
      } else {
        setError("Failed to fetch notifications")
        toast.error("Failed to fetch notifications")
      }
    } catch (err) {
      console.error("Error fetching notifications:", err)
      setError("An error occurred while fetching notifications")
      toast.error("Error loading notifications")
    } finally {
      setIsLoading(false)
    }
  }

  // Initial fetch
  useEffect(() => {
    const read = activeTab === "read" ? true : activeTab === "unread" ? false : undefined
    fetchNotifications(currentPage, read)
  }, [currentPage, activeTab])

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    setCurrentPage(1) // Reset to first page when changing tabs
  }

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      const response = await adminNotificationApi.markNotificationAsRead(id)
      if (response.success) {
        // Update the local state
        setNotifications(notifications.map((n) => (n.id === id ? { ...n, isRead: true } : n)))
        toast.success("Notification marked as read")
      } else {
        toast.error("Failed to mark notification as read")
      }
    } catch (error) {
      console.error("Error marking notification as read:", error)
      toast.error("Error marking notification as read")
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      setIsMarkingAllAsRead(true)
      const response = await adminNotificationApi.markAllNotificationsAsRead()
      if (response.success) {
        // Update the local state
        setNotifications(notifications.map((n) => ({ ...n, isRead: true })))
        toast.success("All notifications marked as read")

        // If we're on the unread tab, refresh the list
        if (activeTab === "unread") {
          fetchNotifications(currentPage, false)
        }
      } else {
        toast.error("Failed to mark all notifications as read")
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      toast.error("Error marking all notifications as read")
    } finally {
      setIsMarkingAllAsRead(false)
    }
  }

  // Get notification type color
  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case "system":
        return "bg-blue-100 text-blue-600"
      case "payment":
        return "bg-green-100 text-green-600"
      case "loan":
        return "bg-purple-100 text-purple-600"
      case "user":
        return "bg-yellow-100 text-yellow-600"
      case "alert":
        return "bg-red-100 text-red-600"
      default:
        return "bg-gray-100 text-gray-600"
    }
  }

  // Get notification type badge
  const getNotificationTypeBadge = (type: string) => {
    switch (type) {
      case "system":
        return <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">System</Badge>
      case "payment":
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">Payment</Badge>
      case "loan":
        return <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200">Loan</Badge>
      case "user":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">User</Badge>
      case "alert":
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">Alert</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Notifications</h1>
          <p className="text-muted-foreground">View and manage your notifications</p>
        </div>
        <Button
          onClick={markAllAsRead}
          disabled={isMarkingAllAsRead || notifications.every(n => n.isRead)}
          className="flex items-center gap-2"
        >
          {isMarkingAllAsRead ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <CheckCircle className="h-4 w-4" />
          )}
          Mark All as Read
        </Button>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Notifications</CardTitle>
          <CardDescription>
            Stay updated with system alerts, loan applications, and more
          </CardDescription>
          <Tabs defaultValue="all" value={activeTab} onValueChange={handleTabChange} className="mt-2">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="unread">Unread</TabsTrigger>
              <TabsTrigger value="read">Read</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">{error}</p>
              <Button onClick={() => fetchNotifications(currentPage)}>Try Again</Button>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <Bell className="h-12 w-12 mx-auto mb-4 opacity-20" />
              <p>No notifications found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border ${!notification.isRead ? "bg-blue-50/50 border-blue-100" : "bg-white border-gray-100"}`}
                  onClick={() => !notification.isRead && markAsRead(notification.id)}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-full ${getNotificationTypeColor(notification.type)}`}>
                      <Bell className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className={`font-medium ${!notification.isRead ? "text-blue-900" : "text-gray-900"}`}>
                            {notification.title}
                          </h3>
                          <div className="flex gap-2 mt-1">
                            {getNotificationTypeBadge(notification.type)}
                            {!notification.isRead && (
                              <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">New</Badge>
                            )}
                          </div>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {formatRelativeTime(notification.createdAt)}
                        </span>
                      </div>
                      <p className={`mt-2 text-sm ${!notification.isRead ? "text-blue-800" : "text-gray-600"}`}>
                        {notification.message}
                      </p>
                      {!notification.isRead && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-0 h-auto"
                          onClick={(e) => {
                            e.stopPropagation();
                            markAsRead(notification.id);
                          }}
                        >
                          Mark as read
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {totalPages > 1 && (
                <div className="flex justify-center mt-6">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                  />
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
