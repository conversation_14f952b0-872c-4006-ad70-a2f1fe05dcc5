"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AlertCircle, CheckCircle2, Loader2, Upload } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { adminApi } from "@/lib/admin-api"
import { toast } from "sonner"

export default function AdminProfilePage() {
  const [activeTab, setActiveTab] = useState("profile")
  const [isSuccess, setIsSuccess] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")

  // Profile data
  const [fullName, setFullName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [profileImage, setProfileImage] = useState("/placeholder.svg?height=128&width=128")

  // Password change
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  // File upload
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Fetch admin profile data
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setIsLoading(true)
        setError("")

        const response = await adminApi.getAdminProfile()

        if (response.success && response.data) {
          const { name, email, phone, faceImage } = response.data
          setFullName(name || "")
          setEmail(email || "")
          setPhone(phone || "")

          // Set profile image if available
          if (faceImage) {
            // Check if faceImage is already a data URL
            if (typeof faceImage === 'string' && faceImage.startsWith('data:image')) {
              setProfileImage(faceImage)
              console.log('Using data URL image')
            } else if (typeof faceImage === 'string') {
              // If it's a URL, use it directly
              setProfileImage(faceImage)
              console.log('Using string URL image')
            } else if (faceImage && typeof faceImage === 'object' && faceImage.type === 'Buffer' && Array.isArray(faceImage.data)) {
              // If it's a Buffer object, convert it to a data URL
              try {
                const bufferData = faceImage.data;
                const base64String = btoa(String.fromCharCode.apply(null, bufferData));
                const dataUrl = `data:image/jpeg;base64,${base64String}`;
                setProfileImage(dataUrl);
                console.log('Converted Buffer to data URL in component')
              } catch (err) {
                console.error('Error converting Buffer to data URL:', err);
                setProfileImage('/placeholder.svg?height=128&width=128');
              }
            } else {
              // If it's an object or something else, use the default image
              console.warn('Unexpected faceImage format:', faceImage)
              setProfileImage('/placeholder.svg?height=128&width=128')
            }
          }
        } else {
          setError("Failed to load profile data")
        }
      } catch (err) {
        console.error("Error fetching admin profile:", err)
        setError("Failed to load profile data. Please try again.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchProfile()
  }, [])

  // Handle profile image click
  const handleProfileImageClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  // Resize and compress image
  const resizeAndCompressImage = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = (event) => {
        const img = new Image()
        img.src = event.target?.result as string
        img.onload = () => {
          // Create canvas
          const canvas = document.createElement('canvas')
          // Max dimensions
          const MAX_WIDTH = 400
          const MAX_HEIGHT = 400

          // Calculate dimensions
          let width = img.width
          let height = img.height

          if (width > height) {
            if (width > MAX_WIDTH) {
              height *= MAX_WIDTH / width
              width = MAX_WIDTH
            }
          } else {
            if (height > MAX_HEIGHT) {
              width *= MAX_HEIGHT / height
              height = MAX_HEIGHT
            }
          }

          // Set canvas dimensions
          canvas.width = width
          canvas.height = height

          // Draw image on canvas
          const ctx = canvas.getContext('2d')
          ctx?.drawImage(img, 0, 0, width, height)

          // Get compressed image as data URL
          const dataUrl = canvas.toDataURL('image/jpeg', 0.7) // 70% quality JPEG
          resolve(dataUrl)
        }
        img.onerror = () => {
          reject(new Error('Failed to load image'))
        }
      }
      reader.onerror = () => {
        reject(new Error('Failed to read file'))
      }
    })
  }

  // Handle file change
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please select an image file")
      return
    }

    // Check file size (max 10MB for initial upload)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image size should be less than 10MB")
      return
    }

    try {
      // Show loading state
      setIsSubmitting(true)

      // Resize and compress image
      const compressedImage = await resizeAndCompressImage(file)

      // Update state with compressed image
      setProfileImage(compressedImage)
      toast.success("Image processed successfully")
    } catch (err) {
      console.error("Error processing image:", err)
      toast.error("Failed to process image")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle profile save
  const handleSave = async () => {
    try {
      setIsSubmitting(true)
      setError("")

      // Validate inputs
      if (!fullName.trim()) {
        setError("Full name is required")
        setIsSubmitting(false)
        return
      }

      if (!email.trim()) {
        setError("Email is required")
        setIsSubmitting(false)
        return
      }

      if (!phone.trim()) {
        setError("Phone number is required")
        setIsSubmitting(false)
        return
      }

      // Prepare data for API
      const profileData: Record<string, any> = {
        name: fullName.trim(),
        email: email.trim(),
        phoneNumber: phone.trim()
      }

      // Only include the image if it's a base64 string and has changed
      if (profileImage && profileImage.startsWith('data:image')) {
        // The image will be processed in the adminApi.updateAdminProfile method
        profileData.faceImage = profileImage
      }

      // Call API to update profile
      const response = await adminApi.updateAdminProfile(profileData)

      if (response.success) {
        // Update localStorage with the new profile image
        try {
          const adminUserStr = localStorage.getItem('adminUser');
          if (adminUserStr) {
            const adminUser = JSON.parse(adminUserStr);

            // Update the admin user data
            adminUser.name = fullName.trim();
            adminUser.email = email.trim();
            adminUser.phoneNumber = phone.trim();

            // Update the face image if it was changed
            if (profileImage && profileImage.startsWith('data:image')) {
              adminUser.faceImage = profileImage;
            }

            // Save back to localStorage
            localStorage.setItem('adminUser', JSON.stringify(adminUser));
            console.log('Updated admin user in localStorage with new profile data');
          }
        } catch (err) {
          console.error('Error updating localStorage:', err);
        }

        setIsSuccess(true)
        setSuccessMessage("Profile updated successfully")
        toast.success("Profile updated successfully")

        setTimeout(() => {
          setIsSuccess(false)
          setSuccessMessage("")
        }, 3000)
      } else {
        setError(response.message || "Failed to update profile")
        toast.error(response.message || "Failed to update profile")
      }
    } catch (err: any) {
      console.error("Error updating profile:", err)
      setError(err.message || "An error occurred while updating profile")
      toast.error(err.message || "An error occurred while updating profile")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle password change
  const handlePasswordChange = async () => {
    try {
      setIsSubmitting(true)
      setError("")

      // Validate inputs
      if (!currentPassword) {
        setError("Current password is required")
        setIsSubmitting(false)
        return
      }

      if (!newPassword) {
        setError("New password is required")
        setIsSubmitting(false)
        return
      }

      if (newPassword.length < 8) {
        setError("New password must be at least 8 characters long")
        setIsSubmitting(false)
        return
      }

      if (newPassword !== confirmPassword) {
        setError("Passwords do not match")
        setIsSubmitting(false)
        return
      }

      // Call API to change password
      const response = await adminApi.changeAdminPassword(currentPassword, newPassword)

      if (response.success) {
        setIsSuccess(true)
        setSuccessMessage("Password changed successfully")
        toast.success("Password changed successfully")

        // Reset password fields
        setCurrentPassword("")
        setNewPassword("")
        setConfirmPassword("")

        setTimeout(() => {
          setIsSuccess(false)
          setSuccessMessage("")
        }, 3000)
      } else {
        setError(response.message || "Failed to change password")
        toast.error(response.message || "Failed to change password")
      }
    } catch (err: any) {
      console.error("Error changing password:", err)
      setError(err.message || "An error occurred while changing password")
      toast.error(err.message || "An error occurred while changing password")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Admin Profile</h1>
      </div>

      {isSuccess && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Your Profile</CardTitle>
          <CardDescription>Manage your personal information and preferences</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2">Loading profile data...</span>
            </div>
          ) : (
            <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="profile">Profile Information</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
              </TabsList>

              <TabsContent value="profile" className="space-y-6 pt-6">
                <div className="flex flex-col items-center space-y-4 mb-6">
                  <div className="relative cursor-pointer group" onClick={handleProfileImageClick}>
                    <Avatar className="h-32 w-32 border-2 border-blue-600">
                      {profileImage && typeof profileImage === 'string' ? (
                        <AvatarImage
                          src={profileImage}
                          alt="Admin"
                          onError={(e) => {
                            console.error('Error loading profile image:', e);
                            console.log('Failed image source:', profileImage);
                            // Set fallback image on error
                            e.currentTarget.src = '/placeholder.svg?height=128&width=128';
                          }}
                        />
                      ) : (
                        <AvatarFallback className="text-4xl">{fullName ? fullName.charAt(0).toUpperCase() : "A"}</AvatarFallback>
                      )}
                    </Avatar>
                    <div className="absolute inset-0 bg-black/30 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                      <Upload className="h-8 w-8 text-white" />
                    </div>
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept="image/*"
                      onChange={handleFileChange}
                      aria-label="Upload profile picture"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">Click to upload a new profile picture</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="Enter your phone number"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={handleSave}
                    className="bg-blue-600 hover:bg-blue-700"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Changes"
                    )}
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="security" className="space-y-6 pt-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      placeholder="Enter your current password"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      placeholder="Enter your new password"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="Confirm your new password"
                    />
                  </div>

                  <div className="pt-4">
                    <Button
                      onClick={handlePasswordChange}
                      className="bg-blue-600 hover:bg-blue-700"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Changing Password...
                        </>
                      ) : (
                        "Change Password"
                      )}
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
