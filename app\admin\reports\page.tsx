"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Download, FileText, BarChart3, <PERSON><PERSON><PERSON>, <PERSON>Chart } from "lucide-react"
import FunctionalReportCard from "@/components/functional-report-card"

export default function ReportsPage() {
  const [reportPeriod, setReportPeriod] = useState("month")

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Download className="mr-2 h-4 w-4" />
          Export All Reports
        </Button>
      </div>

      <Tabs defaultValue="financial" className="space-y-6">
        <TabsList>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="user">User</TabsTrigger>
          <TabsTrigger value="loan">Loan</TabsTrigger>
          <TabsTrigger value="operational">Operational</TabsTrigger>
        </TabsList>

        <TabsContent value="financial" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Financial Reports</h2>
            <div className="flex items-center gap-4">
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <FunctionalReportCard
              title="Cash Flow"
              description="Inflows and outflows"
              icon={<LineChart className="h-5 w-5 text-green-600" />}
              period={reportPeriod}
              reportType="financial"
              reportName="cash-flow"
            />
            <FunctionalReportCard
              title="Profit & Loss"
              description="Profitability analysis"
              icon={<PieChart className="h-5 w-5 text-purple-600" />}
              period={reportPeriod}
              reportType="financial"
              reportName="profit-loss"
            />
            <FunctionalReportCard
              title="Outstanding Loans"
              description="Total outstanding amount"
              icon={<BarChart3 className="h-5 w-5 text-red-600" />}
              period={reportPeriod}
              reportType="financial"
              reportName="outstanding-loans"
            />
            <FunctionalReportCard
              title="Repayment Analysis"
              description="Repayment rates and trends"
              icon={<LineChart className="h-5 w-5 text-amber-600" />}
              period={reportPeriod}
              reportType="financial"
              reportName="repayment-analysis"
            />
            <FunctionalReportCard
              title="Transaction Summary"
              description="All financial transactions"
              icon={<FileText className="h-5 w-5 text-blue-600" />}
              period={reportPeriod}
              reportType="financial"
              reportName="transaction-summary"
            />
          </div>
        </TabsContent>

        <TabsContent value="user" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">User Reports</h2>
            <div className="flex items-center gap-4">
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <FunctionalReportCard
              title="User Growth"
              description="New user registrations"
              icon={<LineChart className="h-5 w-5 text-blue-600" />}
              period={reportPeriod}
              reportType="user"
              reportName="user-growth"
            />
            <FunctionalReportCard
              title="User Activity"
              description="Login and engagement metrics"
              icon={<BarChart3 className="h-5 w-5 text-purple-600" />}
              period={reportPeriod}
              reportType="user"
              reportName="user-activity"
            />
          </div>
        </TabsContent>

        <TabsContent value="loan" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Loan Reports</h2>
            <div className="flex items-center gap-4">
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <FunctionalReportCard
              title="Loan Applications"
              description="Application volume and approval rates"
              icon={<BarChart3 className="h-5 w-5 text-blue-600" />}
              period={reportPeriod}
              reportType="loan"
              reportName="loan-applications"
            />
            <FunctionalReportCard
              title="Loan Performance"
              description="Repayment rates and defaults"
              icon={<LineChart className="h-5 w-5 text-red-600" />}
              period={reportPeriod}
              reportType="loan"
              reportName="loan-performance"
            />
            <FunctionalReportCard
              title="Loan Types"
              description="Distribution by purpose and amount"
              icon={<PieChart className="h-5 w-5 text-green-600" />}
              period={reportPeriod}
              reportType="loan"
              reportName="loan-types"
            />
            <FunctionalReportCard
              title="Loan Aging"
              description="Age of outstanding loans"
              icon={<BarChart3 className="h-5 w-5 text-amber-600" />}
              period={reportPeriod}
              reportType="loan"
              reportName="loan-aging"
            />
          </div>
        </TabsContent>

        <TabsContent value="operational" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Operational Reports</h2>
            <div className="flex items-center gap-4">
              <Select value={reportPeriod} onValueChange={setReportPeriod}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <FunctionalReportCard
              title="System Performance"
              description="Uptime and response metrics"
              icon={<LineChart className="h-5 w-5 text-blue-600" />}
              period={reportPeriod}
              reportType="operational"
              reportName="system-performance"
            />
            <FunctionalReportCard
              title="Staff Activity"
              description="Admin user actions"
              icon={<BarChart3 className="h-5 w-5 text-purple-600" />}
              period={reportPeriod}
              reportType="operational"
              reportName="staff-activity"
            />
            <FunctionalReportCard
              title="Audit Log"
              description="System changes and security events"
              icon={<FileText className="h-5 w-5 text-red-600" />}
              period={reportPeriod}
              reportType="operational"
              reportName="audit-log"
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

