"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AdminAuthNavbar } from "@/components/admin-auth-navbar"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { AnimatedButton, LabelInputContainer } from "@/components/ui/animated-button"
import { motion } from "framer-motion"
import { toast } from "sonner"

// Define API URL
const API_URL = "http://localhost:3001/api";

export default function AdminSignupPage() {
  const router = useRouter();
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [formState, setFormState] = useState<"idle" | "error" | "success">("idle");

  useEffect(() => {
    // Log that the component has mounted
    console.log('Admin signup page mounted');
    
    if (formState === "error") {
      const timer = setTimeout(() => setFormState("idle"), 600);
      return () => clearTimeout(timer);
    }
    if (formState === "success") {
      const timer = setTimeout(() => setFormState("idle"), 1500);
      return () => clearTimeout(timer);
    }
  }, [formState]);
  
  // Log the current pathname
  useEffect(() => {
    console.log('Current pathname:', window.location.pathname);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      // Validate inputs
      if (!fullName) {
        setError("Please enter your full name");
        setFormState("error");
        setIsLoading(false);
        return;
      }

      if (!email) {
        setError("Please enter your email");
        setFormState("error");
        setIsLoading(false);
        return;
      }

      if (!phoneNumber) {
        setError("Please enter your phone number");
        setFormState("error");
        setIsLoading(false);
        return;
      }

      if (!password) {
        setError("Please enter a password");
        setFormState("error");
        setIsLoading(false);
        return;
      }

      if (password.length < 8) {
        setError("Password must be at least 8 characters long");
        setFormState("error");
        setIsLoading(false);
        return;
      }

      if (password !== confirmPassword) {
        setError("Passwords do not match");
        setFormState("error");
        setIsLoading(false);
        return;
      }

      // Create admin user directly
      console.log('Creating admin user with:', { fullName, email, phoneNumber });

      const url = `${API_URL}/auth/create-admin`;
      console.log('Sending request to:', url);

      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fullName,
            email,
            phoneNumber,
            password,
            role: 'admin',
            status: 'active'
          }),
        });
        
        console.log('Response status:', response.status);
        
        // Try to read the response as text first
        const responseText = await response.text();
        console.log('Response text:', responseText);
        
        // Parse the response if possible
        let data;
        try {
          data = responseText ? JSON.parse(responseText) : {};
          console.log('Parsed response data:', data);
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError);
          throw new Error('Failed to parse server response');
        }

        if (data.success) {
          console.log('Admin user created successfully:', data);
          setFormState("success");
          toast.success('Admin user created successfully', {
            description: 'You can now log in with your credentials'
          });
          
          // Redirect to login page after a delay
          setTimeout(() => {
            router.push("/admin/login");
          }, 2000);
        } else {
          throw new Error(data.message || 'Failed to create admin user');
        }
      } catch (err: any) {
        console.error('Admin signup error:', err);
        
        // Show detailed error message
        let errorMessage = "Failed to create admin user";
        
        if (err.message) {
          errorMessage = err.message;
        }
        
        // If it's a network error, provide more helpful information
        if (err instanceof TypeError && err.message.includes('fetch')) {
          errorMessage = "Network error: Could not connect to the server. Please check if the backend is running.";
        }
        
        console.error('Error details:', errorMessage);
        setError(errorMessage);
        setFormState("error");
        
        // Show error toast for better visibility
        toast.error('Admin signup failed', {
          description: errorMessage
        });
      }
    } catch (err: any) {
      console.error('Form validation error:', err);
      setError(err.message || "An unexpected error occurred");
      setFormState("error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <BackgroundWrapper>
      <AdminAuthNavbar />

      <div className="flex-1 flex items-center justify-center px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card
            className={`bg-white/95 ${
              formState === "error" ? "error-animation" : formState === "success" ? "success-animation" : ""
            }`}
          >
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Create Admin Account</CardTitle>
              <CardDescription className="text-center">Enter your details to create an admin account</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <LabelInputContainer>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full name"
                    required
                  />
                </LabelInputContainer>
                <LabelInputContainer>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                  />
                </LabelInputContainer>
                <LabelInputContainer>
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="Enter your phone number"
                    required
                  />
                </LabelInputContainer>
                <LabelInputContainer>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                  />
                </LabelInputContainer>
                <LabelInputContainer>
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your password"
                    required
                  />
                </LabelInputContainer>
              </form>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <AnimatedButton
                onClick={handleSubmit}
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
                variant="gradient"
              >
                {isLoading ? "Creating Account..." : "Create Admin Account"}
              </AnimatedButton>
              <div className="text-center text-sm">
                <Link href="/admin/login" className="text-blue-600 hover:underline">
                  Already have an account? Sign In
                </Link>
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </BackgroundWrapper>
  );
}
