"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, ArrowLeft, CheckCircle2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AuthNavbar } from "@/components/auth-navbar"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { AnimatedButton, LabelInputContainer } from "@/components/ui/animated-button"
import { motion } from "framer-motion"
import { useAuth } from "@/lib/auth-context"

export default function ChangePasswordPage() {
  const router = useRouter()
  const { changePassword, isLoading: authLoading, error: authError, isAuthenticated } = useAuth()
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formState, setFormState] = useState<"idle" | "error" | "success">("idle")
  const [errors, setErrors] = useState<{ currentPassword?: string; newPassword?: string; confirmPassword?: string }>({})

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login")
    }
  }, [isAuthenticated, router])

  useEffect(() => {
    if (authError) {
      setError(authError)
      setFormState("error")
    }
  }, [authError])

  useEffect(() => {
    if (formState === "error") {
      const timer = setTimeout(() => setFormState("idle"), 600)
      return () => clearTimeout(timer)
    }
  }, [formState])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess(false)
    setErrors({})

    // Validate passwords
    if (!currentPassword) {
      setError("Please enter your current password")
      setFormState("error")
      return
    }

    if (!newPassword) {
      setError("Please enter a new password")
      setFormState("error")
      return
    }

    if (newPassword.length < 8) {
      setError("Password must be at least 8 characters long")
      setFormState("error")
      return
    }

    if (newPassword.length > 32) {
      setError("Password must be less than 32 characters long")
      setFormState("error")
      return
    }

    if (!confirmPassword) {
      setError("Please confirm your new password")
      setFormState("error")
      return
    }

    if (newPassword !== confirmPassword) {
      setError("Passwords do not match")
      setFormState("error")
      return
    }

    setIsLoading(true)

    try {
      await changePassword(currentPassword, newPassword)
      setSuccess(true)
      setFormState("success")

      // Redirect after showing success message
      setTimeout(() => {
        router.push("/dashboard")
      }, 2000)
    } catch (err: any) {
      // Error is handled by the auth context
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <BackgroundWrapper>
      <AuthNavbar />

      <div className="flex-1 flex flex-col items-center px-4 py-12">
        <div className="w-full max-w-4xl mb-6 flex justify-end">
          <Button
            variant="outline"
            className="bg-white/20"
            onClick={() => router.push("/dashboard")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card
            className={`bg-white/95 ${
              formState === "error" ? "error-animation" : formState === "success" ? "success-animation" : ""
            }`}
          >
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Change Password</CardTitle>
              <CardDescription className="text-center">Create a new password for your account</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Alert className="bg-green-50 text-green-800 border-green-200">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <AlertDescription>Password changed successfully! Redirecting...</AlertDescription>
                    </Alert>
                  </motion.div>
                )}

                <LabelInputContainer>
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    placeholder="Enter current password"
                    required
                  />
                  {errors.currentPassword && <p className="text-sm text-red-500">{errors.currentPassword}</p>}
                </LabelInputContainer>

                <LabelInputContainer>
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Enter new password"
                    required
                    minLength={8}
                    maxLength={32}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Password must be 8-32 characters</p>
                  {errors.newPassword && <p className="text-sm text-red-500">{errors.newPassword}</p>}
                </LabelInputContainer>

                <LabelInputContainer>
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm new password"
                    required
                    minLength={8}
                    maxLength={32}
                  />
                  {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword}</p>}
                </LabelInputContainer>
              </form>
            </CardContent>
            <CardFooter>
              <AnimatedButton
                onClick={handleSubmit}
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading || authLoading}
                variant="gradient"
              >
                {isLoading || authLoading ? "Changing Password..." : "Change Password"}
              </AnimatedButton>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </BackgroundWrapper>
  )
}

