"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { UserNav } from "@/components/user-nav"
import { LoanSummary } from "@/components/loan-summary"
import { CreditCard, Wallet } from "lucide-react"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { NotificationBell } from "@/components/notification-bell"
import { AuthGuard } from "@/components/auth-guard"
import { useAuth } from "@/lib/auth-context"
import { loanApi } from "@/lib/api"
import { toast } from "sonner"
import { ChatBot } from "@/components/chat-bot"

export default function DashboardPage() {
  const { user } = useAuth()
  const [hasActiveLoans, setHasActiveLoans] = useState(false)
  const [loans, setLoans] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [creditInfo, setCreditInfo] = useState({
    availableCredit: 0,
    outstandingAmount: 0,
    maxCreditLimit: 600
  })

  // Helper function to safely format numbers
  const formatCurrency = (value: any): string => {
    // Convert to number if it's a string or handle null/undefined
    const numValue = typeof value === 'string' ? parseFloat(value) : (typeof value === 'number' ? value : 0);
    // Check if it's a valid number
    return !isNaN(numValue) ? numValue.toFixed(2) : '0.00';
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch loans
        const loansResponse = await loanApi.getUserLoans();
        if (loansResponse.success) {
          setLoans(loansResponse.data);
          // Check for active loans (approved or disbursed status)
          setHasActiveLoans(loansResponse.data.some((loan: any) =>
            ['approved', 'disbursed'].includes(loan.status?.toLowerCase())
          ));
        }

        // Fetch available credit
        const creditResponse = await loanApi.getAvailableCredit();
        if (creditResponse.success && creditResponse.data) {
          // Ensure we have valid numeric values or defaults
          const availableCredit = typeof creditResponse.data.availableCredit === 'number'
            ? creditResponse.data.availableCredit
            : parseFloat(creditResponse.data.availableCredit) || 0;

          const outstandingAmount = typeof creditResponse.data.outstandingAmount === 'number'
            ? creditResponse.data.outstandingAmount
            : parseFloat(creditResponse.data.outstandingAmount) || 0;

          const maxCreditLimit = typeof creditResponse.data.maxCreditLimit === 'number'
            ? creditResponse.data.maxCreditLimit
            : parseFloat(creditResponse.data.maxCreditLimit) || 600;

          // Update state with validated values
          setCreditInfo({
            availableCredit,
            outstandingAmount,
            maxCreditLimit
          });
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Failed to fetch dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <AuthGuard>
      <BackgroundWrapper>
        <ChatBot />
        <header className="bg-gradient-to-r from-gray-200 to-white shadow">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <div className="flex flex-col">
              <h1 className="text-2xl font-bold text-blue-900">Loan Portal</h1>
              <div className="flex items-center mt-1">
                <span className="text-sm text-gray-600">Available Credit:</span>
                <span className="text-sm font-semibold ml-2 text-green-600">
                  E{formatCurrency(creditInfo.availableCredit)}
                </span>
                <span className="text-xs text-gray-500 ml-2">
                  of E{formatCurrency(creditInfo.maxCreditLimit)}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <NotificationBell />
              <UserNav />
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <h3 className="text-3xl font-bold text-white">Welcome Back, {user?.name || 'User'}, to the Umlamleli platform, click on apply now to apply for a loan and click on payment of loan to make a payment. </h3>
            <div className="mt-2 md:mt-0 bg-white/90 rounded-lg px-4 py-2 shadow-sm">
              <div className="flex flex-col">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Available Credit:</span>
                  <span className="text-sm font-bold ml-2 text-green-600">
                    E{formatCurrency(creditInfo.availableCredit)}
                  </span>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-sm text-gray-600">Outstanding Loans:</span>
                  <span className="text-sm font-bold ml-2 text-blue-600">
                    E{formatCurrency(creditInfo.outstandingAmount)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <Card className="hover:shadow-lg transition-shadow bg-white/95">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl">Apply for Loan</CardTitle>
                <CardDescription>Request funds for your needs</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="h-24 flex items-center justify-center text-blue-600">
                  <CreditCard size={64} />
                </div>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full bg-blue-600 hover:bg-blue-700">
                  <Link href="/loan/apply">Apply Now</Link>
                </Button>
              </CardFooter>
            </Card>

            <Card className="hover:shadow-lg transition-shadow bg-white/95">
              <CardHeader className="pb-2">
                <CardTitle className="text-xl">Payment of Loan</CardTitle>
                <CardDescription>Manage your existing loans</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="h-24 flex items-center justify-center text-blue-600">
                  <Wallet size={64} />
                </div>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full bg-blue-600 hover:bg-blue-700" disabled={!hasActiveLoans}>
                  <Link href="/loan/payment">Make Payment</Link>
                </Button>
              </CardFooter>
            </Card>
          </div>

          {isLoading ? (
            <Card className="bg-white/95">
              <CardContent>
                <div className="flex justify-center items-center py-8">
                  <div className="animate-pulse flex flex-col items-center">
                    <div className="w-12 h-12 rounded-full bg-blue-200 mb-3"></div>
                    <div className="h-4 w-24 bg-blue-200 rounded"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : hasActiveLoans ? (
            <LoanSummary loans={loans} />
          ) : (
            <Card className="bg-white/95">
              <CardHeader>
                <CardTitle>No Active Loans</CardTitle>
                <CardDescription>You don't have any active loans at the moment</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center py-8 text-muted-foreground">Apply for a loan to get started</p>
              </CardContent>
            </Card>
          )}
        </main>
      </BackgroundWrapper>
    </AuthGuard>
  )
}

