"use client"

import { useState, useEffect } from "react"
import { useIsMobile } from "@/hooks/use-mobile"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Stepper } from "@/components/stepper"
import { LoanAgreement } from "@/components/loan-agreement"
import FaceScan from "@/components/face-scan"
import { LoanApplicationForm } from "@/components/loan-application-form"
import { OtpVerification } from "@/components/otp-verification"
import { LoanConfirmation } from "@/components/loan-confirmation"
import { LoanSuccess } from "@/components/loan-success"
import { Button } from "@/components/ui/button"
import { authApi } from "@/lib/api"
import { useAuth } from "@/lib/auth-context"
import { toast } from "sonner"
import { Loader2 } from "lucide-react"

export default function LoanApplicationPage() {
  const router = useRouter()
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState(0)
  // State to track API call processing
  const [isProcessing, setIsProcessing] = useState(false)
  // Check if on mobile device
  const isMobile = useIsMobile()

  // Debug log user data
  useEffect(() => {
    if (user) {
      console.log('Loan Application - User data:', {
        userId: user?.id,
        isFaceVerified: user?.isFaceVerified,
        hasFaceDescriptor: !!user?.faceDescriptor,
        faceDescriptorLength: user?.faceDescriptor ? JSON.parse(user.faceDescriptor).length : 'N/A'
      });
    }
  }, [user]);

  const [formData, setFormData] = useState({
    loanAmount: "",
    purpose: "",
    collateral: "",
    phoneNumber: "",
    termInMonths: "",
    loanId: "",
    applicationDate: "",
  })

  const steps = ["Agreement", "Face Verification", "Application", "OTP Verification", "Confirmation", "Complete"]

  const handleNext = () => {
    setCurrentStep((prev) => prev + 1)
  }

  const handleBack = () => {
    setCurrentStep((prev) => prev - 1)
  }

  const updateFormData = (data: Partial<typeof formData>) => {
    setFormData((prev) => ({ ...prev, ...data }))
  }

  const handleCancel = () => {
    // Show confirmation before canceling
    if (window.confirm("Are you sure you want to cancel your loan application? All progress will be lost.")) {
      try {
        router.push("/dashboard")
      } catch (error) {
        console.error("Navigation error:", error)
        // Fallback to direct navigation
        window.location.href = "/dashboard"
      }
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <LoanAgreement onAccept={handleNext} />
          </div>
        )
      case 1:
        return (
          <div className="space-y-6">
            {isProcessing && (
              <div className="flex items-center justify-center p-8 bg-white/80 rounded-lg">
                <div className="flex flex-col items-center space-y-2">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <p>Processing face data...</p>
                </div>
              </div>
            )}

            <FaceScan
              onVerificationComplete={(success) => {
                console.log('Verification complete callback received with result:', success);
                if (success) {
                  toast.success('Face verification successful!');
                  // Add a small delay before proceeding to the next step
                  setTimeout(() => {
                    handleNext();
                  }, 1500);
                } else {
                  toast.error('Face verification failed', {
                    description: 'Please try again or contact support if the issue persists'
                  });
                }
              }}
              onFaceRegistered={async (descriptor) => {
                try {
                  setIsProcessing(true);
                  // Convert Float32Array to regular array for JSON serialization
                  const descriptorArray = Array.from(descriptor);

                  // Call the API to store the face descriptor
                  await authApi.verifyFace(JSON.stringify(descriptorArray));

                  toast.success('Face registered successfully');

                  // Automatically proceed to next step after successful registration
                  setTimeout(() => {
                    handleNext();
                  }, 1500);
                } catch (error) {
                  console.error('Error registering face:', error);
                  toast.error('Failed to register face', {
                    description: error instanceof Error ? error.message : 'Please try again'
                  });
                } finally {
                  setIsProcessing(false);
                }
              }}
              isVerifying={user?.isFaceVerified || false}
              referenceDescriptor={user?.faceDescriptor ? new Float32Array(JSON.parse(user.faceDescriptor)) : undefined}
            />

            <div className="flex justify-between mt-4">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <Button variant="outline" onClick={handleCancel} className="text-red-500">
                Cancel Application
              </Button>
            </div>
          </div>
        )
      case 2:
        return (
          <div className="space-y-6">
            <LoanApplicationForm formData={formData} updateFormData={updateFormData} onSubmit={handleNext} />
            <div className="flex justify-end mt-4">
              <Button variant="outline" onClick={handleCancel} className="text-red-500">
                Cancel Application
              </Button>
            </div>
          </div>
        )
      case 3:
        return (
          <div className="space-y-6">
            <OtpVerification phoneNumber={formData.phoneNumber} onVerify={handleNext} />
            <div className="flex justify-between mt-4">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <Button variant="outline" onClick={handleCancel} className="text-red-500">
                Cancel Application
              </Button>
            </div>
          </div>
        )
      case 4:
        return <LoanConfirmation formData={formData} onConfirm={handleNext} onBack={handleBack} />
      case 5:
        return <LoanSuccess onBackToDashboard={() => {
          try {
            router.push("/dashboard")
          } catch (error) {
            console.error("Router navigation error:", error)
            // Fallback to direct navigation
            window.location.href = "/dashboard"
          }
        }} />
      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-blue-50 py-8 px-4">
      <div className="container mx-auto max-w-4xl">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-blue-900">Loan Application</h1>
            {isMobile && <p className="text-blue-600 mt-1">Step {currentStep + 1}: {steps[currentStep]}</p>}
          </div>
          <Button variant="outline" onClick={handleCancel} className="text-red-500">
            Cancel Application
          </Button>
        </div>

        {!isMobile && <Stepper steps={steps} currentStep={currentStep} className="mb-8" />}

        <Card className="w-full">
          <CardContent className="pt-6">
            {renderStepContent()}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

