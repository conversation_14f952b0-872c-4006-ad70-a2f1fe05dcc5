"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AuthNavbar } from "@/components/auth-navbar"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { AnimatedButton, LabelInputContainer } from "@/components/ui/animated-button"
import { motion } from "framer-motion"
import { useAuth } from "@/lib/auth-context"

export default function LoginPage() {
  const router = useRouter()
  const { login, isLoading, error: authError, isAuthenticated, user } = useAuth()
  const [studentId, setStudentId] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [formState, setFormState] = useState<"idle" | "error" | "success">("idle")

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (isAuthenticated) {
      router.push("/dashboard")
    }
  }, [isAuthenticated, router])

  useEffect(() => {
    if (authError) {
      setError(authError)
      setFormState("error")
    }
  }, [authError])

  useEffect(() => {
    if (formState === "error") {
      const timer = setTimeout(() => setFormState("idle"), 600)
      return () => clearTimeout(timer)
    }
    if (formState === "success") {
      const timer = setTimeout(() => setFormState("idle"), 1500)
      return () => clearTimeout(timer)
    }
  }, [formState])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    console.log("Login form submitted with:", { studentId, password: password ? "***" : "empty" })

    // Validate inputs
    if (!studentId) {
      setError("Please enter your Student ID")
        setFormState("error")
        return
      }

      if (!password) {
        setError("Please enter your password")
        setFormState("error")
        return
      }

    try {
      console.log("Attempting login with:", { studentId, passwordLength: password.length })
      await login(studentId, password)
      console.log("Login function completed, form state:", formState)
      setFormState("success")
      
      // Check if we're authenticated
      console.log("Authentication status:", { isAuthenticated, user })
      
      // Redirect happens automatically in useEffect when isAuthenticated changes
    } catch (err: any) {
      console.error("Login form error:", err)
      // Error is handled by the auth context
    }
  }

  return (
    <BackgroundWrapper>
      <AuthNavbar />

      <div className="flex-1 flex items-center justify-center px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card
            className={`bg-white/95 ${
              formState === "error" ? "error-animation" : formState === "success" ? "success-animation" : ""
            }`}
          >
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Sign In</CardTitle>
              <CardDescription className="text-center">Enter your credentials to access your account</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <LabelInputContainer>
                  <Label htmlFor="studentId">Student ID</Label>
                  <Input
                    id="studentId"
                    type="text"
                    value={studentId}
                    onChange={(e) => setStudentId(e.target.value)}
                    placeholder="Enter your student ID"
                    required
                  />
                </LabelInputContainer>
                <LabelInputContainer>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">First-time users: Use default password "password123"</p>
                </LabelInputContainer>
              </form>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <AnimatedButton
                onClick={handleSubmit}
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
                variant="gradient"
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </AnimatedButton>
              <div className="text-center text-sm flex justify-between w-full">
                {/*<Link href="/change-password" className="text-blue-600 hover:underline">
                  Change Password
                </Link>*/}
                <Link href="/register" className="text-blue-600 hover:underline">
                  Create Account
                </Link>
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </BackgroundWrapper>
  )
}

