"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Bell, CheckCircle, Loader2 } from "lucide-react"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { notificationApi } from "@/lib/api"
import { toast } from "sonner"

type Notification = {
  id: string
  title: string
  message: string
  type: string
  status: string
  isRead: boolean
  createdAt: string
  updatedAt: string
}

export default function NotificationsPage() {
  const router = useRouter()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [activeTab, setActiveTab] = useState("all")

  // Fetch notifications from API
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Determine if we should filter by read status
        const readFilter = activeTab === "unread" ? false : undefined

        const response = await notificationApi.getUserNotifications(currentPage, 10, readFilter)

        if (response.success) {
          setNotifications(response.data)
          setTotalPages(response.pagination?.pages || 1)
        } else {
          setError("Failed to fetch notifications")
          toast.error("Failed to fetch notifications")
          // Fallback to empty array
          setNotifications([])
        }
      } catch (err) {
        console.error("Error fetching notifications:", err)
        setError(err instanceof Error ? err.message : "An error occurred while fetching notifications")
        toast.error("Error loading notifications")
        // Keep empty array as fallback
        setNotifications([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchNotifications()
  }, [currentPage, activeTab])

  const unreadCount = notifications.filter((n) => !n.isRead).length

  const markAsRead = async (id: string) => {
    try {
      const response = await notificationApi.markAsRead(id)
      if (response.success) {
        // Update the local state
        setNotifications(notifications.map((n) => (n.id === id ? { ...n, isRead: true } : n)))
        toast.success("Notification marked as read")
      } else {
        toast.error("Failed to mark notification as read")
      }
    } catch (error) {
      console.error("Error marking notification as read:", error)
      toast.error("Error marking notification as read")
    }
  }

  const markAllAsRead = async () => {
    try {
      const response = await notificationApi.markAllAsRead()
      if (response.success) {
        // Update the local state
        setNotifications(notifications.map((n) => ({ ...n, isRead: true })))
        toast.success("All notifications marked as read")
      } else {
        toast.error("Failed to mark all notifications as read")
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      toast.error("Error marking all notifications as read")
    }
  }

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "payment":
        return "bg-blue-100 text-blue-600"
      case "loan":
        return "bg-green-100 text-green-600"
      case "system":
        return "bg-purple-100 text-purple-600"
      case "alert":
        return "bg-red-100 text-red-600"
      case "user":
        return "bg-yellow-100 text-yellow-600"
      default:
        return "bg-gray-100 text-gray-600"
    }
  }

  return (
    <BackgroundWrapper>
      <div className="py-8 px-4 w-full">
        <div className="container mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-8 gap-4">
            <h1 className="text-3xl font-bold text-white">Notifications</h1>
            <Button variant="outline" className="bg-white/20 self-start sm:self-auto" onClick={() => router.push("/dashboard")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </div>

          <Card className="bg-white/95 w-full">
            <CardHeader className="flex flex-col sm:flex-row sm:items-center justify-between pb-2 gap-4">
              <div>
                <CardTitle className="text-xl">All Notifications</CardTitle>
                <CardDescription>Stay updated with your loan activities</CardDescription>
              </div>
              <div className="flex flex-wrap items-center gap-2 mt-2 sm:mt-0">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600 hover:text-blue-800"
                    onClick={markAllAsRead}
                  >
                    Mark all as read
                  </Button>
                )}
                <Badge variant="outline" className="bg-blue-100 text-blue-600">
                  {unreadCount} unread
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all" onValueChange={setActiveTab}>
                <div className="overflow-x-auto">
                  <TabsList className="mb-4 w-full flex flex-nowrap">
                    <TabsTrigger value="all" className="flex-1">All</TabsTrigger>
                    <TabsTrigger value="unread" className="flex-1">Unread</TabsTrigger>
                    <TabsTrigger value="loan" className="flex-1">Loans</TabsTrigger>
                    <TabsTrigger value="payment" className="flex-1">Payments</TabsTrigger>
                    <TabsTrigger value="system" className="flex-1">System</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="all">
                  <div className="space-y-4">
                    {isLoading ? (
                      <div className="text-center py-12">
                        <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary mb-4" />
                        <p className="text-muted-foreground">Loading notifications...</p>
                      </div>
                    ) : error ? (
                      <div className="text-center py-12 text-red-500">
                        <p>{error}</p>
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={() => setCurrentPage(1)}
                        >
                          Try Again
                        </Button>
                      </div>
                    ) : notifications.length > 0 ? (
                      notifications.map((notification) => (
                        <NotificationItem
                          key={notification.id}
                          notification={notification}
                          onMarkAsRead={markAsRead}
                          typeColor={getTypeColor(notification.type)}
                        />
                      ))
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <Bell className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
                        <p>No notifications to display</p>
                      </div>
                    )}

                    {/* Pagination */}
                    {!isLoading && !error && notifications.length > 0 && totalPages > 1 && (
                      <div className="flex flex-wrap justify-center mt-6 gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={currentPage === 1}
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          className="flex-shrink-0"
                        >
                          Previous
                        </Button>
                        <span className="flex items-center px-3 text-sm">
                          Page {currentPage} of {totalPages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={currentPage === totalPages}
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                          className="flex-shrink-0"
                        >
                          Next
                        </Button>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="unread">
                  <div className="space-y-4">
                    {notifications.filter((n) => !n.isRead).length > 0 ? (
                      notifications
                        .filter((n) => !n.isRead)
                        .map((notification) => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onMarkAsRead={markAsRead}
                            typeColor={getTypeColor(notification.type)}
                          />
                        ))
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <CheckCircle className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
                        <p>No unread notifications</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="payment">
                  <div className="space-y-4">
                    {notifications.filter((n) => n.type.toLowerCase() === "payment").length > 0 ? (
                      notifications
                        .filter((n) => n.type.toLowerCase() === "payment")
                        .map((notification) => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onMarkAsRead={markAsRead}
                            typeColor={getTypeColor(notification.type)}
                          />
                        ))
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <p>No payment notifications</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="loan">
                  <div className="space-y-4">
                    {notifications.filter((n) => n.type.toLowerCase() === "loan").length > 0 ? (
                      notifications
                        .filter((n) => n.type.toLowerCase() === "loan")
                        .map((notification) => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onMarkAsRead={markAsRead}
                            typeColor={getTypeColor(notification.type)}
                          />
                        ))
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <p>No loan notifications</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="system">
                  <div className="space-y-4">
                    {notifications.filter((n) => n.type.toLowerCase() === "system").length > 0 ? (
                      notifications
                        .filter((n) => n.type.toLowerCase() === "system")
                        .map((notification) => (
                          <NotificationItem
                            key={notification.id}
                            notification={notification}
                            onMarkAsRead={markAsRead}
                            typeColor={getTypeColor(notification.type)}
                          />
                        ))
                    ) : (
                      <div className="text-center py-12 text-muted-foreground">
                        <p>No system notifications</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </BackgroundWrapper>
  )
}

interface NotificationItemProps {
  notification: Notification
  onMarkAsRead: (id: string) => void
  typeColor: string
}

function NotificationItem({ notification, onMarkAsRead, typeColor }: NotificationItemProps) {
  // Format the date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);
    const diffInDays = diffInHours / 24;

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInDays < 7) {
      const days = Math.floor(diffInDays);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div
      className={`p-4 rounded-lg border ${!notification.isRead ? "bg-blue-50/50 border-blue-100" : "bg-white border-gray-100"}`}
      onClick={() => !notification.isRead && onMarkAsRead(notification.id)}
    >
      <div className="flex items-start gap-3">
        <div className={`p-2 rounded-full ${typeColor} flex-shrink-0`}>
          <Bell className="h-5 w-5" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-1">
            <h3 className={`font-medium ${!notification.isRead ? "text-blue-900" : "text-gray-900"} break-words`}>
              {notification.title}
            </h3>
            <span className="text-xs text-muted-foreground whitespace-nowrap flex-shrink-0">
              {formatDate(notification.createdAt)}
            </span>
          </div>
          <p className={`mt-1 text-sm ${!notification.isRead ? "text-blue-800" : "text-gray-600"} break-words`}>
            {notification.message}
          </p>
          {!notification.isRead && (
            <div className="mt-2 flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 text-xs text-blue-600"
                onClick={(e) => {
                  e.stopPropagation()
                  onMarkAsRead(notification.id)
                }}
              >
                Mark as read
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

