import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MainNavbar } from "@/components/main-navbar"
import { CreditCard } from "lucide-react"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { InterestCalculator } from "@/components/interest-calculator"
import { ChatBot } from "@/components/chat-bot"

export default function Home() {
  return (
    <BackgroundWrapper>
      <MainNavbar />
      <ChatBot />
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 md:py-24">
        <div className="max-w-3xl mx-auto text-center space-y-6">
          <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl">Quick and Secure Loans</h1>
          <p className="text-xl text-blue-100">
            Get instant access to funds with our streamlined loan application process
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4 mt-8">
            <Button
              asChild
              size="lg"
              className="inline-flex h-12 animate-shimmer items-center justify-center rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-400 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50"
            >
              <Link href="/login">Apply Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-black border-white hover:bg-white/10">
              <Link href="/how-it-works">Learn More</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Interest Calculator Section */}
      <InterestCalculator />

      {/* Footer */}
      <footer className="bg-blue-900/90 text-white py-12">
        <div className="container mx-auto px-4">
          {/*<div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-white p-1.5 rounded">
                  <CreditCard className="h-6 w-6 text-blue-600" />
                </div>
                {/* TEMPORARILY DISABLED: Umlamleli branding */}
                {/* <span className="text-xl font-bold">Umlamleli</span> */}
               {/* <span className="text-xl font-bold">Umlamlelie</span>
              </div>
              <p className="text-blue-200">Making financial services accessible to everyone.</p>*/}
           {/*</div>*/}

            {/* TEMPORARILY DISABLED: Quick Links section
            <div>
              <h3 className="text-lg font-bold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/" className="text-blue-200 hover:text-white">
                    Home
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-blue-200 hover:text-white">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/how-it-works" className="text-blue-200 hover:text-white">
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link href="/service" className="text-blue-200 hover:text-white">
                    Services
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-bold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/terms-of-service" className="text-blue-200 hover:text-white">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="/privacy-policy" className="text-blue-200 hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/cookie-policy" className="text-blue-200 hover:text-white">
                    Cookie Policy
                  </Link>
                </li>
              </ul>
            </div>
            */}

           {/* <div>
              <h3 className="text-lg font-bold mb-4">Contact</h3>
              <ul className="space-y-2">
                <li className="text-blue-200">Email: <EMAIL></li>
                <li className="text-blue-200">Phone: +26878304567</li>
              </ul>
            </div>
          </div>*/}

          <div className="border-t border-blue-800 mt-8 pt-8 text-center text-blue-200">
            <p>&copy; {new Date().getFullYear()} Umlamleli. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </BackgroundWrapper>
  )
}

