import Link from "next/link"
import { MainNavbar } from "@/components/main-navbar"
import { BackgroundWrapper } from "@/components/background-wrapper"

export default function PrivacyPolicyPage() {
  return (
    <BackgroundWrapper>
      <MainNavbar />
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto bg-white/90 rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-blue-900 mb-6">Privacy Policy</h1>

          <div className="prose max-w-none">
            <p className="mb-4">
              At Umlamleli, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose,
              and safeguard your information when you use our services.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">1. Information We Collect</h2>
            <p className="mb-4">
              We collect personal information that you provide directly to us, including but not limited to:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Personal identifiers (name, address, phone number, email)</li>
              <li>Financial information (income, expenses, banking details)</li>
              <li>Employment information</li>
              <li>Identification documents</li>
              <li>Biometric data for verification purposes</li>
            </ul>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">2. How We Use Your Information</h2>
            <p className="mb-4">We use the information we collect to:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Process and evaluate loan applications</li>
              <li>Verify your identity</li>
              <li>Communicate with you about your account</li>
              <li>Process transactions</li>
              <li>Improve our services</li>
              <li>Comply with legal obligations</li>
            </ul>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">3. Information Sharing</h2>
            <p className="mb-4">We may share your information with:</p>
            <ul className="list-disc pl-6 mb-4">
              <li>Service providers who assist in our operations</li>
              <li>Financial institutions for payment processing</li>
              <li>Credit bureaus for credit checks</li>
              <li>Legal authorities when required by law</li>
            </ul>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">4. Data Security</h2>
            <p className="mb-4">
              We implement appropriate security measures to protect your personal information from unauthorized access,
              alteration, disclosure, or destruction. However, no method of transmission over the Internet or electronic
              storage is 100% secure.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">5. Your Rights</h2>
            <p className="mb-4">
              Depending on your location, you may have rights regarding your personal information, including:
            </p>
            <ul className="list-disc pl-6 mb-4">
              <li>Access to your personal information</li>
              <li>Correction of inaccurate information</li>
              <li>Deletion of your information</li>
              <li>Restriction of processing</li>
              <li>Data portability</li>
            </ul>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">6. Changes to This Policy</h2>
            <p className="mb-4">
              We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new
              Privacy Policy on this page and updating the "Last updated" date.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">7. Contact Us</h2>
            <p className="mb-4">
              If you have questions about this Privacy Policy, please contact <NAME_EMAIL>.
            </p>
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600">Last updated: March 16, 2025</p>
            <div className="mt-4">
              <Link href="/" className="text-blue-600 hover:text-blue-800">
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </BackgroundWrapper>
  )
}

