"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ArrowLeft, CheckCircle2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ProfileInfo } from "@/components/profile-info"
import { ProfileSecurity } from "@/components/profile-security"
import { BackgroundWrapper } from "@/components/background-wrapper"

export default function ProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("info")
  const [isSuccess, setIsSuccess] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")

  const handleSuccess = (message: string) => {
    setIsSuccess(true)
    setSuccessMessage(message)

    // Clear success message after 3 seconds
    setTimeout(() => {
      setIsSuccess(false)
      setSuccessMessage("")
    }, 3000)
  }

  return (
    <BackgroundWrapper>
      <div className="py-8 px-4 w-full">
        <div className="container mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-8 gap-4">
            <h1 className="text-3xl font-bold text-white">My Profile</h1>
            <Button
              variant="outline"
              className="bg-white/20 self-start sm:self-auto"
              onClick={() => router.push("/dashboard")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </div>

          {isSuccess && (
            <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <Card className="w-full">
            <CardHeader>
              <CardTitle>Profile Settings</CardTitle>
              <CardDescription>View and update your personal information</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="info" value={activeTab} onValueChange={setActiveTab}>
                <div className="overflow-x-auto">
                  <TabsList className="w-full mb-8 flex flex-nowrap">
                    <TabsTrigger value="info" className="flex-1">Personal Information</TabsTrigger>
                    <TabsTrigger value="security" className="flex-1">Security</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="info">
                  <ProfileInfo onSuccess={handleSuccess} />
                </TabsContent>

                <TabsContent value="security">
                  <ProfileSecurity onSuccess={handleSuccess} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </BackgroundWrapper>
  )
}

