"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AuthNavbar } from "@/components/auth-navbar"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { AnimatedButton, LabelInputContainer } from "@/components/ui/animated-button"
import { motion } from "framer-motion"
import { useAuth } from "@/lib/auth-context"
import { authApi } from "@/lib/api"
import { toast } from "sonner"

export default function RegisterPage() {
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const [formData, setFormData] = useState({
    name: "",
    studentId: "",
    email: "",
    phoneNumber: ""
  })
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [formState, setFormState] = useState<"idle" | "error" | "success">("idle")

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (isAuthenticated) {
      router.push("/dashboard")
    }
  }, [isAuthenticated, router])

  useEffect(() => {
    if (formState === "error") {
      const timer = setTimeout(() => setFormState("idle"), 600)
      return () => clearTimeout(timer)
    }
    if (formState === "success") {
      const timer = setTimeout(() => setFormState("idle"), 1500)
      return () => clearTimeout(timer)
    }
  }, [formState])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const validateForm = () => {
    if (!formData.name) {
      setError("Please enter your name")
      return false
    }
    
    if (!formData.studentId) {
      setError("Please enter your Student ID")
      return false
    }
    
    if (!formData.email) {
      setError("Please enter your email")
      return false
    }
    
    if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      setError("Please enter a valid email address")
      return false
    }
    
    if (!formData.phoneNumber) {
      setError("Please enter your phone number")
      return false
    }
    
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    
    if (!validateForm()) {
      setFormState("error")
      return
    }
    
    setIsLoading(true)

    try {
      // Add data logging to see what's being sent
      console.log('Submitting registration data:', {
        name: formData.name,
        userId: formData.studentId,
        email: formData.email,
        phoneNumber: formData.phoneNumber
      });
      
      const response = await authApi.register({
        name: formData.name,
        userId: formData.studentId,
        email: formData.email,
        phoneNumber: formData.phoneNumber
      });
      
      // Log the response for debugging
      console.log('Registration response:', response);
      
      if (response && response.success) {
        setFormState("success")
        toast.success("Registration successful! Please log in with your Student ID")
        toast.info("Use the default password: password123")
        
        // Redirect to login page after a brief delay
        setTimeout(() => {
          router.push("/login")
        }, 2000)
      } else {
        // Handle non-success response with details
        const message = response?.message || "Registration failed";
        console.error('Registration failed:', message);
        throw new Error(message);
      }
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(err.message || "Failed to register. Please try again.")
      setFormState("error")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <BackgroundWrapper>
      <AuthNavbar />

      <div className="flex-1 flex items-center justify-center px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card
            className={`bg-white/95 ${
              formState === "error" ? "error-animation" : formState === "success" ? "success-animation" : ""
            }`}
          >
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Create Account</CardTitle>
              <CardDescription className="text-center">Enter your information to register</CardDescription>
            </CardHeader>
            <CardContent>
              <form id="registrationForm" onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                
                <LabelInputContainer>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter your full name"
                    required
                  />
                </LabelInputContainer>
                
                <LabelInputContainer>
                  <Label htmlFor="studentId">Student ID</Label>
                  <Input
                    id="studentId"
                    name="studentId"
                    type="text"
                    value={formData.studentId}
                    onChange={handleChange}
                    placeholder="Enter your student ID"
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">You'll use this to log in</p>
                </LabelInputContainer>
                
                <LabelInputContainer>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                    required
                  />
                </LabelInputContainer>
                
                <LabelInputContainer>
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    name="phoneNumber"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={handleChange}
                    placeholder="Enter your phone number"
                    required
                  />
                </LabelInputContainer>
                
                <Alert className="bg-blue-50 text-blue-800 border-blue-200">
                  <div className="flex flex-col gap-1">
                    <p className="font-medium">Default Password Information</p>
                    <p className="text-sm">Your default password will be "password123"</p>
                    <p className="text-sm">You'll be prompted to change it on first login</p>
                  </div>
                </Alert>
              </form>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <AnimatedButton
                type="submit"
                form="registrationForm"
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
                variant="gradient"
              >
                {isLoading ? "Creating Account..." : "Create Account"}
              </AnimatedButton>
              <div className="text-center text-sm">
                Already have an account?{" "}
                <Link href="/login" className="text-blue-600 hover:underline">
                  Sign In
                </Link>
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </BackgroundWrapper>
  )
} 