import { BackgroundWrapper } from "@/components/background-wrapper"
import { MainNavbar } from "@/components/main-navbar"
import { CheckCircle } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

export default function ServicePage() {
  return (
    <BackgroundWrapper>
      <MainNavbar />
      <section className="py-16 bg-white/90" id="services">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center text-blue-900 mb-12">Our Services</h2>
          <div className="max-w-xl mx-auto">
            <Card>
              <CardHeader>
                <CardTitle>Personal Loans</CardTitle>
                <CardDescription>Quick access to funds for your personal needs</CardDescription>
              </CardHeader>
              <CardContent>
                <p>
                  Our personal loans offer competitive interest rates with flexible repayment options. Apply online and
                  get approved within minutes.
                </p>
                <ul className="mt-4 space-y-2">
                  {[
                    "Borrow up to E6000",
                    "Competitive interest rates",
                    "Flexible repayment terms",
                    "No hidden fees",
                  ].map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button asChild className="w-full bg-blue-600 hover:bg-blue-700">
                  <Link href="/login">Apply Now</Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>
    </BackgroundWrapper>
  )
}

