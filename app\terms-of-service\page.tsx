import Link from "next/link"
import { MainNavbar } from "@/components/main-navbar"
import { BackgroundWrapper } from "@/components/background-wrapper"

export default function TermsOfServicePage() {
  return (
    <BackgroundWrapper>
      <MainNavbar />
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto bg-white/90 rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-blue-900 mb-6">Terms of Service</h1>

          <div className="prose max-w-none">
            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">1. Acceptance of Terms</h2>
            <p className="mb-4">
              By accessing or using Umlamleli's services, you agree to be bound by these Terms of Service. If you do not
              agree to these terms, please do not use our services.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">2. Eligibility</h2>
            <p className="mb-4">
              To be eligible for our services, you must be at least 18 years old and capable of entering into a legally
              binding agreement. You must provide accurate and complete information during the registration process.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">3. Loan Services</h2>
            <p className="mb-4">
              Umlamleli provides loan services subject to our approval process. Interest rates, repayment terms, and
              fees are disclosed before loan approval. We reserve the right to approve or deny loan applications based
              on our evaluation criteria.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">4. Repayment</h2>
            <p className="mb-4">
              You agree to repay any loan according to the agreed-upon schedule. Late payments may result in additional
              fees and penalties as outlined in your loan agreement.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">5. Account Security</h2>
            <p className="mb-4">
              You are responsible for maintaining the confidentiality of your account credentials. Any activity that
              occurs under your account is your responsibility.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">6. Modification of Terms</h2>
            <p className="mb-4">
              Umlamleli reserves the right to modify these terms at any time. We will notify users of significant
              changes. Your continued use of our services after such modifications constitutes acceptance of the updated
              terms.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">7. Termination</h2>
            <p className="mb-4">
              We reserve the right to terminate or suspend your account at our discretion, particularly in cases of
              suspected fraud or violation of these terms.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">8. Governing Law</h2>
            <p className="mb-4">
              These terms are governed by and construed in accordance with the laws of the jurisdiction in which
              Umlamleli operates.
            </p>

            <h2 className="text-xl font-semibold text-blue-800 mt-6 mb-3">9. Contact Information</h2>
            <p className="mb-4">
              For questions regarding these Terms of Service, please contact <NAME_EMAIL>.
            </p>
          </div>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600">Last updated: March 16, 2025</p>
            <div className="mt-4">
              <Link href="/" className="text-blue-600 hover:text-blue-800">
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </BackgroundWrapper>
  )
}

