# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=loan_app

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Face API Configuration
FACE_API_MODELS_PATH=./models

# Mobile Money Integration
MOBILE_MONEY_API_KEY=your_api_key
MOBILE_MONEY_API_SECRET=your_api_secret
MOBILE_MONEY_WEBHOOK_SECRET=your_webhook_secret

# File Upload Configuration
MAX_FILE_SIZE=5242880 # 5MB
UPLOAD_DIR=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Payment Processing
# MTN Mobile Money integration has been removed
# Payment processing is now handled through database simulation