# Loan Application Platform Backend

A robust backend system for a loan application platform that integrates PostgreSQL for database management and face-api.js for liveness detection.

## Features

- User authentication and authorization
- Face verification using face-api.js
- Loan application and management
- Transaction processing
- Mobile money integration
- Admin dashboard
- Comprehensive logging and monitoring

## Prerequisites

- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=loan_app

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Face API Configuration
FACE_API_MODELS_PATH=./models

# Mobile Money Integration
MOBILE_MONEY_API_KEY=your_api_key
MOBILE_MONEY_API_SECRET=your_api_secret
MOBILE_MONEY_WEBHOOK_SECRET=your_webhook_secret

# File Upload Configuration
MAX_FILE_SIZE=5242880 # 5MB
UPLOAD_DIR=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Set up the database:
```bash
# Create the database
createdb loan_app

# Run migrations
npm run migration:run
```

4. Start the development server:
```bash
npm run dev
```

## Available Scripts

- `npm run dev`: Start the development server
- `npm run build`: Build the TypeScript code
- `npm start`: Start the production server
- `npm run lint`: Run ESLint
- `npm run lint:fix`: Fix ESLint issues
- `npm run format`: Format code with Prettier
- `npm run migration:generate`: Generate a new migration
- `npm run migration:run`: Run pending migrations
- `npm run migration:revert`: Revert the last migration

## Project Structure

```
src/
├── config/         # Configuration files
├── controllers/    # Route controllers
├── middleware/     # Custom middleware
├── models/         # Database models
├── routes/         # API routes
├── services/       # Business logic
├── types/          # TypeScript type definitions
├── utils/          # Utility functions
├── app.ts          # Express app setup
└── server.ts       # Server entry point
```

## API Documentation

The API documentation is available at `/api-docs` when running the server.

## Security Considerations

- All passwords are hashed using bcrypt
- JWT tokens are used for authentication
- Rate limiting is implemented for API endpoints
- Input validation is performed using express-validator
- CORS is configured for security
- Face verification data is encrypted
- Sensitive data is not logged

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the ISC License. 