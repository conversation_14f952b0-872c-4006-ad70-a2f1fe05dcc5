{"name": "backend", "version": "1.0.0", "description": "Backend for loan application platform with face verification", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d src/data-source.ts", "migration:run": "npm run typeorm -- migration:run -d src/data-source.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/data-source.ts", "create-admin": "node scripts/create-admin-user.js", "generate-hash": "node scripts/generate-password-hash.js", "update-admin-password": "node scripts/update-admin-password.js", "create-new-admin": "node scripts/create-new-admin.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.12", "@types/node": "^20.11.19", "@types/uuid": "^10.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.4", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.0", "pg": "^8.11.3", "reflect-metadata": "^0.2.1", "ts-node-dev": "^2.0.0", "twilio": "^5.5.2", "typeorm": "^0.3.20", "typescript": "^5.3.3", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/morgan": "^1.9.9", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "eslint": "^8.56.0", "prettier": "^3.2.5"}}