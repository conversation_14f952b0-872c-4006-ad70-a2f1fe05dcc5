# Admin User Management Scripts

This directory contains scripts to help manage admin users in the database.

## Prerequisites

Before running these scripts, make sure you have:

1. Built the backend with `npm run build`
2. Set up the database connection in your environment

## Available Scripts

### Generate Password Hash

Generates a bcrypt hash for the password "password123" that can be used in the database.

```bash
npm run generate-hash
```

### Update Admin Password

Updates the password of an existing admin user in the database to "password123" with a proper bcrypt hash.

```bash
npm run update-admin-password
```

### Create New Admin

Creates a new admin user with the username "Admin" and password "password123".

```bash
npm run create-new-admin
```

### Create Admin (Original Script)

The original script to create an admin user with username "admin" and password "admin123".

```bash
npm run create-admin
```

## Troubleshooting Admin Login

If you're having issues logging in as an admin, try the following steps:

1. Make sure the admin user exists in the database with the correct role ("admin" lowercase)
2. Make sure the admin user's status is "active" (lowercase)
3. Update the admin password using the `update-admin-password` script
4. If all else fails, create a new admin user using the `create-new-admin` script

## Login Credentials

After running the scripts, you can log in with:

- **Username**: The `fullName` of the admin user in the database
- **Password**: "password123" (or "admin123" for the original script)

Remember that the username in the UI maps to the `fullName` field in the database.
