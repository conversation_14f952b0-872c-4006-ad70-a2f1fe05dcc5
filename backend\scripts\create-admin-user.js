// Import required modules
const { AppDataSource } = require("../dist/data-source");
const { User } = require("../dist/models/User");
const bcrypt = require("bcryptjs");

async function createAdminUser() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection initialized");

    // Check if admin user already exists
    const existingAdmin = await AppDataSource.getRepository(User).findOne({
      where: { role: "admin" }
    });

    if (existingAdmin) {
      console.log("Admin user already exists with name:", existingAdmin.fullName);
      console.log("Admin user details:", {
        id: existingAdmin.id,
        fullName: existingAdmin.fullName,
        email: existingAdmin.email,
        role: existingAdmin.role,
        status: existingAdmin.status
      });
      await AppDataSource.destroy();
      return;
    }

    // Create a new admin user
    const adminUser = new User();
    adminUser.fullName = "admin"; // This will be the username in the UI
    adminUser.email = "<EMAIL>";
    adminUser.phoneNumber = "+1234567890";
    adminUser.password = await bcrypt.hash("admin123", 10); // Same as hardcoded password in UI
    adminUser.role = "admin"; // Make sure it's lowercase to match the enum
    adminUser.status = "active";
    adminUser.passwordChanged = true;
    adminUser.isEmailVerified = true;
    adminUser.isPhoneVerified = true;

    // Save the admin user to the database
    const savedAdmin = await AppDataSource.getRepository(User).save(adminUser);
    console.log("Admin user created successfully:", savedAdmin.fullName);
    console.log("Admin user details:", {
      id: savedAdmin.id,
      fullName: savedAdmin.fullName,
      email: savedAdmin.email,
      role: savedAdmin.role,
      status: savedAdmin.status
    });

    // Close the database connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
  } catch (error) {
    console.error("Error creating admin user:", error);
  }
}

// Run the function
createAdminUser();
