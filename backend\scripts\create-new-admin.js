// Import required modules
import { AppDataSource } from "../dist/data-source";
import { User } from "../dist/models/User";
import bcrypt from "bcryptjs";

async function createNewAdmin() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection initialized");

    // Check if an admin user with this name already exists
    const existingAdmin = await AppDataSource.getRepository(User).findOne({
      where: { fullName: "Admin" }
    });

    if (existingAdmin) {
      console.log("An admin user with the name 'Admin' already exists.");
      console.log("Admin user details:", {
        id: existingAdmin.id,
        fullName: existingAdmin.fullName,
        email: existingAdmin.email,
        role: existingAdmin.role,
        status: existingAdmin.status
      });

      // Ask if they want to update the password instead
      console.log("\nIf you want to update this admin's password, run the update-admin-password.js script instead.");

      await AppDataSource.destroy();
      return;
    }

    // Create a new admin user
    const adminUser = new User();
    adminUser.fullName = "Admin"; // This will be the username in the UI
    adminUser.email = "<EMAIL>";
    adminUser.phoneNumber = "+1234567890";

    // Generate a password hash
    const password = "password123"; // You can change this to your desired password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    adminUser.password = hashedPassword;

    // Set other required fields
    adminUser.role = "admin"; // Make sure it's lowercase to match the enum
    adminUser.status = "active";
    adminUser.passwordChanged = true;
    adminUser.isEmailVerified = true;
    adminUser.isPhoneVerified = true;

    // Save the admin user to the database
    const savedAdmin = await AppDataSource.getRepository(User).save(adminUser);
    console.log("New admin user created successfully:", savedAdmin.fullName);
    console.log("Admin user details:", {
      id: savedAdmin.id,
      fullName: savedAdmin.fullName,
      email: savedAdmin.email,
      role: savedAdmin.role,
      status: savedAdmin.status
    });
    console.log("\nYou can now log in with:");
    console.log("Username: Admin");
    console.log("Password: password123");

    // Close the database connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
  } catch (error) {
    console.error("Error creating new admin user:", error);
  }
}

// Run the function
createNewAdmin();
