// Import required modules
const { AppDataSource } = require("../dist/data-source");
const { User } = require("../dist/entities/user.entity");
const bcrypt = require("bcryptjs");

async function createTestUser() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection initialized");

    // Check if test user already exists
    const existingUser = await AppDataSource.getRepository(User).findOne({
      where: { email: "<EMAIL>" }
    });

    if (existingUser) {
      console.log("Test user already exists with email: <EMAIL>");
      await AppDataSource.destroy();
      return;
    }

    // Create a new user
    const user = new User();
    user.email = "<EMAIL>";
    user.name = "Test User";
    user.password = await bcrypt.hash("password123", 10);
    user.phoneNumber = "+1234567890";
    user.role = "USER";
    user.isVerified = true;
    user.passwordChanged = false;

    // Save the user to the database
    const savedUser = await AppDataSource.getRepository(User).save(user);
    console.log("Test user created successfully:", savedUser.email);

    // Close the database connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
  } catch (error) {
    console.error("Error creating test user:", error);
  }
}

// Run the function
createTestUser();