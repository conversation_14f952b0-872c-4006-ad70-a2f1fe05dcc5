// Import required modules
const bcrypt = require('bcryptjs');

async function generateHash() {
  try {
    // Define the password you want to use
    const password = 'password123';

    // Generate a salt and hash the password
    const salt = await bcrypt.genSalt(10);
    const hash = await bcrypt.hash(password, salt);

    console.log('Generated hash for password123:');
    console.log(hash);
    console.log('\nYou can use this hash to update the admin user password in the database.');
    console.log('The password for this hash is: password123');
  } catch (error) {
    console.error('Error generating hash:', error);
  }
}

// Run the function
generateHash();
