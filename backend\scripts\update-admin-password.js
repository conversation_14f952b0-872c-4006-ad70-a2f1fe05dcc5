// Import required modules
import { AppDataSource } from "../dist/data-source";
import { User } from "../dist/models/User";
import bcrypt from "bcryptjs";

async function updateAdminPassword() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection initialized");

    // Find the admin user
    const adminUser = await AppDataSource.getRepository(User).findOne({
      where: { role: "admin" }
    });

    if (!adminUser) {
      console.log("No admin user found in the database.");
      await AppDataSource.destroy();
      return;
    }

    console.log("Found admin user:", {
      id: adminUser.id,
      fullName: adminUser.fullName,
      email: adminUser.email,
      role: adminUser.role,
      status: adminUser.status
    });

    // Generate a new password hash
    const password = "password123"; // You can change this to your desired password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update the admin user's password
    adminUser.password = hashedPassword;
    adminUser.passwordChanged = true;

    // Save the updated admin user
    const updatedAdmin = await AppDataSource.getRepository(User).save(adminUser);
    console.log("Admin password updated successfully for:", updatedAdmin.fullName);
    console.log("New password is: password123"); // Make sure this matches the password above

    // Close the database connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
  } catch (error) {
    console.error("Error updating admin password:", error);
  }
}

// Run the function
updateAdminPassword();

