import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service';
import { UserRole } from '../models/User';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  register = async (req: Request, res: Response): Promise<void> => {
    try {
      const { name, userId, email, phoneNumber, password } = req.body;

      // Log received data for debugging
      console.log("Registration data received:", {
        name,
        userId,
        email,
        phoneNumber,
        hasPassword: !!password
      });

      // Validate required fields
      if (!name || !userId || !email || !phoneNumber) {
        res.status(400).json({
          success: false,
          message: 'Name, Student ID, email, and phone number are required',
        });
        return;
      }

      const { user, token } = await this.authService.register({
        fullName: name,
        studentId: userId,
        email,
        phoneNumber,
        password,
      });

      res.status(201).json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.fullName,
            userId: user.studentId,
            email: user.email,
            phoneNumber: user.phoneNumber,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            isFaceVerified: user.isFaceVerified,
            profileImage: user.profileImage,
            faceDescriptor: user.faceDescriptor // Added face descriptor field
          },
          token,
        },
      });
    } catch (error) {
      console.error("Registration error:", error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Registration failed',
      });
    }
  };

  login = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId, password } = req.body;

      // Log received data for debugging
      console.log("Login request received:", {
        userId,
        hasPassword: !!password
      });

      // Validate required fields
      if (!userId || !password) {
        res.status(400).json({
          success: false,
          message: 'Student ID and password are required',
        });
        return;
      }

      // Try login with student ID first, then fall back to email
      const { user, token, passwordChangeRequired, message } = await this.authService.login(userId, password);

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.fullName,
            email: user.email,
            phoneNumber: user.phoneNumber,
            role: user.role,
            status: user.status,
            userId: user.studentId,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            isFaceVerified: user.isFaceVerified,
            passwordChanged: user.passwordChanged,
            profileImage: user.profileImage,
            faceDescriptor: user.faceDescriptor // Added face descriptor field
          },
          token,
          passwordChangeRequired,
          message
        },
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(401).json({
        success: false,
        message: error instanceof Error ? error.message : 'Login failed',
      });
    }
  };

  changePassword = async (req: Request, res: Response): Promise<void> => {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      if (!currentPassword || !newPassword) {
        res.status(400).json({
          success: false,
          message: 'Current password and new password are required',
        });
        return;
      }

      await this.authService.changePassword(userId, currentPassword, newPassword);

      res.json({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to change password',
      });
    }
  };

  verifyEmail = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      await this.authService.verifyEmail(userId);

      res.json({
        success: true,
        message: 'Email verified successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Email verification failed',
      });
    }
  };

  verifyPhone = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      await this.authService.verifyPhone(userId);

      res.json({
        success: true,
        message: 'Phone number verified successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Phone verification failed',
      });
    }
  };

  verifyFace = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const { faceDescriptor } = req.body;

      if (!faceDescriptor) {
        res.status(400).json({
          success: false,
          message: 'Face descriptor is required',
        });
        return;
      }

      await this.authService.verifyFace(userId, faceDescriptor);

      res.json({
        success: true,
        message: 'Face verification completed successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Face verification failed',
      });
    }
  };

  // Create admin user endpoint (temporary)
  createAdmin = async (req: Request, res: Response): Promise<void> => {
    try {
      const { fullName, email, phoneNumber, password, role, status } = req.body;

      // Log received data for debugging
      console.log("Create admin request received:", {
        fullName,
        email,
        phoneNumber,
        hasPassword: !!password,
        role,
        status
      });

      // Validate required fields
      if (!fullName || !email || !phoneNumber || !password) {
        res.status(400).json({
          success: false,
          message: 'All fields are required',
        });
        return;
      }

      // Call the create admin service method
      const { user, token } = await this.authService.createAdmin(fullName, email, phoneNumber, password);

      res.status(201).json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.fullName,
            email: user.email,
            phoneNumber: user.phoneNumber,
            role: user.role,
            status: user.status,
            userId: user.studentId,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            isFaceVerified: user.isFaceVerified,
            passwordChanged: user.passwordChanged,
            profileImage: user.profileImage
          },
          token
        },
        message: 'Admin user created successfully',
      });
    } catch (error) {
      console.error("Create admin error:", error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create admin user',
      });
    }
  };

  // Admin login endpoint
  adminLogin = async (req: Request, res: Response): Promise<void> => {
    try {
      const { username, password } = req.body;

      // Log received data for debugging
      console.log("Admin login request received:", {
        username,
        hasPassword: !!password
      });

      // Validate required fields
      if (!username || !password) {
        res.status(400).json({
          success: false,
          message: 'Username and password are required',
        });
        return;
      }

      // Call the admin login service method
      const { user, token } = await this.authService.adminLogin(username, password);

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.fullName,
            email: user.email,
            phoneNumber: user.phoneNumber,
            role: user.role,
            status: user.status,
            userId: user.studentId,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            isFaceVerified: user.isFaceVerified,
            passwordChanged: user.passwordChanged,
            profileImage: user.profileImage
          },
          token
        },
      });
    } catch (error) {
      console.error("Admin login error:", error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Admin login failed',
      });
    }
  };
}