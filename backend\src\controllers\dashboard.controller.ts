import { Request, Response } from 'express';
import { DashboardService } from '../services/dashboard.service';
import { UserRole } from '../models/User';

export class DashboardController {
  private dashboardService: DashboardService;

  constructor() {
    this.dashboardService = new DashboardService();
  }

  // Get comprehensive dashboard statistics (admin only)
  getDashboardStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const statistics = await this.dashboardService.getDashboardStatistics();

      res.json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      console.error('Error getting dashboard statistics:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get dashboard statistics',
      });
    }
  };

  // Get recent transactions for dashboard (admin only)
  getRecentTransactions = async (req: Request, res: Response): Promise<void> => {
    try {
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const limit = parseInt(req.query.limit as string) || 10;
      const transactions = await this.dashboardService.getRecentTransactions(limit);

      res.json({
        success: true,
        data: transactions,
      });
    } catch (error) {
      console.error('Error getting recent transactions:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get recent transactions',
      });
    }
  };

  // Get dashboard overview (combines stats and recent transactions)
  getDashboardOverview = async (req: Request, res: Response): Promise<void> => {
    try {
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const [statistics, recentTransactions] = await Promise.all([
        this.dashboardService.getDashboardStatistics(),
        this.dashboardService.getRecentTransactions(5), // Get 5 recent transactions for overview
      ]);

      res.json({
        success: true,
        data: {
          statistics,
          recentTransactions,
          lastUpdated: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Error getting dashboard overview:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get dashboard overview',
      });
    }
  };
}
