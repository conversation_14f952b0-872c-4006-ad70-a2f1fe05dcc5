import { Request, Response } from 'express';
import { DocumentService } from '../services/document.service';
import { DocumentStatus, DocumentType } from '../models/Document';
import { UserRole } from '../models/User';
import multer from 'multer';
import * as path from 'path';

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB limit
  },
});

// File type validation
const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const allowedDocumentTypes = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
  'text/plain',
];

export class DocumentController {
  private documentService: DocumentService;

  constructor() {
    this.documentService = new DocumentService();
  }

  // Middleware for file upload
  uploadMiddleware = upload.single('file');

  // Get all documents (admin only)
  getAllDocuments = async (req: Request, res: Response): Promise<void> => {
    try {
      const documents = await this.documentService.getAllDocuments();
      
      // Map documents to a more frontend-friendly format
      const formattedDocuments = documents.map(doc => ({
        id: doc.id,
        userId: doc.userId,
        userName: doc.user?.fullName || 'Unknown User',
        loanId: doc.loanId,
        documentType: doc.documentType,
        fileName: doc.originalFileName,
        fileSize: this.formatFileSize(doc.fileSize),
        mimeType: doc.mimeType,
        documentStatus: doc.documentStatus,
        uploadedBy: doc.uploadedBy?.fullName || 'Unknown',
        uploadDate: doc.uploadDate,
        statusUpdatedAt: doc.statusUpdatedAt,
        statusUpdatedBy: doc.statusUpdatedBy?.fullName,
        rejectionReason: doc.rejectionReason,
      }));
      
      res.status(200).json({
        success: true,
        data: formattedDocuments,
      });
    } catch (error) {
      console.error('Error getting all documents:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve documents',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Get documents by user ID
  getDocumentsByUserId = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      
      // Check if the user is requesting their own documents or is an admin
      const requestingUserId = req.user?.id;
      const userRole = req.user?.role;
      
      if (userId !== requestingUserId && userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF) {
        res.status(403).json({
          success: false,
          message: 'You are not authorized to access these documents',
        });
        return;
      }
      
      const documents = await this.documentService.getDocumentsByUserId(userId);
      
      // Map documents to a more frontend-friendly format
      const formattedDocuments = documents.map(doc => ({
        id: doc.id,
        documentType: doc.documentType,
        fileName: doc.originalFileName,
        fileSize: this.formatFileSize(doc.fileSize),
        mimeType: doc.mimeType,
        documentStatus: doc.documentStatus,
        uploadDate: doc.uploadDate,
        loanId: doc.loanId,
      }));
      
      res.status(200).json({
        success: true,
        data: formattedDocuments,
      });
    } catch (error) {
      console.error('Error getting documents by user ID:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve documents',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Get documents by loan ID
  getDocumentsByLoanId = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const documents = await this.documentService.getDocumentsByLoanId(loanId);
      
      // Map documents to a more frontend-friendly format
      const formattedDocuments = documents.map(doc => ({
        id: doc.id,
        userId: doc.userId,
        userName: doc.user?.fullName || 'Unknown User',
        documentType: doc.documentType,
        fileName: doc.originalFileName,
        fileSize: this.formatFileSize(doc.fileSize),
        mimeType: doc.mimeType,
        documentStatus: doc.documentStatus,
        uploadedBy: doc.uploadedBy?.fullName || 'Unknown',
        uploadDate: doc.uploadDate,
      }));
      
      res.status(200).json({
        success: true,
        data: formattedDocuments,
      });
    } catch (error) {
      console.error('Error getting documents by loan ID:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve documents',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Get document by ID
  getDocumentById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { documentId } = req.params;
      const document = await this.documentService.getDocumentById(documentId);
      
      if (!document) {
        res.status(404).json({
          success: false,
          message: 'Document not found',
        });
        return;
      }
      
      // Check if the user is authorized to access this document
      const requestingUserId = req.user?.id;
      const userRole = req.user?.role;
      
      if (document.userId !== requestingUserId && 
          userRole !== UserRole.ADMIN && 
          userRole !== UserRole.STAFF) {
        res.status(403).json({
          success: false,
          message: 'You are not authorized to access this document',
        });
        return;
      }
      
      // Format document for frontend
      const formattedDocument = {
        id: document.id,
        userId: document.userId,
        userName: document.user?.fullName || 'Unknown User',
        loanId: document.loanId,
        documentType: document.documentType,
        fileName: document.originalFileName,
        fileSize: this.formatFileSize(document.fileSize),
        mimeType: document.mimeType,
        documentStatus: document.documentStatus,
        uploadedBy: document.uploadedBy?.fullName || 'Unknown',
        uploadDate: document.uploadDate,
        statusUpdatedAt: document.statusUpdatedAt,
        statusUpdatedBy: document.statusUpdatedBy?.fullName,
        rejectionReason: document.rejectionReason,
      };
      
      res.status(200).json({
        success: true,
        data: formattedDocument,
      });
    } catch (error) {
      console.error('Error getting document by ID:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve document',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Upload a new document
  uploadDocument = async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: 'No file uploaded',
        });
        return;
      }
      
      const { documentType, userId, loanId } = req.body;
      
      // Validate document type
      if (!Object.values(DocumentType).includes(documentType)) {
        res.status(400).json({
          success: false,
          message: 'Invalid document type',
        });
        return;
      }
      
      // Validate file type based on document type
      if (!this.isValidFileType(req.file.mimetype, documentType)) {
        res.status(400).json({
          success: false,
          message: 'Invalid file type for the selected document type',
        });
        return;
      }
      
      // Determine the user ID for the document
      // If admin is uploading for a user, use the provided userId
      // Otherwise, use the current user's ID
      const targetUserId = (req.user?.role === UserRole.ADMIN || req.user?.role === UserRole.STAFF) && userId
        ? userId
        : req.user?.id;
      
      if (!targetUserId) {
        res.status(400).json({
          success: false,
          message: 'User ID is required',
        });
        return;
      }
      
      // Create the document
      const document = await this.documentService.createDocument(
        req.file,
        documentType as DocumentType,
        targetUserId,
        req.user?.id as string,
        loanId,
      );
      
      res.status(201).json({
        success: true,
        message: 'Document uploaded successfully',
        data: {
          id: document.id,
          documentType: document.documentType,
          fileName: document.originalFileName,
          fileSize: this.formatFileSize(document.fileSize),
          uploadDate: document.uploadDate,
        },
      });
    } catch (error) {
      console.error('Error uploading document:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to upload document',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Update document status
  updateDocumentStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { documentId } = req.params;
      const { status, rejectionReason } = req.body;
      
      // Validate status
      if (!Object.values(DocumentStatus).includes(status)) {
        res.status(400).json({
          success: false,
          message: 'Invalid document status',
        });
        return;
      }
      
      // Update document status
      const document = await this.documentService.updateDocumentStatus(
        documentId,
        status as DocumentStatus,
        req.user?.id as string,
        rejectionReason,
      );
      
      if (!document) {
        res.status(404).json({
          success: false,
          message: 'Document not found',
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        message: 'Document status updated successfully',
        data: {
          id: document.id,
          documentStatus: document.documentStatus,
          statusUpdatedAt: document.statusUpdatedAt,
        },
      });
    } catch (error) {
      console.error('Error updating document status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update document status',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Delete document
  deleteDocument = async (req: Request, res: Response): Promise<void> => {
    try {
      const { documentId } = req.params;
      
      // Check if document exists and user is authorized to delete it
      const document = await this.documentService.getDocumentById(documentId);
      
      if (!document) {
        res.status(404).json({
          success: false,
          message: 'Document not found',
        });
        return;
      }
      
      // Only admin, staff, or the document owner can delete it
      const requestingUserId = req.user?.id;
      const userRole = req.user?.role;
      
      if (document.userId !== requestingUserId && 
          document.uploadedById !== requestingUserId && 
          userRole !== UserRole.ADMIN && 
          userRole !== UserRole.STAFF) {
        res.status(403).json({
          success: false,
          message: 'You are not authorized to delete this document',
        });
        return;
      }
      
      // Delete the document
      const deleted = await this.documentService.deleteDocument(documentId);
      
      if (!deleted) {
        res.status(500).json({
          success: false,
          message: 'Failed to delete document',
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        message: 'Document deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting document:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete document',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Download document
  downloadDocument = async (req: Request, res: Response): Promise<void> => {
    try {
      const { documentId } = req.params;
      
      // Get document file
      const documentFile = await this.documentService.getDocumentFile(documentId);
      
      if (!documentFile) {
        res.status(404).json({
          success: false,
          message: 'Document file not found',
        });
        return;
      }
      
      // Set response headers
      res.setHeader('Content-Type', documentFile.mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${documentFile.fileName}"`);
      
      // Send file
      res.send(documentFile.buffer);
    } catch (error) {
      console.error('Error downloading document:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to download document',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Helper method to format file size
  private formatFileSize(bytes: number): string {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }

  // Helper method to validate file type
  private isValidFileType(mimeType: string, documentType: string): boolean {
    if (documentType === DocumentType.ID || documentType === DocumentType.COLLATERAL) {
      return allowedImageTypes.includes(mimeType);
    } else {
      return allowedDocumentTypes.includes(mimeType) || allowedImageTypes.includes(mimeType);
    }
  }
}
