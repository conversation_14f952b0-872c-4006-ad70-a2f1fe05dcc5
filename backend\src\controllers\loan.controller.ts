import { Request, Response } from 'express';
import { LoanService } from '../services/loan.service';
import { UserRole } from '../models/User';
import { LoanStatus } from '../models/Loan';
import { TransactionService } from '../services/transaction.service';

export class LoanController {
  private loanService: LoanService;
  private transactionService: TransactionService;

  constructor() {
    this.loanService = new LoanService();
    this.transactionService = new TransactionService();
  }

  // Apply for a loan
  applyForLoan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { amount, purpose, termInMonths, collateral } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      // Validate required fields
      if (!amount || !purpose || !termInMonths) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields',
        });
        return;
      }

      const loan = await this.loanService.applyForLoan(userId, {
        amount: parseFloat(amount),
        purpose,
        termInMonths: parseInt(termInMonths),
        collateral, // Pass the collateral field to the service
      });

      res.status(201).json({
        success: true,
        data: loan,
        message: 'Loan application submitted successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to submit loan application',
      });
    }
  };

  // Get all user loans with optional filtering
  getUserLoans = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      // Extract filter parameters from query
      const {
        status,
        minAmount,
        maxAmount,
        startDate,
        endDate,
        search
      } = req.query;

      // Check if any filters are provided
      const hasFilters = status || minAmount || maxAmount || startDate || endDate || search;

      let loans;
      if (hasFilters) {
        // Create filter object
        const filters: Record<string, string | number | boolean> = {};

        // Ensure status is a valid enum value
        if (status && Object.values(LoanStatus).includes(status as LoanStatus)) {
          filters.status = status as string;
        }
        if (minAmount) filters.minAmount = parseFloat(minAmount as string);
        if (maxAmount) filters.maxAmount = parseFloat(maxAmount as string);
        if (startDate) filters.startDate = startDate as string;
        if (endDate) filters.endDate = endDate as string;
        if (search) filters.search = search as string;

        // Get filtered loans
        loans = await this.loanService.getUserLoansFiltered(userId, filters);
      } else {
        // Get all loans without filtering
        loans = await this.loanService.getUserLoans(userId);
      }

      res.json({
        success: true,
        data: loans,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get loans',
      });
    }
  };

  // Get loan by ID
  getLoanById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      // If user is not admin, only allow them to view their own loans
      const loan = userRole === UserRole.ADMIN || userRole === UserRole.STAFF
        ? await this.loanService.getLoanById(loanId)
        : await this.loanService.getLoanById(loanId, userId);

      res.json({
        success: true,
        data: loan,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get loan',
      });
    }
  };

  // Get all loans (admin only)
  getAllLoans = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      // Get query parameters for filtering
      const { status, search, page = '1', limit = '10' } = req.query;
      const pageNum = parseInt(page as string, 10);
      const limitNum = parseInt(limit as string, 10);

      const loans = await this.loanService.getAllLoans({
        status: status as string,
        search: search as string,
        page: pageNum,
        limit: limitNum
      });

      res.json({
        success: true,
        data: loans.data,
        pagination: loans.pagination
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get loans',
      });
    }
  };

  // Get all pending loans (admin only)
  getPendingLoans = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const loans = await this.loanService.getPendingLoans();

      res.json({
        success: true,
        data: loans,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get pending loans',
      });
    }
  };

  // Approve a loan (admin only)
  approveLoan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const loan = await this.loanService.approveLoan(loanId, userId);

      res.json({
        success: true,
        data: loan,
        message: 'Loan approved successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to approve loan',
      });
    }
  };

  // Reject a loan (admin only)
  rejectLoan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const { rejectionReason, reason } = req.body; // Accept both rejectionReason and reason
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      // Use rejectionReason or reason or empty string if neither is provided
      const finalReason = rejectionReason || reason || '';

      const loan = await this.loanService.rejectLoan(loanId, userId, finalReason);

      res.json({
        success: true,
        data: loan,
        message: 'Loan rejected successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to reject loan',
      });
    }
  };

  // Disburse a loan (admin only)
  disburseLoan = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const loan = await this.loanService.disburseLoan(loanId, userId);

      res.json({
        success: true,
        data: loan,
        message: 'Loan disbursed successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to disburse loan',
      });
    }
  };

  // Make a loan repayment
  makeRepayment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const { amount, payerPhoneNumber } = req.body;
      const userId = req.user?.id;

      console.log('🔍 makeRepayment controller received:', {
        loanId,
        amount,
        amountType: typeof amount,
        payerPhoneNumber,
        userId,
        fullBody: req.body
      });

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      if (!amount) {
        res.status(400).json({
          success: false,
          message: 'Payment amount is required',
        });
        return;
      }

      const parsedAmount = parseFloat(amount);
      console.log('🔍 Parsed amount:', {
        original: amount,
        parsed: parsedAmount,
        parsedType: typeof parsedAmount
      });

      const result = await this.loanService.makeRepayment(
        loanId,
        userId,
        parsedAmount,
        payerPhoneNumber
      );

      res.json({
        success: true,
        data: result,
        message: 'Payment made successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to make payment',
      });
    }
  };

  // Get loan statistics for a user
  getUserLoanStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      const statistics = await this.loanService.getUserLoanStatistics(userId);

      res.json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get loan statistics',
      });
    }
  };

  // Get all active loans for a user
  getActiveLoans = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      const loans = await this.loanService.getUserActiveLoans(userId);

      res.json({
        success: true,
        data: loans,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get active loans',
      });
    }
  };

  // Get user's available credit
  getAvailableCredit = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      // Get credit information with proper number formatting
      const availableCredit = await this.loanService.calculateAvailableCredit(userId);
      const outstandingAmount = await this.loanService.calculateOutstandingLoanAmount(userId);

      // Ensure we're sending numeric values
      res.json({
        success: true,
        data: {
          availableCredit: Number(availableCredit),
          outstandingAmount: Number(outstandingAmount),
          maxCreditLimit: 600 // E600 maximum lifetime loan limit
        },
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get available credit',
      });
    }
  };

  // Get loan payment schedule
  getLoanPaymentSchedule = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      // Get loan details
      const loan = await this.loanService.getLoanById(loanId, userId);

      // Get all transactions related to this loan
      const transactions = await this.loanService.getLoanTransactions(loanId);

      // Calculate payment schedule
      const paymentSchedule = await this.loanService.calculatePaymentSchedule(loan, transactions);

      res.json({
        success: true,
        data: paymentSchedule,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get payment schedule',
      });
    }
  };

  // Calculate loan health score
  getLoanHealthScore = async (req: Request, res: Response): Promise<void> => {
    try {
      const { loanId } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      // Get loan health score
      const healthScore = await this.loanService.calculateLoanHealthScore(loanId, userId);

      res.json({
        success: true,
        data: healthScore,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to calculate loan health score',
      });
    }
  };

  // Get simulated account balance
  getSimulatedBalance = async (req: Request, res: Response): Promise<void> => {
    try {
      const balance = await this.loanService.getSimulatedAccountBalance();

      res.json({
        success: true,
        data: balance,
        message: 'Account balance retrieved successfully',
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get account balance',
      });
    }
  };

  // Webhook to handle transaction status updates
  handleTransactionWebhook = async (req: Request, res: Response): Promise<void> => {
    try {
      const { transactionId, status } = req.body;

      if (!transactionId || !status) {
        res.status(400).json({
          success: false,
          message: 'Missing transactionId or status',
        });
        return;
      }

      // Update transaction status in the database
      await this.transactionService.updateTransactionStatus(transactionId, status);

      res.status(200).json({
        success: true,
        message: 'Transaction status updated successfully',
      });
    } catch (error) {
      const errorMessage = (error instanceof Error) ? error.message : 'Unknown error';
      res.status(500).json({
        success: false,
        message: 'Failed to update transaction status',
        error: errorMessage,
      });
    }
  };
}