import { Request, Response } from 'express';
import { NotificationService } from '../services/notification.service';
import { NotificationStatus, NotificationType } from '../models/Notification';
import { UserRole } from '../models/User';

export class NotificationController {
  private notificationService = new NotificationService();

  // Create a notification (admin only)
  createNotification = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const { title, message, type, status, recipientId, isAllUsers, scheduledFor } = req.body;

      // Validate required fields
      if (!title || !message) {
        res.status(400).json({
          success: false,
          message: 'Title and message are required',
        });
        return;
      }

      // Validate notification type
      if (type && !Object.values(NotificationType).includes(type)) {
        res.status(400).json({
          success: false,
          message: 'Invalid notification type',
        });
        return;
      }

      // Validate notification status
      if (status && !Object.values(NotificationStatus).includes(status)) {
        res.status(400).json({
          success: false,
          message: 'Invalid notification status',
        });
        return;
      }

      // If scheduled, validate scheduledFor date
      if (status === NotificationStatus.SCHEDULED && !scheduledFor) {
        res.status(400).json({
          success: false,
          message: 'Scheduled date is required for scheduled notifications',
        });
        return;
      }

      const notification = await this.notificationService.createNotification({
        title,
        message,
        type: type || NotificationType.SYSTEM,
        status: status || NotificationStatus.DRAFT,
        recipientId,
        isAllUsers: isAllUsers || false,
        scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined,
        createdById: userId,
      });

      res.status(201).json({
        success: true,
        data: notification,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create notification',
      });
    }
  };

  // Get all notifications (admin only)
  getAllNotifications = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const { page, limit, type, status, search } = req.query;

      const result = await this.notificationService.getAllNotifications({
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        type: type as NotificationType,
        status: status as NotificationStatus,
        search: search as string,
      });

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get notifications',
      });
    }
  };

  // Get notification by ID (admin only)
  getNotificationById = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const { notificationId } = req.params;
      const notification = await this.notificationService.getNotificationById(notificationId);

      if (!notification) {
        res.status(404).json({
          success: false,
          message: 'Notification not found',
        });
        return;
      }

      res.json({
        success: true,
        data: notification,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get notification',
      });
    }
  };

  // Update notification (admin only)
  updateNotification = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const { notificationId } = req.params;
      const { title, message, type, status, recipientId, isAllUsers, scheduledFor } = req.body;

      // Validate notification type
      if (type && !Object.values(NotificationType).includes(type)) {
        res.status(400).json({
          success: false,
          message: 'Invalid notification type',
        });
        return;
      }

      // Validate notification status
      if (status && !Object.values(NotificationStatus).includes(status)) {
        res.status(400).json({
          success: false,
          message: 'Invalid notification status',
        });
        return;
      }

      // If scheduled, validate scheduledFor date
      if (status === NotificationStatus.SCHEDULED && !scheduledFor) {
        res.status(400).json({
          success: false,
          message: 'Scheduled date is required for scheduled notifications',
        });
        return;
      }

      const notification = await this.notificationService.updateNotification(notificationId, {
        title,
        message,
        type,
        status,
        recipientId,
        isAllUsers,
        scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined,
      });

      if (!notification) {
        res.status(404).json({
          success: false,
          message: 'Notification not found',
        });
        return;
      }

      res.json({
        success: true,
        data: notification,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update notification',
      });
    }
  };

  // Delete notification (admin only)
  deleteNotification = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const { notificationId } = req.params;
      const success = await this.notificationService.deleteNotification(notificationId);

      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Notification not found',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Notification deleted successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete notification',
      });
    }
  };

  // Send notification now (admin only)
  sendNotificationNow = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const { notificationId } = req.params;
      const notification = await this.notificationService.updateNotification(notificationId, {
        status: NotificationStatus.SENT,
        scheduledFor: new Date(),
      });

      if (!notification) {
        res.status(404).json({
          success: false,
          message: 'Notification not found',
        });
        return;
      }

      res.json({
        success: true,
        data: notification,
        message: 'Notification sent successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send notification',
      });
    }
  };

  // Get user notifications (for current user)
  getUserNotifications = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Authentication required',
        });
        return;
      }

      const { page, limit, read } = req.query;
      const isAdmin = userRole === UserRole.ADMIN || userRole === UserRole.STAFF;

      const result = await this.notificationService.getUserNotifications(userId, {
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        read: read === 'true' ? true : read === 'false' ? false : undefined,
        isAdmin,
      });

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get notifications',
      });
    }
  };

  // Mark notification as read
  markAsRead = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Authentication required',
        });
        return;
      }

      const { notificationId } = req.params;
      const success = await this.notificationService.markAsRead(notificationId);

      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Notification not found',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Notification marked as read',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to mark notification as read',
      });
    }
  };

  // Mark all notifications as read
  markAllAsRead = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Authentication required',
        });
        return;
      }

      const success = await this.notificationService.markAllAsRead(userId);

      res.json({
        success: true,
        message: 'All notifications marked as read',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to mark all notifications as read',
      });
    }
  };

  // Get admin notifications (admin only)
  getAdminNotifications = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const { page, limit, read } = req.query;

      const result = await this.notificationService.getAdminNotifications({
        page: page ? parseInt(page as string, 10) : undefined,
        limit: limit ? parseInt(limit as string, 10) : undefined,
        read: read === 'true' ? true : read === 'false' ? false : undefined,
      });

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get admin notifications',
      });
    }
  };
}
