import { Request, Response } from 'express';
import { TwilioVerifyService } from '../services/twilio-verify.service';

export class OtpController {
  private verifyService: TwilioVerifyService;

  constructor() {
    this.verifyService = new TwilioVerifyService();
  }

  /**
   * Send verification code to a phone number
   */
  sendOtp = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { phoneNumber } = req.body;

      console.log(`Verification code send request received for user ${userId} and phone ${phoneNumber}`);

      if (!userId) {
        console.log('Authentication required for verification code send');
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      if (!phoneNumber) {
        console.log('Phone number is required for verification code send');
        res.status(400).json({
          success: false,
          message: 'Phone number is required',
        });
        return;
      }

      console.log(`Calling Twilio service to send SMS code to ${phoneNumber}`);
      const result = await this.verifyService.sendVerificationCode(phoneNumber);
      console.log(`Twilio service result:`, result);

      if (result.success) {
        console.log(`Verification code sent successfully to ${phoneNumber}`);
        res.status(200).json({
          success: true,
          message: result.message,
        });
      } else {
        console.log(`Failed to send verification code to ${phoneNumber}: ${result.message}`);
        res.status(400).json({
          success: false,
          message: result.message,
        });
      }
    } catch (error) {
      console.error('Error in sendOtp controller:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send verification code',
        details: error instanceof Error ? error.stack : undefined,
      });
    }
  };

  /**
   * Verify code
   */
  verifyOtp = async (req: Request, res: Response): Promise<void> => {
    try {
      const { phoneNumber, otpCode } = req.body;

      console.log(`Verification request received for phone ${phoneNumber} with code ${otpCode}`);

      if (!phoneNumber) {
        console.log('Phone number is required for verification');
        res.status(400).json({
          success: false,
          message: 'Phone number is required',
        });
        return;
      }

      if (!otpCode) {
        console.log('Verification code is required');
        res.status(400).json({
          success: false,
          message: 'Verification code is required',
        });
        return;
      }

      console.log(`Calling Twilio Verify service to verify code ${otpCode} for phone ${phoneNumber}`);
      const result = await this.verifyService.verifyCode(phoneNumber, otpCode);
      console.log(`Verification result:`, result);

      if (result.success) {
        console.log(`Phone number verified successfully: ${phoneNumber}`);
        res.status(200).json({
          success: true,
          message: result.message,
        });
      } else {
        console.log(`Failed to verify phone number ${phoneNumber}: ${result.message}`);
        res.status(400).json({
          success: false,
          message: result.message,
        });
      }
    } catch (error) {
      console.error('Error in verifyOtp controller:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to verify code',
        details: error instanceof Error ? error.stack : undefined,
      });
    }
  };

  /**
   * Resend verification code to a phone number
   */
  resendOtp = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { phoneNumber } = req.body;

      console.log(`Verification code resend request received for user ${userId} and phone ${phoneNumber}`);

      if (!userId) {
        console.log('Authentication required for verification code resend');
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      if (!phoneNumber) {
        console.log('Phone number is required for verification code resend');
        res.status(400).json({
          success: false,
          message: 'Phone number is required',
        });
        return;
      }

      console.log(`Calling Twilio service to resend SMS code to ${phoneNumber}`);
      const result = await this.verifyService.sendVerificationCode(phoneNumber);
      console.log(`Twilio service result:`, result);

      if (result.success) {
        console.log(`Verification code resent successfully to ${phoneNumber}`);
        res.status(200).json({
          success: true,
          message: result.message,
        });
      } else {
        console.log(`Failed to resend verification code to ${phoneNumber}: ${result.message}`);
        res.status(400).json({
          success: false,
          message: result.message,
        });
      }
    } catch (error) {
      console.error('Error in resendOtp controller:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to resend verification code',
        details: error instanceof Error ? error.stack : undefined,
      });
    }
  };
}
