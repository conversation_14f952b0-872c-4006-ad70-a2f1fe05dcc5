import { Request, Response } from 'express';
import { ReportsService } from '../services/reports.service';
import { UserRole } from '../models/User';

export class ReportsController {
  private reportsService: ReportsService;

  constructor() {
    this.reportsService = new ReportsService();
  }

  // Get all reports for a specific period
  getAllReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = 'month' } = req.query;
      
      // Validate period
      const validPeriods = ['week', 'month', 'quarter', 'year'];
      if (!validPeriods.includes(period as string)) {
        res.status(400).json({
          success: false,
          message: 'Invalid period. Must be one of: week, month, quarter, year',
        });
        return;
      }

      const reports = await this.reportsService.getAllReports(period as string);

      res.json({
        success: true,
        data: reports,
      });
    } catch (error) {
      console.error('Error getting all reports:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get reports',
      });
    }
  };

  // Get financial reports
  getFinancialReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = 'month' } = req.query;
      
      const reports = await this.reportsService.getFinancialReports(period as string);

      res.json({
        success: true,
        data: reports,
      });
    } catch (error) {
      console.error('Error getting financial reports:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get financial reports',
      });
    }
  };

  // Get user reports
  getUserReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = 'month' } = req.query;
      
      const reports = await this.reportsService.getUserReports(period as string);

      res.json({
        success: true,
        data: reports,
      });
    } catch (error) {
      console.error('Error getting user reports:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user reports',
      });
    }
  };

  // Get loan reports
  getLoanReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = 'month' } = req.query;
      
      const reports = await this.reportsService.getLoanReports(period as string);

      res.json({
        success: true,
        data: reports,
      });
    } catch (error) {
      console.error('Error getting loan reports:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get loan reports',
      });
    }
  };

  // Get operational reports
  getOperationalReports = async (req: Request, res: Response): Promise<void> => {
    try {
      const { period = 'month' } = req.query;
      
      const reports = await this.reportsService.getOperationalReports(period as string);

      res.json({
        success: true,
        data: reports,
      });
    } catch (error) {
      console.error('Error getting operational reports:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get operational reports',
      });
    }
  };

  // Get specific report by type and name
  getSpecificReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { reportType, reportName } = req.params;
      const { period = 'month' } = req.query;

      let reportData;

      switch (reportType) {
        case 'financial':
          switch (reportName) {
            case 'cash-flow':
              reportData = await this.reportsService.getCashFlowReport(period as string);
              break;
            case 'profit-loss':
              reportData = await this.reportsService.getProfitLossReport(period as string);
              break;
            case 'outstanding-loans':
              reportData = await this.reportsService.getOutstandingLoansReport(period as string);
              break;
            case 'repayment-analysis':
              reportData = await this.reportsService.getRepaymentAnalysisReport(period as string);
              break;
            case 'transaction-summary':
              reportData = await this.reportsService.getTransactionSummaryReport(period as string);
              break;
            default:
              res.status(404).json({
                success: false,
                message: 'Financial report not found',
              });
              return;
          }
          break;

        case 'user':
          switch (reportName) {
            case 'user-growth':
              reportData = await this.reportsService.getUserGrowthReport(period as string);
              break;
            case 'user-activity':
              reportData = await this.reportsService.getUserActivityReport(period as string);
              break;
            default:
              res.status(404).json({
                success: false,
                message: 'User report not found',
              });
              return;
          }
          break;

        case 'loan':
          switch (reportName) {
            case 'loan-applications':
              reportData = await this.reportsService.getLoanApplicationsReport(period as string);
              break;
            case 'loan-performance':
              reportData = await this.reportsService.getLoanPerformanceReport(period as string);
              break;
            case 'loan-types':
              reportData = await this.reportsService.getLoanTypesReport(period as string);
              break;
            case 'loan-aging':
              reportData = await this.reportsService.getLoanAgingReport(period as string);
              break;
            default:
              res.status(404).json({
                success: false,
                message: 'Loan report not found',
              });
              return;
          }
          break;

        case 'operational':
          switch (reportName) {
            case 'system-performance':
              reportData = await this.reportsService.getSystemPerformanceReport(period as string);
              break;
            case 'staff-activity':
              reportData = await this.reportsService.getStaffActivityReport(period as string);
              break;
            case 'audit-log':
              reportData = await this.reportsService.getAuditLogReport(period as string);
              break;
            default:
              res.status(404).json({
                success: false,
                message: 'Operational report not found',
              });
              return;
          }
          break;

        default:
          res.status(404).json({
            success: false,
            message: 'Report type not found',
          });
          return;
      }

      res.json({
        success: true,
        data: reportData,
      });
    } catch (error) {
      console.error('Error getting specific report:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get report',
      });
    }
  };

  // Export report data (for download functionality)
  exportReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { reportType, reportName } = req.params;
      const { period = 'month', format = 'json' } = req.query;

      // Get the report data
      const reportData = await this.getReportData(reportType, reportName, period as string);

      if (!reportData) {
        res.status(404).json({
          success: false,
          message: 'Report not found',
        });
        return;
      }

      // Set appropriate headers for download
      const filename = `${reportType}-${reportName}-${period}-${new Date().toISOString().split('T')[0]}`;
      
      if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
        
        // Convert to CSV (simplified implementation)
        const csvData = this.convertToCSV(reportData);
        res.send(csvData);
      } else {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
        res.json(reportData);
      }
    } catch (error) {
      console.error('Error exporting report:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to export report',
      });
    }
  };

  // Helper method to get report data
  private async getReportData(reportType: string, reportName: string, period: string): Promise<any> {
    // This is a simplified version of the getSpecificReport logic
    // In a real implementation, you might want to refactor this to avoid duplication
    switch (reportType) {
      case 'financial':
        switch (reportName) {
          case 'cash-flow':
            return await this.reportsService.getCashFlowReport(period);
          case 'profit-loss':
            return await this.reportsService.getProfitLossReport(period);
          // Add other cases...
        }
        break;
      // Add other report types...
    }
    return null;
  }

  // Helper method to convert data to CSV
  private convertToCSV(data: any): string {
    // Simplified CSV conversion
    // In a real implementation, you'd want a more robust CSV library
    if (Array.isArray(data)) {
      const headers = Object.keys(data[0] || {});
      const csvRows = [
        headers.join(','),
        ...data.map(row => headers.map(header => JSON.stringify(row[header] || '')).join(','))
      ];
      return csvRows.join('\n');
    } else {
      // For single objects, convert to key-value pairs
      const entries = Object.entries(data);
      return entries.map(([key, value]) => `${key},${JSON.stringify(value)}`).join('\n');
    }
  }
}
