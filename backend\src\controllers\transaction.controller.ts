import { Request, Response } from 'express';
import { TransactionService } from '../services/transaction.service';
import { TransactionStatus } from '../models/Transaction';
import { UserRole } from '../models/User';

export class TransactionController {
  private transactionService: TransactionService;

  constructor() {
    this.transactionService = new TransactionService();
  }

  // Get user transactions
  getUserTransactions = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      const transactions = await this.transactionService.getUserTransactions(userId);

      res.json({
        success: true,
        data: transactions,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get transactions',
      });
    }
  };

  // Get transaction by ID
  getTransactionById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { transactionId } = req.params;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      // If user is not admin/staff, only allow them to view their own transactions
      const transaction = userRole === UserRole.ADMIN || userRole === UserRole.STAFF
        ? await this.transactionService.getTransactionById(transactionId)
        : await this.transactionService.getTransactionById(transactionId, userId);

      res.json({
        success: true,
        data: transaction,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get transaction',
      });
    }
  };

  // Create deposit
  createDeposit = async (req: Request, res: Response): Promise<void> => {
    try {
      const { amount, reference, description } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      if (!amount) {
        res.status(400).json({
          success: false,
          message: 'Amount is required',
        });
        return;
      }

      const transaction = await this.transactionService.createDepositTransaction(
        userId,
        parseFloat(amount),
        reference,
        description
      );

      res.status(201).json({
        success: true,
        data: transaction,
        message: 'Deposit transaction created successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create deposit',
      });
    }
  };

  // Create withdrawal
  createWithdrawal = async (req: Request, res: Response): Promise<void> => {
    try {
      const { amount, reference, description } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      if (!amount) {
        res.status(400).json({
          success: false,
          message: 'Amount is required',
        });
        return;
      }

      const transaction = await this.transactionService.createWithdrawalTransaction(
        userId,
        parseFloat(amount),
        reference,
        description
      );

      res.status(201).json({
        success: true,
        data: transaction,
        message: 'Withdrawal transaction created successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create withdrawal',
      });
    }
  };

  // Get all transactions (admin/staff only)
  getAllTransactions = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      // Get query parameters for pagination and filtering
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string || '';
      const type = req.query.type as string;
      const status = req.query.status as string;

      const result = await this.transactionService.getAllTransactions({
        page,
        limit,
        search,
        type,
        status
      });

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error('Error getting all transactions:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get transactions',
      });
    }
  };

  // Update transaction status (admin/staff only)
  updateTransactionStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { transactionId } = req.params;
      const { status, metadata, failureReason } = req.body;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      if (!status || !Object.values(TransactionStatus).includes(status)) {
        res.status(400).json({
          success: false,
          message: 'Valid status is required',
        });
        return;
      }

      const transaction = await this.transactionService.updateTransactionStatus(
        transactionId,
        status,
        metadata,
        failureReason
      );

      res.json({
        success: true,
        data: transaction,
        message: 'Transaction status updated successfully',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update transaction status',
      });
    }
  };

  // Get transaction statistics
  getTransactionStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      const statistics = await this.transactionService.getUserTransactionStatistics(userId);

      res.json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get transaction statistics',
      });
    }
  };
}