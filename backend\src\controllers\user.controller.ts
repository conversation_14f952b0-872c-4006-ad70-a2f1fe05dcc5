import { Request, Response } from 'express';
import { UserService } from '../services/user.service';
import { UserRole, UserStatus } from '../models/User';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  // Get all users (admin only)
  getAllUsers = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;

      if (!userId || (userRole !== UserRole.ADMIN && userRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      // Get query parameters for pagination and filtering
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string || '';
      const status = req.query.status as UserStatus | undefined;
      const role = req.query.role as UserRole | undefined;

      const { users, total } = await this.userService.getAllUsers(page, limit, search, status, role);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
          }
        },
      });
    } catch (error) {
      console.error('Error getting all users:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get users',
      });
    }
  };

  // Get user by ID (admin only)
  getUserById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const user = await this.userService.getUserById(userId);

      res.json({
        success: true,
        data: user,
      });
    } catch (error) {
      console.error('Error getting user by ID:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user',
      });
    }
  };

  // Create new user (admin only)
  createUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const userData = req.body;
      const user = await this.userService.createUser(userData);

      res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully',
      });
    } catch (error) {
      console.error('Error creating user:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create user',
      });
    }
  };

  // Update user (admin only)
  updateUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const userData = req.body;
      const user = await this.userService.updateUser(userId, userData);

      res.json({
        success: true,
        data: user,
        message: 'User updated successfully',
      });
    } catch (error) {
      console.error('Error updating user:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update user',
      });
    }
  };

  // Delete user (admin only)
  deleteUser = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      await this.userService.deleteUser(userId);

      res.json({
        success: true,
        message: 'User deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete user',
      });
    }
  };

  // Update user status (admin only)
  updateUserStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const { status } = req.body;
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      if (!status || !Object.values(UserStatus).includes(status as UserStatus)) {
        res.status(400).json({
          success: false,
          message: 'Invalid status value',
        });
        return;
      }

      const user = await this.userService.updateUserStatus(userId, status as UserStatus);

      res.json({
        success: true,
        data: user,
        message: `User status updated to ${status} successfully`,
      });
    } catch (error) {
      console.error('Error updating user status:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update user status',
      });
    }
  };

  // Reset user password (admin only)
  resetUserPassword = async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const newPassword = await this.userService.resetUserPassword(userId);

      res.json({
        success: true,
        data: { password: newPassword },
        message: 'User password reset successfully',
      });
    } catch (error) {
      console.error('Error resetting user password:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to reset user password',
      });
    }
  };

  // Update user profile (for current user)
  updateProfile = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      const { name, email, phoneNumber } = req.body;

      const user = await this.userService.updateProfile(userId, {
        name,
        email,
        phoneNumber
      });

      res.json({
        success: true,
        data: user,
        message: 'Profile updated successfully',
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update profile',
      });
    }
  };

  // Upload profile image (for current user)
  uploadProfileImage = async (req: Request, res: Response): Promise<void> => {
    try {
      console.log('uploadProfileImage controller called');
      const userId = req.user?.id;

      if (!userId) {
        console.log('No user ID found in request');
        res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
        return;
      }

      console.log('Request body:', Object.keys(req.body));

      // For now, we'll just accept a base64 image string
      const { imageData } = req.body;

      if (!imageData) {
        console.log('No image data provided in request');
        res.status(400).json({
          success: false,
          message: 'No image data provided',
        });
        return;
      }

      console.log('Image data received, length:', imageData.length);

      try {
        // Store the base64 image data directly in the user profile
        const user = await this.userService.uploadProfileImage(userId, imageData);

        console.log('Profile image updated successfully');
        res.json({
          success: true,
          data: user,
          message: 'Profile image uploaded successfully',
        });
      } catch (serviceError) {
        console.error('Error in userService.uploadProfileImage:', serviceError);
        res.status(500).json({
          success: false,
          message: serviceError instanceof Error ? serviceError.message : 'Failed to upload profile image',
        });
      }
    } catch (error) {
      console.error('Error in uploadProfileImage controller:', error);
      res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to upload profile image',
      });
    }
  };

  // Get user statistics (admin only)
  getUserStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const requesterId = req.user?.id;
      const requesterRole = req.user?.role;

      if (!requesterId || (requesterRole !== UserRole.ADMIN && requesterRole !== UserRole.STAFF)) {
        res.status(403).json({
          success: false,
          message: 'Forbidden: Insufficient permissions',
        });
        return;
      }

      const stats = await this.userService.getUserStatistics();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Error getting user statistics:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user statistics',
      });
    }
  };
}
