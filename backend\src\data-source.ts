import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { User } from './models/User';
import { Loan } from './models/Loan';
import { Transaction } from './models/Transaction';
import { Notification } from './models/Notification';
import { Otp } from './models/Otp';
import { Document } from './models/Document';
import dotenv from 'dotenv';

dotenv.config();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'loan_app',
  synchronize: process.env.NODE_ENV === 'development',
  logging: process.env.NODE_ENV === 'development',
  entities: [User, Loan, Transaction, Notification, Otp, Document],
  migrations: ['src/migrations/**/*.ts'],
  subscribers: ['src/subscribers/**/*.ts'],
});