import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialMigration1709913600000 implements MigrationInterface {
    name = 'InitialMigration1709913600000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create enum types
        await queryRunner.query(`
            CREATE TYPE "public"."user_role_enum" AS ENUM ('customer', 'admin', 'staff')
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."user_status_enum" AS ENUM ('active', 'inactive', 'pending', 'suspended')
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."loan_status_enum" AS ENUM ('pending', 'approved', 'rejected', 'disbursed', 'paid', 'overdue')
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."transaction_type_enum" AS ENUM ('deposit', 'withdrawal', 'loan_disbursement', 'loan_repayment')
        `);
        await queryRunner.query(`
            CREATE TYPE "public"."transaction_status_enum" AS ENUM ('pending', 'completed', 'failed', 'cancelled')
        `);

        // Create users table
        await queryRunner.query(`
            CREATE TABLE "users" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "fullName" character varying NOT NULL,
                "email" character varying NOT NULL,
                "phoneNumber" character varying NOT NULL,
                "password" character varying NOT NULL,
                "role" "public"."user_role_enum" NOT NULL DEFAULT 'customer',
                "status" "public"."user_status_enum" NOT NULL DEFAULT 'pending',
                "studentId" character varying,
                "faceImage" character varying,
                "faceDescriptor" character varying,
                "isEmailVerified" boolean NOT NULL DEFAULT false,
                "isPhoneVerified" boolean NOT NULL DEFAULT false,
                "isFaceVerified" boolean NOT NULL DEFAULT false,
                "lastLoginAt" TIMESTAMP,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"),
                CONSTRAINT "UQ_17d1817f241f10a3dbafb169320" UNIQUE ("phoneNumber"),
                CONSTRAINT "PK_users" PRIMARY KEY ("id")
            )
        `);

        // Create loans table
        await queryRunner.query(`
            CREATE TABLE "loans" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "amount" decimal(10,2) NOT NULL,
                "interestRate" decimal(5,2) NOT NULL,
                "purpose" character varying NOT NULL,
                "status" "public"."loan_status_enum" NOT NULL DEFAULT 'pending',
                "termInMonths" integer NOT NULL,
                "amountPaid" decimal(10,2) NOT NULL DEFAULT '0',
                "approvedAt" TIMESTAMP,
                "disbursedAt" TIMESTAMP,
                "dueDate" TIMESTAMP,
                "paidAt" TIMESTAMP,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "userId" uuid NOT NULL,
                CONSTRAINT "PK_loans" PRIMARY KEY ("id"),
                CONSTRAINT "FK_loans_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
            )
        `);

        // Create transactions table
        await queryRunner.query(`
            CREATE TABLE "transactions" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "type" "public"."transaction_type_enum" NOT NULL,
                "amount" decimal(10,2) NOT NULL,
                "status" "public"."transaction_status_enum" NOT NULL DEFAULT 'pending',
                "reference" character varying,
                "description" character varying,
                "metadata" character varying,
                "completedAt" TIMESTAMP,
                "failedAt" TIMESTAMP,
                "failureReason" character varying,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "userId" uuid NOT NULL,
                CONSTRAINT "PK_transactions" PRIMARY KEY ("id"),
                CONSTRAINT "FK_transactions_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop tables
        await queryRunner.query(`DROP TABLE "transactions"`);
        await queryRunner.query(`DROP TABLE "loans"`);
        await queryRunner.query(`DROP TABLE "users"`);

        // Drop enum types
        await queryRunner.query(`DROP TYPE "public"."transaction_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."transaction_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."loan_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."user_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
    }
} 