import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPasswordChangedField1709913700000 implements MigrationInterface {
    name = 'AddPasswordChangedField1709913700000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" ADD "passwordChanged" boolean NOT NULL DEFAULT false
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" DROP COLUMN "passwordChanged"
        `);
    }
} 