import { MigrationInterface, QueryRunner } from "typeorm";

export class AddProfileImageToUser1709913800000 implements MigrationInterface {
    name = 'AddProfileImageToUser1709913800000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" ADD "profileImage" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "users" DROP COLUMN "profileImage"
        `);
    }
} 