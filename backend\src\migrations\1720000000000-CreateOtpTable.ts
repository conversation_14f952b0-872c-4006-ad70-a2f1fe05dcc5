import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateOtpTable1720000000000 implements MigrationInterface {
    name = 'CreateOtpTable1720000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create enum types
        await queryRunner.query(`CREATE TYPE "public"."otps_purpose_enum" AS ENUM('phone_verification', 'password_reset', 'login_verification')`);
        await queryRunner.query(`CREATE TYPE "public"."otps_status_enum" AS ENUM('pending', 'verified', 'expired')`);
        
        // Create otps table
        await queryRunner.query(`
            CREATE TABLE "otps" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "code" character varying NOT NULL,
                "phoneNumber" character varying NOT NULL,
                "purpose" "public"."otps_purpose_enum" NOT NULL DEFAULT 'phone_verification',
                "status" "public"."otps_status_enum" NOT NULL DEFAULT 'pending',
                "expiresAt" TIMESTAMP NOT NULL,
                "verifiedAt" TIMESTAMP,
                "attempts" integer NOT NULL DEFAULT 0,
                "userId" uuid,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_3c3410e4b9c2b9f4f5f7f1e54d2" PRIMARY KEY ("id")
            )
        `);
        
        // Add foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "otps" 
            ADD CONSTRAINT "FK_otps_users" 
            FOREIGN KEY ("userId") 
            REFERENCES "users"("id") 
            ON DELETE SET NULL 
            ON UPDATE NO ACTION
        `);
        
        // Add index for faster lookups
        await queryRunner.query(`
            CREATE INDEX "IDX_otps_phoneNumber" 
            ON "otps" ("phoneNumber")
        `);
        
        await queryRunner.query(`
            CREATE INDEX "IDX_otps_status" 
            ON "otps" ("status")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_otps_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_otps_phoneNumber"`);
        
        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE "otps" DROP CONSTRAINT "FK_otps_users"`);
        
        // Drop table
        await queryRunner.query(`DROP TABLE "otps"`);
        
        // Drop enum types
        await queryRunner.query(`DROP TYPE "public"."otps_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."otps_purpose_enum"`);
    }
}
