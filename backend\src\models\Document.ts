import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  CreateDateColumn, 
  UpdateDateColumn, 
  ManyToOne, 
  JoinColumn 
} from 'typeorm';
import { User } from './User';
import { Loan } from './Loan';

export enum DocumentType {
  ID = 'id',
  COLLATERAL = 'collateral',
  CONTRACT = 'contract',
  STATEMENT = 'statement',
  OTHER = 'other'
}

export enum DocumentStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

@Entity('documents')
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'user_id' })
  user!: User;

  @Column({ name: 'user_id' })
  userId!: string;

  @ManyToOne(() => Loan, { nullable: true })
  @JoinColumn({ name: 'loan_id' })
  loan?: Loan;

  @Column({ name: 'loan_id', nullable: true })
  loanId?: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    name: 'document_type'
  })
  documentType!: DocumentType;

  @Column({ name: 'file_name' })
  fileName!: string;

  @Column({ name: 'original_file_name' })
  originalFileName!: string;

  @Column({ name: 'file_path', nullable: true })
  filePath?: string;

  @Column({ name: 'file_data', type: 'bytea', nullable: true })
  fileData?: Buffer;

  @Column({ name: 'mime_type' })
  mimeType!: string;

  @Column({ name: 'file_size' })
  fileSize!: number;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    name: 'document_status',
    default: DocumentStatus.PENDING
  })
  documentStatus!: DocumentStatus;

  @Column({ name: 'status_updated_at', type: 'timestamp', nullable: true })
  statusUpdatedAt?: Date;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'status_updated_by' })
  statusUpdatedBy?: User;

  @Column({ name: 'status_updated_by', nullable: true })
  statusUpdatedById?: string;

  @Column({ name: 'rejection_reason', nullable: true })
  rejectionReason?: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'uploaded_by' })
  uploadedBy!: User;

  @Column({ name: 'uploaded_by' })
  uploadedById!: string;

  @Column({ name: 'upload_date', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  uploadDate!: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;
}
