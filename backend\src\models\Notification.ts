import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from './User';

export enum NotificationType {
  SYSTEM = 'system',
  PAYMENT = 'payment',
  LOAN = 'loan',
  USER = 'user',
  ALERT = 'alert'
}

export enum NotificationStatus {
  SENT = 'sent',
  SCHEDULED = 'scheduled',
  DRAFT = 'draft'
}

@Entity('notifications')
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  title!: string;

  @Column('text')
  message!: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
    default: NotificationType.SYSTEM
  })
  type!: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.DRAFT
  })
  status!: NotificationStatus;

  @Column({ nullable: true })
  recipientId?: string;

  @Column({ default: false })
  isAllUsers!: boolean;

  @Column({ default: false })
  isAdminOnly!: boolean;

  @Column({ nullable: true })
  scheduledFor?: Date;

  @Column({ default: false })
  isRead!: boolean;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'recipientId' })
  recipient?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'createdById' })
  createdBy?: User;
}
