import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinC<PERSON>umn } from 'typeorm';
import { User } from './User';

export enum OtpPurpose {
  PHONE_VERIFICATION = 'phone_verification',
  PASSWORD_RESET = 'password_reset',
  LOGIN_VERIFICATION = 'login_verification'
}

export enum OtpStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  EXPIRED = 'expired'
}

@Entity('otps')
export class Otp {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  code!: string;

  @Column()
  phoneNumber!: string;

  @Column({
    type: 'enum',
    enum: OtpPurpose,
    default: OtpPurpose.PHONE_VERIFICATION
  })
  purpose!: OtpPurpose;

  @Column({
    type: 'enum',
    enum: OtpStatus,
    default: OtpStatus.PENDING
  })
  status!: OtpStatus;

  @Column()
  expiresAt!: Date;

  @Column({ nullable: true })
  verifiedAt?: Date;

  @Column({ default: 0 })
  attempts!: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'userId' })
  user?: User;

  @Column({ nullable: true })
  userId?: string;

  @CreateDateColumn()
  createdAt!: Date;
}
