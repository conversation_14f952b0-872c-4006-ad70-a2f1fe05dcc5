import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, BeforeInsert, BeforeUpdate } from 'typeorm';
import { Loan } from './Loan';
import { Transaction } from './Transaction';
import * as bcrypt from 'bcryptjs';

export enum UserRole {
  CUSTOMER = 'customer',
  ADMIN = 'admin',
  STAFF = 'staff'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended'
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  fullName!: string;

  @Column({ unique: true })
  email!: string;

  @Column({ unique: true })
  phoneNumber!: string;

  @Column()
  password!: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.CUSTOMER
  })
  role!: UserRole;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.PENDING
  })
  status!: UserStatus;

  @Column({ nullable: true })
  studentId?: string;

  @Column({ nullable: true })
  faceImage?: string;

  @Column({ type: 'text', nullable: true })
  faceDescriptor?: string;

  @Column({ type: 'text', nullable: true })
  profileImage?: string;

  @Column({ default: false })
  isEmailVerified!: boolean;

  @Column({ default: false })
  isPhoneVerified!: boolean;

  @Column({ default: false })
  isFaceVerified!: boolean;

  @Column({ default: false })
  passwordChanged!: boolean;

  @Column({ nullable: true })
  lastLoginAt?: Date;

  @OneToMany(() => Loan, loan => loan.user)
  loans!: Loan[];

  @OneToMany(() => Transaction, transaction => transaction.user)
  transactions!: Transaction[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword(): Promise<void> {
    if (this.password) {
      // Log the password before hashing for debugging
      console.log('User model hashPassword called with:', {
        id: this.id,
        fullName: this.fullName,
        passwordLength: this.password.length,
        passwordStartsWith: this.password.substring(0, 10) + '...',
        isAlreadyHashed: this.password.startsWith('$2'),
      });

      // Check if password is already hashed
      if (!this.password.startsWith('$2')) {
        console.log('Hashing password in User model...');
        try {
          this.password = await bcrypt.hash(this.password, 10);
          console.log('Password hashed successfully:', this.password.substring(0, 10) + '...');
        } catch (error) {
          console.error('Error hashing password in User model:', error);
          throw error;
        }
      } else {
        console.log('Password already hashed, skipping hash operation');
      }
    }
  }

  async validatePassword(password: string): Promise<boolean> {
    console.log('User.validatePassword called for user:', this.fullName);
    console.log('Input password:', password);
    console.log('Stored hash:', this.password);

    try {
      const result = await bcrypt.compare(password, this.password);
      console.log('validatePassword result:', result);
      return result;
    } catch (error) {
      console.error('Error in validatePassword:', error);
      return false;
    }
  }
}