import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller';
import { authenticate } from '../middleware/auth.middleware';
import { Request, Response } from 'express';
import { AppDataSource } from '../data-source';
import { User } from '../models/User';

const router = Router();
const authController = new AuthController();
const userRepository = AppDataSource.getRepository(User);

console.log('🛠️ Setting up auth routes...');

// Public routes
console.log('📝 Registering POST /register route');
router.post('/register', authController.register);

console.log('🔑 Registering POST /login route');
router.post('/login', authController.login);

console.log('👑 Registering POST /admin-login route');
router.post('/admin-login', authController.adminLogin);

console.log('👑 Registering POST /create-admin route (temporary)');
router.post('/create-admin', authController.createAdmin);

// Protected routes
console.log('🔒 Registering POST /change-password route (protected)');
router.post('/change-password', authenticate, authController.changePassword);

console.log('✉️ Registering POST /verify-email/:userId route (protected)');
router.post('/verify-email/:userId', authenticate, authController.verifyEmail);

console.log('📱 Registering POST /verify-phone/:userId route (protected)');
router.post('/verify-phone/:userId', authenticate, authController.verifyPhone);

console.log('👤 Registering POST /verify-face/:userId route (protected)');
router.post('/verify-face/:userId', authenticate, authController.verifyFace);

// Debug routes - temporary for development only
console.log('🔍 Setting up DEBUG route: GET /check-user');
router.get('/check-user/:credential', async (req: Request<{credential: string}>, res: Response) => {
  try {
    const { credential } = req.params;
    console.log('DEBUG: Check user request for credential:', credential);

    // Find by email
    const userByEmail = await userRepository.findOne({ where: { email: credential } });
    if (userByEmail) {
      return res.json({
        found: true,
        type: 'email',
        user: {
          id: userByEmail.id,
          fullName: userByEmail.fullName,
          email: userByEmail.email,
          studentId: userByEmail.studentId,
          phoneNumber: userByEmail.phoneNumber,
          role: userByEmail.role,
          status: userByEmail.status,
          hasPassword: !!userByEmail.password,
          passwordLength: userByEmail.password?.length || 0,
          passwordChanged: userByEmail.passwordChanged
        }
      });
    }

    // Find by studentId
    const userByStudentId = await userRepository.findOne({ where: { studentId: credential } });
    if (userByStudentId) {
      return res.json({
        found: true,
        type: 'studentId',
        user: {
          id: userByStudentId.id,
          fullName: userByStudentId.fullName,
          email: userByStudentId.email,
          studentId: userByStudentId.studentId,
          phoneNumber: userByStudentId.phoneNumber,
          role: userByStudentId.role,
          status: userByStudentId.status,
          hasPassword: !!userByStudentId.password,
          passwordLength: userByStudentId.password?.length || 0,
          passwordChanged: userByStudentId.passwordChanged
        }
      });
    }

    return res.json({
      found: false,
      message: `No user found with credential: ${credential}`
    });
  } catch (error: any) {
    console.error('Error in check-user route:', error);
    res.status(500).json({
      error: 'Failed to check user info',
      message: error.message
    });
  }
});

console.log('✅ Auth routes setup complete');

export default router;