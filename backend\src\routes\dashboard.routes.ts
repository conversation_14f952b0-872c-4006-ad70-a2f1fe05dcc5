import { Router } from 'express';
import { DashboardController } from '../controllers/dashboard.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { UserRole } from '../models/User';

const router = Router();
const dashboardController = new DashboardController();

console.log('🛠️ Setting up dashboard routes...');

// All dashboard routes require admin authentication
const adminAuth = [authenticate, authorize(UserRole.ADMIN, UserRole.STAFF)];

// Get comprehensive dashboard statistics
console.log('📊 Registering GET /dashboard/statistics route (admin only)');
router.get('/statistics', ...adminAuth, dashboardController.getDashboardStatistics);

// Get recent transactions for dashboard
console.log('📊 Registering GET /dashboard/transactions route (admin only)');
router.get('/transactions', ...adminAuth, dashboardController.getRecentTransactions);

// Get dashboard overview (combines stats and recent transactions)
console.log('📊 Registering GET /dashboard/overview route (admin only)');
router.get('/overview', ...adminAuth, dashboardController.getDashboardOverview);

// Legacy endpoint for backward compatibility
console.log('📊 Registering GET /dashboard-stats route (admin only) - legacy');
router.get('/dashboard-stats', ...adminAuth, dashboardController.getDashboardStatistics);

export default router;
