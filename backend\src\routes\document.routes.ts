import { Router } from 'express';
import { DocumentController } from '../controllers/document.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { UserRole } from '../models/User';

const router = Router();
const documentController = new DocumentController();

console.log('🛠️ Setting up document routes...');

// Admin routes (authenticated + authorized)
console.log('📄 Registering GET /documents route (admin only)');
router.get('/',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  documentController.getAllDocuments
);

// Get documents by user ID
console.log('📄 Registering GET /documents/user/:userId route');
router.get('/user/:userId',
  authenticate,
  documentController.getDocumentsByUserId
);

// Get documents by loan ID
console.log('📄 Registering GET /documents/loan/:loanId route');
router.get('/loan/:loanId',
  authenticate,
  documentController.getDocumentsByLoanId
);

// Get document by ID
console.log('📄 Registering GET /documents/:documentId route');
router.get('/:documentId',
  authenticate,
  documentController.getDocumentById
);

// Upload document
console.log('📄 Registering POST /documents/upload route');
router.post('/upload',
  authenticate,
  documentController.uploadMiddleware,
  documentController.uploadDocument
);

// Update document status (admin only)
console.log('📄 Registering PATCH /documents/:documentId/status route (admin only)');
router.patch('/:documentId/status',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  documentController.updateDocumentStatus
);

// Delete document
console.log('📄 Registering DELETE /documents/:documentId route');
router.delete('/:documentId',
  authenticate,
  documentController.deleteDocument
);

// Download document
console.log('📄 Registering GET /documents/:documentId/download route');
router.get('/:documentId/download',
  authenticate,
  documentController.downloadDocument
);

export default router;
