import { Router } from 'express';
import { Loan<PERSON>ontroller } from '../controllers/loan.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { UserRole } from '../models/User';

const router = Router();
const loanController = new LoanController();

// Customer routes (authenticated)
router.post('/apply', authenticate, loanController.applyForLoan);
router.get('/my-loans', authenticate, loanController.getUserLoans);
router.get('/active', authenticate, loanController.getActiveLoans);
router.get('/my-loans/:loanId', authenticate, loanController.getLoanById);
router.post('/my-loans/:loanId/repay', authenticate, loanController.makeRepayment);
router.get('/statistics', authenticate, loanController.getUserLoanStatistics);
router.get('/available-credit', authenticate, loanController.getAvailableCredit);
router.get('/my-loans/:loanId/payment-schedule', authenticate, loanController.getLoanPaymentSchedule);
router.get('/my-loans/:loanId/health-score', authenticate, loanController.getLoanHealthScore);

// Admin routes (authenticated + authorized)
router.get('/all',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  loanController.getAllLoans
);

router.get('/pending',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  loanController.getPendingLoans
);
router.post('/:loanId/approve',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  loanController.approveLoan
);
router.post('/:loanId/reject',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  loanController.rejectLoan
);
router.post('/:loanId/disburse',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  loanController.disburseLoan
);

router.get('/simulated-balance',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  loanController.getSimulatedBalance
);

// Webhook route (no authentication required)
router.post('/webhook/transaction-status', loanController.handleTransactionWebhook);

export default router;