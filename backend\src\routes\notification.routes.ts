import { Router } from 'express';
import { NotificationController } from '../controllers/notification.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { UserRole } from '../models/User';

const router = Router();
const notificationController = new NotificationController();

console.log('🛠️ Setting up notification routes...');

// Admin routes (authenticated + authorized)
console.log('📢 Registering GET /notifications route (admin only)');
router.get('/',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  notificationController.getAllNotifications
);

console.log('📢 Registering POST /notifications route (admin only)');
router.post('/',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  notificationController.createNotification
);

console.log('📢 Registering GET /notifications/:notificationId route (admin only)');
router.get('/:notificationId',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  notificationController.getNotificationById
);

console.log('📢 Registering PATCH /notifications/:notificationId route (admin only)');
router.patch('/:notificationId',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  notificationController.updateNotification
);

console.log('📢 Registering DELETE /notifications/:notificationId route (admin only)');
router.delete('/:notificationId',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  notificationController.deleteNotification
);

console.log('📢 Registering POST /notifications/:notificationId/send route (admin only)');
router.post('/:notificationId/send',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  notificationController.sendNotificationNow
);

console.log('📢 Registering GET /notifications/admin/admin-notifications route (admin only)');
router.get('/admin/admin-notifications',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  notificationController.getAdminNotifications
);

// User routes (authenticated)
console.log('📢 Registering GET /notifications/user/my-notifications route');
router.get('/user/my-notifications',
  authenticate,
  notificationController.getUserNotifications
);

console.log('📢 Registering PATCH /notifications/user/:notificationId/read route');
router.patch('/user/:notificationId/read',
  authenticate,
  notificationController.markAsRead
);

console.log('📢 Registering PATCH /notifications/user/read-all route');
router.patch('/user/read-all',
  authenticate,
  notificationController.markAllAsRead
);

export default router;
