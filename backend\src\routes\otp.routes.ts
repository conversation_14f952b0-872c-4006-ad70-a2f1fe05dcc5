import { Router } from 'express';
import { OtpController } from '../controllers/otp.controller';
import { authenticate } from '../middleware/auth.middleware';

const router = Router();
const otpController = new OtpController();

console.log('🛠️ Setting up OTP routes...');

// All routes are protected and require authentication
console.log('📱 Registering POST /send route (protected)');
router.post('/send', authenticate, otpController.sendOtp);

console.log('📱 Registering POST /verify route (protected)');
router.post('/verify', authenticate, otpController.verifyOtp);

console.log('📱 Registering POST /resend route (protected)');
router.post('/resend', authenticate, otpController.resendOtp);

export default router;
