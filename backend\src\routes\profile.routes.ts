import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { authenticate } from '../middleware/auth.middleware';

const router = Router();
const userController = new UserController();

console.log('🛠️ Setting up profile routes...');

// User profile routes (for current user)
console.log('👤 Registering PATCH /profile route');
router.patch('/', 
  authenticate, 
  userController.updateProfile
);

console.log('🖼️ Registering POST /profile/image route');
router.post('/image', 
  authenticate, 
  userController.uploadProfileImage
);

export default router;
