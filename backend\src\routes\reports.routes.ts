import { Router } from 'express';
import { ReportsController } from '../controllers/reports.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { UserRole } from '../models/User';

const router = Router();
const reportsController = new ReportsController();

console.log('🛠️ Setting up reports routes...');

// All reports routes require admin authentication
const adminAuth = [authenticate, authorize(UserRole.ADMIN, UserRole.STAFF)];

// Get all reports for dashboard
console.log('📊 Registering GET /reports route (admin only)');
router.get('/', ...adminAuth, reportsController.getAllReports);

// Get reports by category
console.log('📊 Registering GET /reports/financial route (admin only)');
router.get('/financial', ...adminAuth, reportsController.getFinancialReports);

console.log('📊 Registering GET /reports/user route (admin only)');
router.get('/user', ...adminAuth, reportsController.getUserReports);

console.log('📊 Registering GET /reports/loan route (admin only)');
router.get('/loan', ...adminAuth, reportsController.getLoanReports);

console.log('📊 Registering GET /reports/operational route (admin only)');
router.get('/operational', ...adminAuth, reportsController.getOperationalReports);

// Get specific reports
console.log('📊 Registering GET /reports/:reportType/:reportName route (admin only)');
router.get('/:reportType/:reportName', ...adminAuth, reportsController.getSpecificReport);

// Export reports
console.log('📊 Registering GET /reports/:reportType/:reportName/export route (admin only)');
router.get('/:reportType/:reportName/export', ...adminAuth, reportsController.exportReport);

export default router;
