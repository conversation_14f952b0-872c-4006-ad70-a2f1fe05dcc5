import { Router } from 'express';
import { TransactionController } from '../controllers/transaction.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { UserRole } from '../models/User';

const router = Router();
const transactionController = new TransactionController();

// Customer routes (authenticated)
router.get('/my-transactions', authenticate, transactionController.getUserTransactions);
router.get('/my-transactions/:transactionId', authenticate, transactionController.getTransactionById);
router.post('/deposit', authenticate, transactionController.createDeposit);
router.post('/withdraw', authenticate, transactionController.createWithdrawal);
router.get('/statistics', authenticate, transactionController.getTransactionStatistics);

// Admin routes (authenticated + authorized)
router.get('/admin/all-transactions',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  transactionController.getAllTransactions
);

router.patch('/:transactionId/status',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  transactionController.updateTransactionStatus
);

export default router;