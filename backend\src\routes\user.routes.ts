import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';
import { UserRole } from '../models/User';

const router = Router();
const userController = new UserController();

console.log('🛠️ Setting up user routes...');

// Admin routes (authenticated + authorized)
console.log('👥 Registering GET /users route (admin only)');
router.get('/',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.getAllUsers
);

console.log('👤 Registering GET /users/:userId route (admin only)');
router.get('/:userId',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.getUserById
);

console.log('➕ Registering POST /users route (admin only)');
router.post('/',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.createUser
);

console.log('✏️ Registering PATCH /users/:userId route (admin only)');
router.patch('/:userId',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.updateUser
);

console.log('🗑️ Registering DELETE /users/:userId route (admin only)');
router.delete('/:userId',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.deleteUser
);

console.log('🔄 Registering PATCH /users/:userId/status route (admin only)');
router.patch('/:userId/status',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.updateUserStatus
);

console.log('🔑 Registering POST /users/:userId/reset-password route (admin only)');
router.post('/:userId/reset-password',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.resetUserPassword
);

console.log('📊 Registering GET /statistics route (admin only)');
router.get('/statistics',
  authenticate,
  authorize(UserRole.ADMIN, UserRole.STAFF),
  userController.getUserStatistics
);

// Profile routes moved to profile.routes.ts

export default router;
