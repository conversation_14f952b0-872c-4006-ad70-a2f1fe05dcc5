import { AppDataSource } from './data-source';

async function runMigration() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log('Database connection established');

    // Run the migration to add profileImage column if it doesn't exist
    try {
      await AppDataSource.query(`
        ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "profileImage" character varying;
      `);
      console.log('Migration successful: Added profileImage column to users table');
    } catch (error) {
      console.error('Error adding profileImage column:', error);
    }

    // Close the connection
    await AppDataSource.destroy();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error running migration:', error);
  }
}

runMigration(); 