import 'dotenv/config';
import { AppDataSource } from './data-source';
import { app } from './app';
import { Scheduler } from './tasks/scheduler';

const PORT = process.env.PORT || 3001;

// Initialize database connection
AppDataSource.initialize()
  .then(() => {
    console.log('Database connection established');

    // Start server
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);

      // Start scheduled tasks
      const scheduler = new Scheduler();
      scheduler.start();

      // Handle graceful shutdown
      process.on('SIGTERM', () => {
        console.log('SIGTERM received, shutting down gracefully');
        scheduler.stop();
        process.exit(0);
      });
    });
  })
  .catch((error) => {
    console.error('Error connecting to database:', error);
    process.exit(1);
  });

// Handle unhandled promise rejections
process.on('unhandledRejection', (error: Error) => {
  console.error('Unhandled Promise Rejection:', error);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});