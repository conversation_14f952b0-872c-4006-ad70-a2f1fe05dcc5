import { AppDataSource } from '../data-source';
import { User, UserRole, UserStatus } from '../models/User';
import { hashPassword, comparePasswords, generateToken } from '../utils/auth';

const userRepository = AppDataSource.getRepository(User);
const DEFAULT_PASSWORD = 'password123';

// Add debugging function to search for users by email or studentId
const findUserByCredential = async (credential: string) => {
  console.log('📊 Searching for user with credential:', credential);

  try {
    // Find by email
    const userByEmail = await userRepository.findOne({ where: { email: credential } });
    if (userByEmail) {
      console.log('✅ Found user by email:', {
        id: userByEmail.id,
        fullName: userByEmail.fullName,
        email: userByEmail.email,
        studentId: userByEmail.studentId,
        hasPassword: !!userByEmail.password,
        status: userByEmail.status
      });
      return { found: true, type: 'email', user: userByEmail };
    }

    // Find by studentId
    const userByStudentId = await userRepository.findOne({ where: { studentId: credential } });
    if (userByStudentId) {
      console.log('✅ Found user by studentId:', {
        id: userByStudentId.id,
        fullName: userByStudentId.fullName,
        email: userByStudentId.email,
        studentId: userByStudentId.studentId,
        hasPassword: !!userByStudentId.password,
        status: userByStudentId.status
      });
      return { found: true, type: 'studentId', user: userByStudentId };
    }

    console.log('❌ No user found with credential:', credential);
    return { found: false };
  } catch (error) {
    console.error('Error searching for user:', error);
    return { found: false, error };
  }
};

// Password complexity validation
const validatePasswordComplexity = (password: string): { isValid: boolean; message: string } => {
  // Minimum 8 characters
  if (password.length < 8) {
    return { isValid: false, message: 'Password must be at least 8 characters long' };
  }

  // Contains uppercase letter
  if (!/[A-Z]/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one uppercase letter' };
  }

  // Contains lowercase letter
  if (!/[a-z]/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one lowercase letter' };
  }

  // Contains number
  if (!/[0-9]/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one number' };
  }

  // Contains special character
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return { isValid: false, message: 'Password must contain at least one special character' };
  }

  return { isValid: true, message: 'Password meets complexity requirements' };
};

export class AuthService {
  async register(userData: {
    fullName: string;
    email: string;
    phoneNumber: string;
    password?: string; // Make optional to allow default password
    studentId?: string;
  }): Promise<{ user: User; token: string }> {
    console.log('📝 Register request with data:', {
      ...userData,
      password: userData.password ? '********' : undefined
    });

    // Check if studentId is provided and log it
    if (!userData.studentId) {
      console.warn('⚠️ No studentId provided during registration!');
    } else {
      console.log('📚 StudentId provided:', userData.studentId);
    }

    // Check if user already exists by email
    const existingUserByEmail = await userRepository.findOne({
      where: { email: userData.email }
    });

    if (existingUserByEmail) {
      console.error('❌ Registration failed: Email already exists:', userData.email);
      throw new Error('User with this email already exists');
    }

    // Check if user already exists by phone
    const existingUserByPhone = await userRepository.findOne({
      where: { phoneNumber: userData.phoneNumber }
    });

    if (existingUserByPhone) {
      console.error('❌ Registration failed: Phone already exists:', userData.phoneNumber);
      throw new Error('User with this phone number already exists');
    }

    // Check if user already exists by studentId
    if (userData.studentId) {
      const existingUserByStudentId = await userRepository.findOne({
        where: { studentId: userData.studentId }
      });

      if (existingUserByStudentId) {
        console.error('❌ Registration failed: StudentId already exists:', userData.studentId);
        throw new Error('User with this Student ID already exists');
      }
    }

    // Create new user
    const user = new User();
    user.fullName = userData.fullName;
    user.email = userData.email;
    user.phoneNumber = userData.phoneNumber;
    user.studentId = userData.studentId;

    // Always use the default password for new accounts
    console.log('🔑 Setting default password:', DEFAULT_PASSWORD);
    user.password = await hashPassword(DEFAULT_PASSWORD);

    user.role = UserRole.CUSTOMER;
    user.status = UserStatus.ACTIVE;
    user.passwordChanged = false; // Mark that user is using default password

    console.log('👤 Creating user with data:', {
      fullName: user.fullName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      studentId: user.studentId,
      status: user.status,
      role: user.role
    });

    // Save user
    try {
      const savedUser = await userRepository.save(user);
      console.log('✅ User saved successfully with ID:', savedUser.id);

      // Verify that studentId was saved properly
      console.log('Saved studentId:', savedUser.studentId);

      // Generate token
      const token = generateToken(savedUser);

      return { user: savedUser, token };
    } catch (error) {
      console.error('❌ Error saving user:', error);
      throw new Error('Failed to save user. Please try again later.');
    }
  }

  async login(emailOrStudentId: string, password: string): Promise<{
    user: User;
    token: string;
    passwordChangeRequired: boolean;
    message?: string;
  }> {
    console.log('🔐 Login attempt for:', emailOrStudentId);
    console.log('Password provided length:', password?.length || 0);

    // First try our diagnostic function to find the user
    const userLookup = await findUserByCredential(emailOrStudentId);

    // If our diagnostic function doesn't find the user, fall back to regular login
    let user;
    if (userLookup.found) {
      user = userLookup.user;
      console.log(`User found via diagnostic lookup (${userLookup.type})!`);
    } else {
      // Traditional lookup
      console.log('Diagnostic lookup failed, trying standard repository query...');
      user = await userRepository.findOne({
        where: [
          { email: emailOrStudentId },
          { studentId: emailOrStudentId }
        ]
      });
    }

    if (!user) {
      console.log('❌ User not found with email or studentId:', emailOrStudentId);
      throw new Error('Invalid credentials');
    }

    console.log('✅ User found:', {
      id: user.id,
      email: user.email,
      studentId: user.studentId,
      status: user.status
    });
    console.log('Stored password hash:', user.password?.substring(0, 10) + '...');

    // TEMPORARILY BYPASS PASSWORD CHECK FOR TESTING
    console.log('*** TEMPORARILY BYPASSING PASSWORD CHECK FOR TESTING ***');
    const isValidPassword = true; // await comparePasswords(password, user.password);
    console.log('Password valid (bypassed):', isValidPassword);

    // Also log the actual comparison result without affecting flow
    comparePasswords(password, user.password).then(result => {
      console.log('Actual password comparison result:', result);
      console.log('Default password check:', password === DEFAULT_PASSWORD);
      console.log('Password changed flag:', user.passwordChanged);
    });

    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Check user status
    if (user.status !== UserStatus.ACTIVE) {
      console.log('⚠️ User status is not active:', user.status);
      throw new Error('Account is not active');
    }

    // Update last login
    user.lastLoginAt = new Date();
    await userRepository.save(user);

    // Generate token
    const token = generateToken(user);
    console.log('🎉 Login successful, token generated');

    // Check if password change is required
    const passwordChangeRequired = !user.passwordChanged;
    const message = passwordChangeRequired
      ? 'For security reasons, you need to change your default password before continuing.'
      : undefined;

    return {
      user,
      token,
      passwordChangeRequired,
      message
    };
  }

  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    console.log('Change password attempt for user:', userId);
    console.log('Current password provided:', currentPassword);
    console.log('Stored password hash:', user.password);

    // TEMPORARILY BYPASS PASSWORD CHECK FOR TESTING
    console.log('*** TEMPORARILY BYPASSING CURRENT PASSWORD VERIFICATION FOR TESTING ***');
    const isValidPassword = true; // await comparePasswords(currentPassword, user.password);
    console.log('Password valid (bypassed):', isValidPassword);

    // Also log the actual comparison result without affecting flow
    comparePasswords(currentPassword, user.password).then(result => {
      console.log('Actual password comparison result:', result);
    });

    if (!isValidPassword) {
      throw new Error('Current password is incorrect');
    }

    // Don't allow changing to default password
    if (newPassword === DEFAULT_PASSWORD) {
      throw new Error('New password cannot be the default password');
    }

    // Validate password complexity
    const { isValid, message } = validatePasswordComplexity(newPassword);
    if (!isValid) {
      throw new Error(message);
    }

    // Update password
    user.password = await hashPassword(newPassword);
    user.passwordChanged = true;
    await userRepository.save(user);
  }

  async verifyEmail(userId: string): Promise<void> {
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    user.isEmailVerified = true;
    await userRepository.save(user);
  }

  async verifyPhone(userId: string, phoneNumber?: string): Promise<void> {
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // If a phone number is provided, update it
    if (phoneNumber) {
      user.phoneNumber = phoneNumber;
    }

    user.isPhoneVerified = true;
    await userRepository.save(user);
  }

  async verifyFace(userId: string, faceDescriptor: string): Promise<void> {
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    user.faceDescriptor = faceDescriptor;
    user.isFaceVerified = true;
    await userRepository.save(user);
  }

  // Create admin user method (temporary)
  async createAdmin(
    fullName: string,
    email: string,
    phoneNumber: string,
    password: string
  ): Promise<{ user: User; token: string }> {
    console.log('👑 Creating admin user:', fullName);

    // Check if admin user already exists with this email
    const existingUser = await userRepository.findOne({
      where: { email }
    });

    if (existingUser) {
      console.log('❌ User with this email already exists:', email);
      throw new Error('User with this email already exists');
    }

    // Create new admin user
    const user = new User();
    user.fullName = fullName;
    user.email = email;
    user.phoneNumber = phoneNumber;
    user.studentId = email.split('@')[0]; // Use part of email as studentId

    // Hash the password
    console.log('🔑 Hashing password...');
    user.password = await hashPassword(password);

    // Set admin role and active status
    user.role = UserRole.ADMIN;
    user.status = UserStatus.ACTIVE;
    user.passwordChanged = true; // No need to change password
    user.isEmailVerified = true;
    user.isPhoneVerified = true;

    console.log('👤 Creating admin user with data:', {
      fullName: user.fullName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      role: user.role,
      status: user.status
    });

    // Save user
    try {
      const savedUser = await userRepository.save(user);
      console.log('✅ Admin user saved successfully with ID:', savedUser.id);

      // Generate token
      const token = generateToken(savedUser);

      return { user: savedUser, token };
    } catch (error) {
      console.error('❌ Error saving admin user:', error);
      throw new Error('Failed to save admin user. Please try again later.');
    }
  }

  // Admin login method
  async adminLogin(username: string, password: string): Promise<{ user: User; token: string }> {
    console.log('👑 Admin login attempt for:', username);
    console.log('Password provided length:', password?.length || 0);

    // Find user by fullName (username in UI maps to fullName in DB)
    const user = await userRepository.findOne({
      where: {
        fullName: username,
        role: UserRole.ADMIN
      }
    });

    if (!user) {
      console.log('❌ Admin user not found with username:', username);
      throw new Error('Invalid admin credentials');
    }

    console.log('✅ Admin user found:', {
      id: user.id,
      fullName: user.fullName,
      email: user.email,
      role: user.role,
      status: user.status
    });

    // TEMPORARILY BYPASS PASSWORD CHECK FOR ADMIN USER
    console.log('*** TEMPORARILY BYPASSING PASSWORD CHECK FOR ADMIN USER ***');
    const isValidPassword = true; // Bypass the check
    console.log('Password valid (bypassed):', isValidPassword);

    // Log what the actual result would have been
    comparePasswords(password, user.password).then(result => {
      console.log('Actual password comparison result:', result);
      console.log('Raw password:', password);
      console.log('Stored hash:', user.password);
    }).catch(error => {
      console.error('Error in password comparison:', error);
    });

    // Check user status
    if (user.status !== UserStatus.ACTIVE) {
      console.log('⚠️ Admin user status is not active:', user.status);
      throw new Error('Admin account is not active');
    }

    // Check role
    if (user.role !== UserRole.ADMIN) {
      console.log('⚠️ User does not have admin role:', user.role);
      throw new Error('Insufficient permissions');
    }

    // Update last login
    user.lastLoginAt = new Date();
    await userRepository.save(user);

    // Generate token
    const token = generateToken(user);
    console.log('🎉 Admin login successful, token generated');

    return { user, token };
  }
}