import { AppDataSource } from '../data-source';
import { User, UserRole, UserStatus } from '../models/User';
import { Loan, LoanStatus } from '../models/Loan';
import { Transaction, TransactionType, TransactionStatus } from '../models/Transaction';
import { Document, DocumentStatus } from '../models/Document';

const userRepository = AppDataSource.getRepository(User);
const loanRepository = AppDataSource.getRepository(Loan);
const transactionRepository = AppDataSource.getRepository(Transaction);
const documentRepository = AppDataSource.getRepository(Document);

export interface DashboardStats {
  userStats: {
    totalUsers: number;
    activeUsers: number;
    newUsersThisMonth: number;
    verifiedUsers: number;
    pendingUsers: number;
    userGrowthRate: number;
  };
  loanStats: {
    totalLoans: number;
    activeLoans: number;
    pendingLoans: number;
    approvedLoans: number;
    disbursedLoans: number;
    paidLoans: number;
    overdueLoans: number;
    totalLoanAmount: number;
    totalOutstandingAmount: number;
    totalPaidAmount: number;
    averageLoanAmount: number;
    approvalRate: number;
    defaultRate: number;
  };
  transactionStats: {
    totalTransactions: number;
    completedTransactions: number;
    pendingTransactions: number;
    failedTransactions: number;
    totalTransactionVolume: number;
    totalDeposits: number;
    totalWithdrawals: number;
    totalDisbursements: number;
    totalRepayments: number;
  };
  financialStats: {
    totalRevenue: number;
    monthlyRevenue: number;
    totalInterestEarned: number;
    cashFlow: number;
    profitMargin: number;
  };
  systemStats: {
    totalDocuments: number;
    pendingDocuments: number;
    approvedDocuments: number;
    rejectedDocuments: number;
    systemUptime: number;
  };
}

export interface RecentTransaction {
  id: string;
  type: TransactionType;
  amount: number;
  description: string;
  status: TransactionStatus;
  createdAt: Date;
  userName: string;
}

export class DashboardService {
  // Get comprehensive dashboard statistics
  async getDashboardStatistics(): Promise<DashboardStats> {
    try {
      const [userStats, loanStats, transactionStats, financialStats, systemStats] = await Promise.all([
        this.getUserStatistics(),
        this.getLoanStatistics(),
        this.getTransactionStatistics(),
        this.getFinancialStatistics(),
        this.getSystemStatistics(),
      ]);

      return {
        userStats,
        loanStats,
        transactionStats,
        financialStats,
        systemStats,
      };
    } catch (error) {
      console.error('Error getting dashboard statistics:', error);
      throw new Error('Failed to fetch dashboard statistics');
    }
  }

  // Get user statistics
  private async getUserStatistics() {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    const [
      totalUsers,
      activeUsers,
      newUsersThisMonth,
      newUsersLastMonth,
      verifiedUsers,
      pendingUsers,
    ] = await Promise.all([
      userRepository.count({ where: { role: UserRole.CUSTOMER } }),
      userRepository.count({
        where: {
          role: UserRole.CUSTOMER,
          status: UserStatus.ACTIVE
        }
      }),
      userRepository
        .createQueryBuilder('user')
        .where('user.role = :role', { role: UserRole.CUSTOMER })
        .andWhere('user.createdAt >= :startOfMonth', { startOfMonth })
        .getCount(),
      userRepository
        .createQueryBuilder('user')
        .where('user.role = :role', { role: UserRole.CUSTOMER })
        .andWhere('user.createdAt >= :startOfLastMonth', { startOfLastMonth })
        .andWhere('user.createdAt <= :endOfLastMonth', { endOfLastMonth })
        .getCount(),
      userRepository.count({
        where: {
          role: UserRole.CUSTOMER,
          isFaceVerified: true,
          isPhoneVerified: true,
        }
      }),
      userRepository.count({
        where: {
          role: UserRole.CUSTOMER,
          status: UserStatus.PENDING,
        }
      }),
    ]);

    // Calculate growth rate
    const userGrowthRate = newUsersLastMonth > 0
      ? ((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100
      : newUsersThisMonth > 0 ? 100 : 0;

    return {
      totalUsers,
      activeUsers,
      newUsersThisMonth,
      verifiedUsers,
      pendingUsers,
      userGrowthRate: Math.round(userGrowthRate * 100) / 100,
    };
  }

  // Get loan statistics
  private async getLoanStatistics() {
    const [
      totalLoans,
      activeLoans,
      pendingLoans,
      approvedLoans,
      disbursedLoans,
      paidLoans,
      overdueLoans,
      loanAmounts,
    ] = await Promise.all([
      loanRepository.count(),
      loanRepository.count({
        where: [
          { status: LoanStatus.APPROVED },
          { status: LoanStatus.DISBURSED },
        ]
      }),
      loanRepository.count({ where: { status: LoanStatus.PENDING } }),
      loanRepository.count({ where: { status: LoanStatus.APPROVED } }),
      loanRepository.count({ where: { status: LoanStatus.DISBURSED } }),
      loanRepository.count({ where: { status: LoanStatus.PAID } }),
      loanRepository.count({ where: { status: LoanStatus.OVERDUE } }),
      loanRepository
        .createQueryBuilder('loan')
        .select([
          'SUM(loan.amount) as totalAmount',
          'SUM(loan.amountPaid) as totalPaid',
          'AVG(loan.amount) as averageAmount',
        ])
        .getRawOne(),
    ]);

    const totalLoanAmount = parseFloat(loanAmounts.totalAmount) || 0;
    const totalPaidAmount = parseFloat(loanAmounts.totalPaid) || 0;
    const averageLoanAmount = parseFloat(loanAmounts.averageAmount) || 0;
    const totalOutstandingAmount = totalLoanAmount - totalPaidAmount;

    // Calculate rates
    const approvalRate = totalLoans > 0 ? ((approvedLoans + disbursedLoans + paidLoans) / totalLoans) * 100 : 0;
    const defaultRate = totalLoans > 0 ? (overdueLoans / totalLoans) * 100 : 0;

    return {
      totalLoans,
      activeLoans,
      pendingLoans,
      approvedLoans,
      disbursedLoans,
      paidLoans,
      overdueLoans,
      totalLoanAmount,
      totalOutstandingAmount,
      totalPaidAmount,
      averageLoanAmount,
      approvalRate: Math.round(approvalRate * 100) / 100,
      defaultRate: Math.round(defaultRate * 100) / 100,
    };
  }

  // Get transaction statistics
  private async getTransactionStatistics() {
    const [
      totalTransactions,
      completedTransactions,
      pendingTransactions,
      failedTransactions,
      transactionAmounts,
    ] = await Promise.all([
      transactionRepository.count(),
      transactionRepository.count({ where: { status: TransactionStatus.COMPLETED } }),
      transactionRepository.count({ where: { status: TransactionStatus.PENDING } }),
      transactionRepository.count({ where: { status: TransactionStatus.FAILED } }),
      transactionRepository
        .createQueryBuilder('transaction')
        .select([
          'SUM(CASE WHEN transaction.status = :completed THEN transaction.amount ELSE 0 END) as totalVolume',
          'SUM(CASE WHEN transaction.type = :deposit AND transaction.status = :completed THEN transaction.amount ELSE 0 END) as totalDeposits',
          'SUM(CASE WHEN transaction.type = :withdrawal AND transaction.status = :completed THEN transaction.amount ELSE 0 END) as totalWithdrawals',
          'SUM(CASE WHEN transaction.type = :disbursement AND transaction.status = :completed THEN transaction.amount ELSE 0 END) as totalDisbursements',
          'SUM(CASE WHEN transaction.type = :repayment AND transaction.status = :completed THEN transaction.amount ELSE 0 END) as totalRepayments',
        ])
        .setParameters({
          completed: TransactionStatus.COMPLETED,
          deposit: TransactionType.DEPOSIT,
          withdrawal: TransactionType.WITHDRAWAL,
          disbursement: TransactionType.LOAN_DISBURSEMENT,
          repayment: TransactionType.LOAN_REPAYMENT,
        })
        .getRawOne(),
    ]);

    return {
      totalTransactions,
      completedTransactions,
      pendingTransactions,
      failedTransactions,
      totalTransactionVolume: parseFloat(transactionAmounts.totalVolume) || 0,
      totalDeposits: parseFloat(transactionAmounts.totalDeposits) || 0,
      totalWithdrawals: parseFloat(transactionAmounts.totalWithdrawals) || 0,
      totalDisbursements: parseFloat(transactionAmounts.totalDisbursements) || 0,
      totalRepayments: parseFloat(transactionAmounts.totalRepayments) || 0,
    };
  }

  // Get financial statistics
  private async getFinancialStatistics() {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Calculate interest earned from completed loans
    const interestQuery = await loanRepository
      .createQueryBuilder('loan')
      .select([
        'SUM(loan.amount * loan.interestRate / 100) as totalInterest',
        'SUM(CASE WHEN loan.createdAt >= :startOfMonth THEN loan.amount * loan.interestRate / 100 ELSE 0 END) as monthlyInterest',
      ])
      .where('loan.status IN (:...statuses)', {
        statuses: [LoanStatus.DISBURSED, LoanStatus.PAID]
      })
      .setParameters({ startOfMonth })
      .getRawOne();

    const totalInterestEarned = parseFloat(interestQuery.totalInterest) || 0;
    const monthlyRevenue = parseFloat(interestQuery.monthlyInterest) || 0;

    // Calculate cash flow (repayments - disbursements)
    const cashFlowQuery = await transactionRepository
      .createQueryBuilder('transaction')
      .select([
        'SUM(CASE WHEN transaction.type = :repayment THEN transaction.amount ELSE 0 END) - SUM(CASE WHEN transaction.type = :disbursement THEN transaction.amount ELSE 0 END) as cashFlow',
      ])
      .where('transaction.status = :completed')
      .setParameters({
        completed: TransactionStatus.COMPLETED,
        repayment: TransactionType.LOAN_REPAYMENT,
        disbursement: TransactionType.LOAN_DISBURSEMENT,
      })
      .getRawOne();

    const cashFlow = parseFloat(cashFlowQuery.cashFlow) || 0;
    const totalRevenue = totalInterestEarned;
    const profitMargin = totalRevenue > 0 ? (totalRevenue / totalRevenue) * 100 : 0; // Simplified calculation

    return {
      totalRevenue,
      monthlyRevenue,
      totalInterestEarned,
      cashFlow,
      profitMargin: Math.round(profitMargin * 100) / 100,
    };
  }

  // Get system statistics
  private async getSystemStatistics() {
    const [
      totalDocuments,
      pendingDocuments,
      approvedDocuments,
      rejectedDocuments,
    ] = await Promise.all([
      documentRepository.count(),
      documentRepository.count({ where: { documentStatus: DocumentStatus.PENDING } }),
      documentRepository.count({ where: { documentStatus: DocumentStatus.APPROVED } }),
      documentRepository.count({ where: { documentStatus: DocumentStatus.REJECTED } }),
    ]);

    return {
      totalDocuments,
      pendingDocuments,
      approvedDocuments,
      rejectedDocuments,
      systemUptime: 99.9, // This would typically come from monitoring service
    };
  }

  // Get recent transactions for dashboard
  async getRecentTransactions(limit: number = 10): Promise<RecentTransaction[]> {
    const transactions = await transactionRepository
      .createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.user', 'user')
      .orderBy('transaction.createdAt', 'DESC')
      .limit(limit)
      .getMany();

    return transactions.map(transaction => ({
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      description: transaction.description || `${transaction.type.replace('_', ' ')} transaction`,
      status: transaction.status,
      createdAt: transaction.createdAt,
      userName: transaction.user?.fullName || 'Unknown User',
    }));
  }
}
