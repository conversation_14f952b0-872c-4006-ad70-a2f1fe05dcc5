import { Repository } from 'typeorm';
import { AppDataSource } from '../data-source';
import { Document, DocumentStatus, DocumentType } from '../models/Document';
import { User } from '../models/User';
import * as fs from 'fs';
import * as path from 'path';
import * as util from 'util';
import { v4 as uuidv4 } from 'uuid';

const mkdir = util.promisify(fs.mkdir);
const writeFile = util.promisify(fs.writeFile);
const unlink = util.promisify(fs.unlink);

export class DocumentService {
  private documentRepository: Repository<Document>;
  private uploadDir: string;

  constructor() {
    this.documentRepository = AppDataSource.getRepository(Document);
    this.uploadDir = process.env.UPLOAD_DIR || './uploads';
    
    // Ensure upload directory exists
    this.ensureUploadDirExists();
  }

  private async ensureUploadDirExists(): Promise<void> {
    try {
      if (!fs.existsSync(this.uploadDir)) {
        await mkdir(this.uploadDir, { recursive: true });
      }
    } catch (error) {
      console.error('Error creating upload directory:', error);
    }
  }

  async getAllDocuments(): Promise<Document[]> {
    return this.documentRepository.find({
      relations: ['user', 'loan', 'uploadedBy', 'statusUpdatedBy'],
      order: { uploadDate: 'DESC' }
    });
  }

  async getDocumentsByUserId(userId: string): Promise<Document[]> {
    return this.documentRepository.find({
      where: { userId },
      relations: ['user', 'loan', 'uploadedBy', 'statusUpdatedBy'],
      order: { uploadDate: 'DESC' }
    });
  }

  async getDocumentsByLoanId(loanId: string): Promise<Document[]> {
    return this.documentRepository.find({
      where: { loanId },
      relations: ['user', 'loan', 'uploadedBy', 'statusUpdatedBy'],
      order: { uploadDate: 'DESC' }
    });
  }

  async getDocumentById(id: string): Promise<Document | null> {
    return this.documentRepository.findOne({
      where: { id },
      relations: ['user', 'loan', 'uploadedBy', 'statusUpdatedBy']
    });
  }

  async createDocument(
    file: Express.Multer.File,
    documentType: DocumentType,
    userId: string,
    uploadedById: string,
    loanId?: string,
  ): Promise<Document> {
    const document = new Document();
    document.userId = userId;
    document.loanId = loanId;
    document.documentType = documentType;
    document.originalFileName = file.originalname;
    document.mimeType = file.mimetype;
    document.fileSize = file.size;
    document.uploadedById = uploadedById;
    document.documentStatus = DocumentStatus.PENDING;

    // Generate a unique filename
    const fileExt = path.extname(file.originalname);
    const uniqueFileName = `${uuidv4()}${fileExt}`;
    document.fileName = uniqueFileName;

    // Store file either in filesystem or database based on configuration
    const storeInDatabase = process.env.STORE_FILES_IN_DB === 'true';

    if (storeInDatabase) {
      // Store file in database
      document.fileData = file.buffer;
    } else {
      // Store file in filesystem
      const filePath = path.join(this.uploadDir, uniqueFileName);
      await writeFile(filePath, file.buffer);
      document.filePath = filePath;
    }

    return this.documentRepository.save(document);
  }

  async updateDocumentStatus(
    id: string,
    status: DocumentStatus,
    statusUpdatedById: string,
    rejectionReason?: string
  ): Promise<Document | null> {
    const document = await this.getDocumentById(id);
    
    if (!document) {
      return null;
    }

    document.documentStatus = status;
    document.statusUpdatedAt = new Date();
    document.statusUpdatedById = statusUpdatedById;
    
    if (status === DocumentStatus.REJECTED && rejectionReason) {
      document.rejectionReason = rejectionReason;
    }

    return this.documentRepository.save(document);
  }

  async deleteDocument(id: string): Promise<boolean> {
    const document = await this.getDocumentById(id);
    
    if (!document) {
      return false;
    }

    // Delete file from filesystem if it exists
    if (document.filePath && fs.existsSync(document.filePath)) {
      await unlink(document.filePath);
    }

    await this.documentRepository.remove(document);
    return true;
  }

  async getDocumentFile(id: string): Promise<{ buffer: Buffer; mimeType: string; fileName: string } | null> {
    const document = await this.getDocumentById(id);
    
    if (!document) {
      return null;
    }

    let buffer: Buffer;

    if (document.fileData) {
      // File is stored in database
      buffer = document.fileData;
    } else if (document.filePath && fs.existsSync(document.filePath)) {
      // File is stored in filesystem
      buffer = fs.readFileSync(document.filePath);
    } else {
      return null;
    }

    return {
      buffer,
      mimeType: document.mimeType,
      fileName: document.originalFileName
    };
  }
}
