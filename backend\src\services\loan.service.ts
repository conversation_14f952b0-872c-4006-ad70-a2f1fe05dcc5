import { AppDataSource } from '../data-source';
import { Loan, LoanStatus } from '../models/Loan';
import { User } from '../models/User';
import { Transaction, TransactionType, TransactionStatus } from '../models/Transaction';
import { NotificationService } from './notification.service';
import { NotificationType, NotificationStatus } from '../models/Notification';

const loanRepository = AppDataSource.getRepository(Loan);
const userRepository = AppDataSource.getRepository(User);
const transactionRepository = AppDataSource.getRepository(Transaction);

export class LoanService {
  private notificationService: NotificationService;

  constructor() {
    this.notificationService = new NotificationService();
  }
  // Calculate total amount due for a loan (principal + interest)
  calculateTotalAmountDue(loan: Loan): number {
    const principal = Number(loan.amount);
    const rate = Number(loan.interestRate);

    // Calculate interest amount
    const interestAmount = !isNaN(principal) && !isNaN(rate) ? (principal * rate) / 100 : 0;

    // Return total amount due (principal + interest)
    return principal + interestAmount;
  }

  // Calculate outstanding amount for a loan (total due - amount paid)
  calculateOutstandingAmount(loan: Loan): number {
    const totalDue = this.calculateTotalAmountDue(loan);
    const amountPaid = Number(loan.amountPaid) || 0;

    return Math.max(0, totalDue - amountPaid);
  }

  // Calculate user's total outstanding loan amount
  async calculateOutstandingLoanAmount(userId: string): Promise<number> {
    // Find all active loans for the user (status: APPROVED or DISBURSED)
    const activeLoans = await loanRepository.find({
      where: [
        { user: { id: userId }, status: LoanStatus.APPROVED },
        { user: { id: userId }, status: LoanStatus.DISBURSED }
      ]
    });

    // Calculate total outstanding amount including interest
    let totalOutstanding = 0;
    for (const loan of activeLoans) {
      // For approved loans, the full total amount due is outstanding
      if (loan.status === LoanStatus.APPROVED) {
        totalOutstanding += this.calculateTotalAmountDue(loan);
      }
      // For disbursed loans, calculate remaining amount including interest
      else if (loan.status === LoanStatus.DISBURSED) {
        totalOutstanding += this.calculateOutstandingAmount(loan);
      }
    }

    return totalOutstanding;
  }

  // Calculate user's available credit
  async calculateAvailableCredit(userId: string): Promise<number> {
    const MAXIMUM_CREDIT_LIMIT = 600; // E600 maximum lifetime loan limit

    // Get total outstanding loan amount
    const outstandingAmount = await this.calculateOutstandingLoanAmount(userId);

    // Calculate available credit
    const availableCredit = Math.max(0, MAXIMUM_CREDIT_LIMIT - outstandingAmount);

    return availableCredit;
  }

  // Get all transactions related to a specific loan
  async getLoanTransactions(loanId: string): Promise<Transaction[]> {
    // Find all transactions related to this loan
    const transactions = await transactionRepository.find({
      where: [
        { reference: `DISBURSE-${loanId}` },
        { reference: `REPAY-${loanId}` }
      ],
      order: { createdAt: 'ASC' }
    });

    return transactions;
  }

  // Calculate payment schedule for a loan
  async calculatePaymentSchedule(loan: Loan, transactions: Transaction[]): Promise<any[]> {
    const schedule = [];

    // If loan is not yet disbursed, return empty schedule
    if (!loan.disbursedAt) {
      return schedule;
    }

    // Add disbursement as first item in schedule
    const disbursementTransaction = transactions.find(t =>
      t.type === TransactionType.LOAN_DISBURSEMENT && t.status === TransactionStatus.COMPLETED
    );

    if (disbursementTransaction) {
      schedule.push({
        date: disbursementTransaction.completedAt || loan.disbursedAt,
        amount: loan.amount,
        type: 'disbursement',
        status: 'completed',
        transactionId: disbursementTransaction.id
      });
    } else {
      schedule.push({
        date: loan.disbursedAt,
        amount: loan.amount,
        type: 'disbursement',
        status: 'completed',
        transactionId: null
      });
    }

    // Add all repayments
    const repaymentTransactions = transactions.filter(t =>
      t.type === TransactionType.LOAN_REPAYMENT && t.status === TransactionStatus.COMPLETED
    );

    for (const transaction of repaymentTransactions) {
      schedule.push({
        date: transaction.completedAt || transaction.createdAt,
        amount: transaction.amount,
        type: 'repayment',
        status: 'completed',
        transactionId: transaction.id
      });
    }

    // Calculate remaining amount
    const totalPaid = repaymentTransactions.reduce((sum, t) => sum + t.amount, 0);
    const remainingAmount = loan.amount - totalPaid;

    // If there's still an amount to be paid and the loan is not fully paid
    if (remainingAmount > 0 && loan.status !== LoanStatus.PAID) {
      // Add future payment (due date)
      const dueDate = loan.dueDate || new Date();
      const now = new Date();

      schedule.push({
        date: dueDate,
        amount: remainingAmount,
        type: 'repayment',
        status: now > dueDate ? 'overdue' : 'upcoming',
        transactionId: null
      });
    }

    // Sort by date
    return schedule.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  // Calculate loan health score (0-100)
  async calculateLoanHealthScore(loanId: string, userId: string): Promise<any> {
    // Find loan
    const loan = await loanRepository.findOne({
      where: { id: loanId, user: { id: userId } }
    });

    if (!loan) {
      throw new Error('Loan not found');
    }

    // Default score components
    let paymentTimelinessScore = 100;
    let amountPaidScore = 0;
    let timeToMaturityScore = 100;
    let overallScore = 0;

    // If loan is not disbursed yet, return perfect score
    if (loan.status === LoanStatus.PENDING || loan.status === LoanStatus.APPROVED) {
      return {
        score: 100,
        components: {
          paymentTimeliness: 100,
          amountPaid: 100,
          timeToMaturity: 100
        },
        status: 'excellent',
        details: 'Loan not yet disbursed'
      };
    }

    // If loan is paid, return perfect score
    if (loan.status === LoanStatus.PAID) {
      return {
        score: 100,
        components: {
          paymentTimeliness: 100,
          amountPaid: 100,
          timeToMaturity: 100
        },
        status: 'excellent',
        details: 'Loan fully paid'
      };
    }

    // Calculate amount paid score (0-100)
    if (loan.amount > 0) {
      amountPaidScore = Math.min(100, Math.round((loan.amountPaid / loan.amount) * 100));
    }

    // Calculate time to maturity score
    if (loan.dueDate && loan.disbursedAt) {
      const now = new Date();
      const dueDate = new Date(loan.dueDate);
      const disbursedDate = new Date(loan.disbursedAt);

      // Total loan duration in days
      const totalDuration = Math.max(1, (dueDate.getTime() - disbursedDate.getTime()) / (1000 * 60 * 60 * 24));

      // Days remaining until due date
      const daysRemaining = Math.max(0, (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      // If loan is overdue
      if (daysRemaining <= 0) {
        // Penalty increases with days overdue
        const daysOverdue = Math.abs(daysRemaining);
        paymentTimelinessScore = Math.max(0, 100 - Math.min(100, daysOverdue * 5)); // -5 points per day overdue
        timeToMaturityScore = 0;
      } else {
        // Score based on percentage of time remaining
        timeToMaturityScore = Math.round((daysRemaining / totalDuration) * 100);

        // If less than 20% of time remaining, start reducing score
        if (timeToMaturityScore < 20) {
          timeToMaturityScore = Math.max(50, timeToMaturityScore * 2.5);
        }
      }
    }

    // Calculate overall score (weighted average)
    overallScore = Math.round(
      (paymentTimelinessScore * 0.4) + // 40% weight for payment timeliness
      (amountPaidScore * 0.4) +         // 40% weight for amount paid
      (timeToMaturityScore * 0.2)       // 20% weight for time to maturity
    );

    // Determine status based on score
    let status = 'excellent';
    if (overallScore < 20) {
      status = 'critical';
    } else if (overallScore < 40) {
      status = 'poor';
    } else if (overallScore < 60) {
      status = 'fair';
    } else if (overallScore < 80) {
      status = 'good';
    }

    // Generate details message
    let details = '';
    if (loan.status === LoanStatus.OVERDUE) {
      details = 'Loan is overdue. Immediate payment recommended.';
    } else if (paymentTimelinessScore < 50) {
      details = 'Payment is delayed. Please make a payment soon.';
    } else if (timeToMaturityScore < 30) {
      details = 'Loan is approaching due date. Prepare for repayment.';
    } else if (amountPaidScore < 30) {
      details = 'Consider making a partial payment to improve loan health.';
    } else {
      details = 'Loan is in good standing.';
    }

    return {
      score: overallScore,
      components: {
        paymentTimeliness: paymentTimelinessScore,
        amountPaid: amountPaidScore,
        timeToMaturity: timeToMaturityScore
      },
      status,
      details
    };
  }

  // Apply for a new loan
  async applyForLoan(
    userId: string,
    loanData: {
      amount: number;
      purpose: string;
      termInMonths: number;
      collateral?: string;
    }
  ): Promise<Loan> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Validate loan amount
    if (loanData.amount <= 0) {
      throw new Error('Loan amount must be greater than zero');
    }

    // Validate loan term
    if (loanData.termInMonths < 0.5 || loanData.termInMonths > 5) {
      throw new Error('Loan term must be between 2 weeks and 5 months');
    }

    // Check available credit
    const availableCredit = await this.calculateAvailableCredit(userId);
    if (loanData.amount > availableCredit) {
      throw new Error(`Loan amount exceeds your available credit of E${availableCredit.toFixed(2)}`);
    }

    // Calculate interest rate based on term
    let interestRate = 0;
    if (loanData.termInMonths <= 1) {
      interestRate = 10; // 10% for very short-term loans (2 weeks to 1 month)
    } else if (loanData.termInMonths === 2) {
      interestRate = 20; // 20% for 2-month loans
    } else if (loanData.termInMonths === 3) {
      interestRate = 30; // 30% for 3-month loans
    } else if (loanData.termInMonths === 4) {
      interestRate = 40; // 40% for 4-month loans
    } else if (loanData.termInMonths === 5) {
      interestRate = 50; // 50% for 5-month loans
    }

    // Create new loan
    const loan = new Loan();
    loan.user = user;
    loan.amount = loanData.amount;
    loan.purpose = loanData.purpose;
    loan.collateral = loanData.collateral || null; // Store collateral if provided
    console.log('Setting loan collateral to:', loan.collateral);
    loan.termInMonths = loanData.termInMonths;
    loan.interestRate = interestRate;
    loan.status = LoanStatus.PENDING;
    loan.amountPaid = 0;

    console.log('Loan object before saving:', {
      amount: loan.amount,
      purpose: loan.purpose,
      collateral: loan.collateral,
      termInMonths: loan.termInMonths
    });

    // Save loan
    const savedLoan = await loanRepository.save(loan);

    console.log('Loan saved with ID:', savedLoan.id);
    console.log('Saved loan collateral:', savedLoan.collateral);

    // Create notification for admins
    try {
      await this.notificationService.createNotification({
        title: 'New Loan Application',
        message: `A new loan application for E${loanData.amount} has been submitted by ${user.fullName}. Purpose: ${loanData.purpose}`,
        type: NotificationType.LOAN,
        status: NotificationStatus.SENT,
        isAllUsers: false,
        isAdminOnly: true, // Send to all admins
        createdById: userId
      });
      console.log('Admin notification created for new loan application');
    } catch (error) {
      console.error('Failed to create admin notification for new loan application:', error);
      // Don't throw error, just log it - we don't want to fail the loan application if notification fails
    }

    return savedLoan;
  }

  // Get all loans for a user
  async getUserLoans(userId: string): Promise<Loan[]> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Get loans
    return loanRepository.find({
      where: { user: { id: userId } },
      order: { createdAt: 'DESC' }
    });
  }

  // Get filtered loans for a user
  async getUserLoansFiltered(userId: string, filters: any = {}): Promise<Loan[]> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Build query conditions
    const queryBuilder = loanRepository.createQueryBuilder('loan')
      .leftJoinAndSelect('loan.user', 'user')
      .where('user.id = :userId', { userId });

    // Apply filters
    if (filters.status) {
      // For enum types, we need to use exact matching without LOWER() function
      queryBuilder.andWhere('loan.status = :status', { status: filters.status });
    }

    if (filters.minAmount) {
      queryBuilder.andWhere('loan.amount >= :minAmount', { minAmount: filters.minAmount });
    }

    if (filters.maxAmount) {
      queryBuilder.andWhere('loan.amount <= :maxAmount', { maxAmount: filters.maxAmount });
    }

    if (filters.startDate) {
      queryBuilder.andWhere('loan.createdAt >= :startDate', { startDate: new Date(filters.startDate) });
    }

    if (filters.endDate) {
      // Add one day to include the end date fully
      const endDate = new Date(filters.endDate);
      endDate.setDate(endDate.getDate() + 1);
      queryBuilder.andWhere('loan.createdAt < :endDate', { endDate });
    }

    if (filters.search) {
      // Get matching statuses if any
      const matchingStatuses = Object.values(LoanStatus).filter(status =>
        status.toLowerCase().includes(filters.search.toLowerCase())
      );

      // Build the search condition
      const searchConditions = [
        'loan.purpose ILIKE :search',
        'CAST(loan.id as TEXT) ILIKE :search'
      ];

      // Add status condition if we have matching statuses
      if (matchingStatuses.length > 0) {
        searchConditions.push('loan.status IN (:...statuses)');
      }

      // Combine all conditions with OR, but keep them within parentheses to maintain AND with user filter
      queryBuilder.andWhere(`(${searchConditions.join(' OR ')})`, {
        search: `%${filters.search}%`,
        statuses: matchingStatuses
      });
    }

    // Order by creation date (newest first)
    queryBuilder.orderBy('loan.createdAt', 'DESC');

    // Execute query
    return queryBuilder.getMany();
  }

  // Get a specific loan
  async getLoanById(loanId: string, userId?: string): Promise<Loan> {
    // Find loan
    const queryOptions: any = { where: { id: loanId } };
    if (userId) {
      queryOptions.where.user = { id: userId };
    }

    const loan = await loanRepository.findOne(queryOptions);
    if (!loan) {
      throw new Error('Loan not found');
    }

    return loan;
  }

  // Get all loans with filtering and pagination (for admin)
  async getAllLoans(options: {
    status?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ data: any[]; pagination: { total: number; pages: number; page: number; limit: number } }> {
    const { status, search, page = 1, limit = 10 } = options;

    // Build the where clause
    const whereClause: any = {};

    // Add status filter if provided
    if (status) {
      whereClause.status = status.toLowerCase();
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await loanRepository.count({
      where: whereClause
    });

    // Get loans with relations
    const loans = await loanRepository.find({
      where: whereClause,
      relations: ['user'],
      order: { createdAt: 'DESC' },
      skip,
      take: limit
    });

    // Format the loans with user information
    const formattedLoans = loans.map(loan => {
      // Calculate interest amount (principal × interest rate percentage)
      // Ensure we're using numeric values for the calculation
      const principal = Number(loan.amount);
      const rate = Number(loan.interestRate);

      // Calculate interest amount with proper validation
      const interestAmount = !isNaN(principal) && !isNaN(rate) ? (principal * rate) / 100 : 0;

      // Calculate total payment (principal + interest)
      const totalPayment = principal + interestAmount;

      // Log the calculation for debugging
      console.log(`Loan ${loan.id} calculation:`, {
        principal,
        rate,
        interestAmount,
        totalPayment
      });

      return {
        id: loan.id,
        userId: loan.user.id,
        userName: loan.user.fullName,
        userPhoneNumber: loan.user.phoneNumber, // Add phone number for admin table
        amount: principal,
        purpose: loan.purpose,
        status: loan.status,
        applicationDate: loan.createdAt,
        approvalDate: loan.approvedAt,
        dueDate: loan.dueDate,
        interestRate: rate,
        principalAmount: principal, // Original loan amount
        totalPayment: totalPayment, // Total amount including interest
        amountDue: totalPayment, // Keep for backward compatibility
        isPaid: loan.status === LoanStatus.PAID,
        paidDate: loan.paidAt
      };
    });

    // Apply search filter if provided (client-side filtering for simplicity)
    let filteredLoans = formattedLoans;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredLoans = formattedLoans.filter(loan =>
        loan.id.toLowerCase().includes(searchLower) ||
        loan.userName.toLowerCase().includes(searchLower) ||
        loan.userId.toLowerCase().includes(searchLower) ||
        loan.purpose.toLowerCase().includes(searchLower)
      );
    }

    return {
      data: filteredLoans,
      pagination: {
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
        page,
        limit
      }
    };
  }

  // Get all pending loan applications (for admin)
  async getPendingLoans(): Promise<Loan[]> {
    return loanRepository.find({
      where: { status: LoanStatus.PENDING },
      order: { createdAt: 'ASC' }
    });
  }

  // Approve a loan application (admin only)
  async approveLoan(loanId: string, adminId: string): Promise<Loan> {
    // Find loan
    const loan = await loanRepository.findOne({
      where: { id: loanId },
      relations: ['user']
    });
    if (!loan) {
      throw new Error('Loan not found');
    }

    // Check if loan is in pending status
    if (loan.status !== LoanStatus.PENDING) {
      throw new Error('Only pending loans can be approved');
    }

    // Update loan status
    loan.status = LoanStatus.APPROVED;
    loan.approvedAt = new Date();

    // Save loan
    const savedLoan = await loanRepository.save(loan);

    // Create notification for the user
    try {
      await this.notificationService.createNotification({
        title: 'Loan Application Approved',
        message: `Your loan application for E${loan.amount} has been approved. The funds will be disbursed soon.`,
        type: NotificationType.LOAN,
        status: NotificationStatus.SENT,
        recipientId: loan.user.id,
        isAllUsers: false,
        createdById: adminId
      });
      console.log('User notification created for loan approval');
    } catch (error) {
      console.error('Failed to create user notification for loan approval:', error);
      // Don't throw error, just log it
    }

    return savedLoan;
  }

  // Reject a loan application (admin only)
  async rejectLoan(loanId: string, adminId: string, rejectionReason: string): Promise<Loan> {
    // Find loan
    const loan = await loanRepository.findOne({
      where: { id: loanId },
      relations: ['user']
    });
    if (!loan) {
      throw new Error('Loan not found');
    }

    // Check if loan is in pending status
    if (loan.status !== LoanStatus.PENDING) {
      throw new Error('Only pending loans can be rejected');
    }

    // Update loan status
    loan.status = LoanStatus.REJECTED;

    // Save loan
    const savedLoan = await loanRepository.save(loan);

    // Create notification for the user
    try {
      const reasonText = rejectionReason ? ` Reason: ${rejectionReason}` : '';
      await this.notificationService.createNotification({
        title: 'Loan Application Rejected',
        message: `Your loan application for E${loan.amount} has been rejected.${reasonText}`,
        type: NotificationType.LOAN,
        status: NotificationStatus.SENT,
        recipientId: loan.user.id,
        isAllUsers: false,
        createdById: adminId
      });
      console.log('User notification created for loan rejection');
    } catch (error) {
      console.error('Failed to create user notification for loan rejection:', error);
      // Don't throw error, just log it
    }

    return savedLoan;
  }

  // Disburse an approved loan (admin only)
  async disburseLoan(loanId: string, adminId: string): Promise<Loan> {
    // Find loan
    const loan = await loanRepository.findOne({
      where: { id: loanId },
      relations: ['user']
    });
    if (!loan) {
      throw new Error('Loan not found');
    }

    // Check if loan is in approved status
    if (loan.status !== LoanStatus.APPROVED) {
      throw new Error('Only approved loans can be disbursed');
    }

    // Update loan status
    loan.status = LoanStatus.DISBURSED;
    loan.disbursedAt = new Date();

    // Calculate due date (term in months from disbursement date)
    const dueDate = new Date(loan.disbursedAt);
    dueDate.setMonth(dueDate.getMonth() + loan.termInMonths);
    loan.dueDate = dueDate;

    // Create disbursement transaction
    const transaction = new Transaction();
    transaction.user = loan.user;
    transaction.amount = loan.amount;
    transaction.type = TransactionType.LOAN_DISBURSEMENT;
    transaction.status = TransactionStatus.COMPLETED;
    transaction.reference = `DISBURSE-${loanId}`;
    transaction.description = `Loan disbursement for $${loan.amount}`;
    transaction.completedAt = new Date();

    // Save transaction
    await transactionRepository.save(transaction);

    // Save loan
    const savedLoan = await loanRepository.save(loan);

    // Create notification for the user
    try {
      const dueDate = loan.dueDate ? new Date(loan.dueDate).toLocaleDateString() : 'N/A';
      await this.notificationService.createNotification({
        title: 'Loan Disbursed',
        message: `Your loan of E${loan.amount} has been disbursed. The due date for repayment is ${dueDate}.`,
        type: NotificationType.LOAN,
        status: NotificationStatus.SENT,
        recipientId: loan.user.id,
        isAllUsers: false,
        createdById: adminId
      });
      console.log('User notification created for loan disbursement');
    } catch (error) {
      console.error('Failed to create user notification for loan disbursement:', error);
      // Don't throw error, just log it
    }

    return savedLoan;
  }



  // Make a loan repayment with database simulation
  async makeRepayment(loanId: string, userId: string, amount: number, payerPhoneNumber?: string): Promise<{ loan: Loan, transaction: Transaction }> {
    // Debug logging
    console.log('🔍 makeRepayment called with:', {
      loanId,
      userId,
      amount,
      amountType: typeof amount,
      payerPhoneNumber
    });

    // Find loan
    const loan = await loanRepository.findOne({
      where: { id: loanId, user: { id: userId } }
    });
    if (!loan) {
      throw new Error('Loan not found');
    }

    // Check if loan is in disbursed status
    if (loan.status !== LoanStatus.DISBURSED) {
      throw new Error('Only disbursed loans can be repaid');
    }

    // Validate payment amount
    if (amount <= 0) {
      throw new Error('Payment amount must be greater than zero');
    }

    console.log('🔍 Amount validation passed:', amount);

    // Calculate total amount due and outstanding amount
    const totalAmountDue = this.calculateTotalAmountDue(loan);
    const outstandingAmount = this.calculateOutstandingAmount(loan);

    // Validate payment amount against outstanding balance
    if (amount > outstandingAmount) {
      throw new Error(`Payment amount exceeds remaining balance: E${outstandingAmount.toFixed(2)}`);
    }

    // Create repayment transaction first (pending status)
    const transaction = new Transaction();
    transaction.user = await userRepository.findOne({ where: { id: userId } }) as User;
    transaction.amount = amount;
    transaction.type = TransactionType.LOAN_REPAYMENT;
    transaction.status = TransactionStatus.PENDING;
    transaction.reference = `REPAY-${loanId}`;
    transaction.description = `Loan repayment of E${amount}`;

    console.log('🔍 Before saving transaction:', {
      amount: transaction.amount,
      amountType: typeof transaction.amount,
      description: transaction.description
    });

    // Save transaction
    const savedTransaction = await transactionRepository.save(transaction);

    console.log('🔍 After saving transaction:', {
      id: savedTransaction.id,
      amount: savedTransaction.amount,
      amountType: typeof savedTransaction.amount,
      description: savedTransaction.description
    });

    // Simulate payment processing - mark as completed immediately
    savedTransaction.status = TransactionStatus.COMPLETED;
    savedTransaction.completedAt = new Date();

    // Set payment method metadata
    const paymentMethod = payerPhoneNumber ? 'mobile_money' : 'cash';
    savedTransaction.metadata = JSON.stringify({
      paymentMethod,
      simulatedPayment: true,
      ...(payerPhoneNumber && { phoneNumber: payerPhoneNumber })
    });

    await transactionRepository.save(savedTransaction);

    console.log('🔍 After final transaction save:', {
      id: savedTransaction.id,
      amount: savedTransaction.amount,
      amountType: typeof savedTransaction.amount
    });

    // Create notification for successful payment
    await this.notificationService.createNotification({
      recipientId: userId,
      title: 'Payment Successful',
      message: `Your payment of E${amount} has been processed successfully.`,
      type: NotificationType.PAYMENT,
      status: NotificationStatus.SENT
    });

    console.log('🔍 Before updating loan amountPaid:', {
      currentAmountPaid: loan.amountPaid,
      paymentAmount: amount,
      amountType: typeof amount
    });

    // Update loan amount paid
    loan.amountPaid += amount;

    console.log('🔍 After updating loan amountPaid:', {
      newAmountPaid: loan.amountPaid
    });

    // Check if loan is fully paid (compare against total amount due)
    if (loan.amountPaid >= totalAmountDue) {
      loan.status = LoanStatus.PAID;
      loan.paidAt = new Date();
    }

    // Save loan
    const savedLoan = await loanRepository.save(loan);

    return { loan: savedLoan, transaction: savedTransaction };
  }

  // Get simulated account balance
  async getSimulatedAccountBalance(): Promise<{ availableBalance: string; currency: string }> {
    try {
      // Base balance to start with
      const BASE_BALANCE = 25000.00;

      // Get all completed transactions
      const completedTransactions = await transactionRepository.find({
        where: { status: TransactionStatus.COMPLETED },
        order: { createdAt: 'ASC' }
      });

      // Calculate balance based on transaction history
      let currentBalance = BASE_BALANCE;

      for (const transaction of completedTransactions) {
        const amount = parseFloat(transaction.amount.toString());

        if (transaction.type === TransactionType.LOAN_REPAYMENT) {
          // Repayments increase the balance
          currentBalance += amount;
        } else if (transaction.type === TransactionType.LOAN_DISBURSEMENT) {
          // Disbursements decrease the balance
          currentBalance -= amount;
        }
        // Note: Deposits and withdrawals could also be handled here if needed
      }

      // Ensure balance doesn't go negative (minimum 0)
      currentBalance = Math.max(0, currentBalance);

      console.log(`Calculated simulated balance: E${currentBalance.toFixed(2)} from ${completedTransactions.length} transactions`);

      return {
        availableBalance: currentBalance.toFixed(2),
        currency: 'SZL',
      };
    } catch (error) {
      console.error('Error calculating simulated balance:', error);
      // Fallback to base balance if calculation fails
      return {
        availableBalance: '25000.00',
        currency: 'SZL',
      };
    }
  }

  // Get loan statistics for a user
  async getUserLoanStatistics(userId: string): Promise<any> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Get all loans for the user
    const loans = await loanRepository.find({
      where: { user: { id: userId } }
    });

    // Calculate statistics
    const totalLoanAmount = loans.reduce((total, loan) => total + loan.amount, 0);
    const totalAmountPaid = loans.reduce((total, loan) => total + loan.amountPaid, 0);
    const totalAmountRemaining = totalLoanAmount - totalAmountPaid;

    const activeLoanCount = loans.filter(loan =>
      loan.status === LoanStatus.DISBURSED ||
      loan.status === LoanStatus.APPROVED
    ).length;

    const completedLoanCount = loans.filter(loan =>
      loan.status === LoanStatus.PAID
    ).length;

    return {
      totalLoanAmount,
      totalAmountPaid,
      totalAmountRemaining,
      activeLoanCount,
      completedLoanCount,
      totalLoanCount: loans.length
    };
  }

  // Get all active loans for a user
  async getUserActiveLoans(userId: string): Promise<Loan[]> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Get all active loans for the user (status: APPROVED or DISBURSED)
    return loanRepository.find({
      where: [
        { user: { id: userId }, status: LoanStatus.APPROVED },
        { user: { id: userId }, status: LoanStatus.DISBURSED }
      ],
      order: {
        createdAt: 'DESC'
      }
    });
  }

  // Check for overdue loans and create notifications
  async checkOverdueLoans(): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day

    // Find all disbursed loans that are past their due date
    const overdueLoans = await loanRepository.find({
      where: {
        status: LoanStatus.DISBURSED,
        dueDate: (() => {
          const qb = loanRepository.createQueryBuilder('loan');
          return qb.where('loan.dueDate < :today', { today }).getQuery();
        })()
      },
      relations: ['user']
    });

    console.log(`Found ${overdueLoans.length} overdue loans`);

    let notificationCount = 0;

    // Create notifications for each overdue loan
    for (const loan of overdueLoans) {
      try {
        // Calculate days overdue
        const dueDate = new Date(loan.dueDate);
        const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

        // Create notification for the user
        await this.notificationService.createNotification({
          title: 'Loan Payment Overdue',
          message: `Your loan payment of E${loan.amount} is overdue by ${daysOverdue} days. Please make a payment as soon as possible to avoid additional interest charges.`,
          type: NotificationType.LOAN,
          status: NotificationStatus.SENT,
          recipientId: loan.user.id,
          isAllUsers: false,
          createdById: 'system' // System-generated notification
        });

        notificationCount++;
        console.log(`Created overdue notification for loan ${loan.id}, user ${loan.user.id}`);
      } catch (error) {
        console.error(`Failed to create overdue notification for loan ${loan.id}:`, error);
      }
    }

    return notificationCount;
  }

  // Apply late payment penalties and create notifications
  async applyLatePaymentPenalties(): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day

    // Find all disbursed loans that are past their due date
    const overdueLoans = await loanRepository.find({
      where: {
        status: LoanStatus.DISBURSED,
        dueDate: (() => {
          const qb = loanRepository.createQueryBuilder('loan');
          return qb.where('loan.dueDate < :today', { today }).getQuery();
        })()
      },
      relations: ['user']
    });

    console.log(`Found ${overdueLoans.length} loans for late payment penalties`);

    let penaltyCount = 0;

    // Apply penalties for each overdue loan
    for (const loan of overdueLoans) {
      try {
        // Calculate days overdue
        const dueDate = new Date(loan.dueDate);
        const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));

        // Apply penalty only if it's been at least 30 days since the due date
        // and we haven't already applied a penalty in the last 30 days
        const lastPenaltyDate = loan.lastPenaltyDate ? new Date(loan.lastPenaltyDate) : null;
        const daysSinceLastPenalty = lastPenaltyDate ?
          Math.floor((today.getTime() - lastPenaltyDate.getTime()) / (1000 * 60 * 60 * 24)) :
          Infinity;

        if (daysOverdue >= 30 && daysSinceLastPenalty >= 30) {
          // Increase interest rate by 2%
          const oldInterestRate = loan.interestRate;
          loan.interestRate += 2;
          loan.lastPenaltyDate = today;

          // Save the updated loan
          await loanRepository.save(loan);

          // Create notification for the user
          await this.notificationService.createNotification({
            title: 'Late Payment Penalty Applied',
            message: `Due to your loan payment being ${daysOverdue} days overdue, your interest rate has been increased from ${oldInterestRate}% to ${loan.interestRate}%. Please make a payment as soon as possible to avoid further penalties.`,
            type: NotificationType.LOAN,
            status: NotificationStatus.SENT,
            recipientId: loan.user.id,
            isAllUsers: false,
            createdById: 'system' // System-generated notification
          });

          penaltyCount++;
          console.log(`Applied late payment penalty to loan ${loan.id}, user ${loan.user.id}`);
        }
      } catch (error) {
        console.error(`Failed to apply late payment penalty for loan ${loan.id}:`, error);
      }
    }

    return penaltyCount;
  }
}