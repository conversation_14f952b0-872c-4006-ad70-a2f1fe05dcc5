import { AppDataSource } from '../data-source';
import { Notification, NotificationStatus, NotificationType } from '../models/Notification';
import { User } from '../models/User';

export class NotificationService {
  private notificationRepository = AppDataSource.getRepository(Notification);
  private userRepository = AppDataSource.getRepository(User);

  // Create a notification
  async createNotification(data: {
    title: string;
    message: string;
    type: NotificationType;
    status: NotificationStatus;
    recipientId?: string;
    isAllUsers?: boolean;
    isAdminOnly?: boolean;
    scheduledFor?: Date;
    createdById: string;
  }): Promise<Notification> {
    const notification = this.notificationRepository.create({
      title: data.title,
      message: data.message,
      type: data.type,
      status: data.status,
      recipientId: data.recipientId,
      isAllUsers: data.isAllUsers || false,
      isAdminOnly: data.isAdminOnly || false,
      scheduledFor: data.scheduledFor,
      createdBy: { id: data.createdById }
    });

    return this.notificationRepository.save(notification);
  }

  // Get all notifications with pagination and filtering
  async getAllNotifications(options: {
    page?: number;
    limit?: number;
    type?: NotificationType;
    status?: NotificationStatus;
    search?: string;
  }): Promise<{ data: Notification[]; pagination: { total: number; pages: number; page: number; limit: number } }> {
    const { page = 1, limit = 10, type, status, search } = options;

    // Build the where clause
    const whereClause: any = {};
    if (type) whereClause.type = type;
    if (status) whereClause.status = status;

    // Add search condition if provided
    let queryBuilder = this.notificationRepository.createQueryBuilder('notification');

    if (whereClause.type) queryBuilder = queryBuilder.andWhere('notification.type = :type', { type: whereClause.type });
    if (whereClause.status) queryBuilder = queryBuilder.andWhere('notification.status = :status', { status: whereClause.status });

    if (search) {
      queryBuilder = queryBuilder.andWhere(
        '(notification.title LIKE :search OR notification.message LIKE :search OR notification.id LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Count total records for pagination
    const total = await queryBuilder.getCount();

    // Add pagination
    const skip = (page - 1) * limit;
    queryBuilder = queryBuilder
      .orderBy('notification.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    // Execute query
    const notifications = await queryBuilder.getMany();

    return {
      data: notifications,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit
      }
    };
  }

  // Get notification by ID
  async getNotificationById(id: string): Promise<Notification | null> {
    return this.notificationRepository.findOne({ where: { id } });
  }

  // Update notification
  async updateNotification(id: string, data: Partial<Notification>): Promise<Notification | null> {
    await this.notificationRepository.update(id, data);
    return this.getNotificationById(id);
  }

  // Delete notification
  async deleteNotification(id: string): Promise<boolean> {
    const result = await this.notificationRepository.delete(id);
    return result.affected !== undefined && result.affected > 0;
  }

  // Get user notifications
  async getUserNotifications(userId: string, options: {
    page?: number;
    limit?: number;
    read?: boolean;
    isAdmin?: boolean;
  }): Promise<{ data: Notification[]; pagination: { total: number; pages: number; page: number; limit: number } }> {
    const { page = 1, limit = 10, read, isAdmin = false } = options;

    let queryBuilder = this.notificationRepository.createQueryBuilder('notification')
      .where('(notification.recipientId = :userId OR notification.isAllUsers = :isAllUsers)',
        { userId, isAllUsers: true });

    // If user is not an admin, exclude admin-only notifications
    if (!isAdmin) {
      queryBuilder = queryBuilder.andWhere('notification.isAdminOnly = :isAdminOnly', { isAdminOnly: false });
    }

    if (read !== undefined) {
      queryBuilder = queryBuilder.andWhere('notification.isRead = :read', { read });
    }

    // Count total records for pagination
    const total = await queryBuilder.getCount();

    // Add pagination
    const skip = (page - 1) * limit;
    queryBuilder = queryBuilder
      .orderBy('notification.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    // Execute query
    const notifications = await queryBuilder.getMany();

    return {
      data: notifications,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit
      }
    };
  }

  // Mark notification as read
  async markAsRead(id: string): Promise<boolean> {
    const result = await this.notificationRepository.update(id, { isRead: true });
    return result.affected !== undefined && result.affected > 0;
  }

  // Mark all user notifications as read
  async markAllAsRead(userId: string): Promise<boolean> {
    // Get user role
    const user = await this.userRepository.findOne({ where: { id: userId } });
    const isAdmin = user?.role === 'admin' || user?.role === 'staff';

    let result;

    if (isAdmin) {
      // For admins, mark all admin notifications as read
      result = await this.notificationRepository.update(
        { isAdminOnly: true, isRead: false },
        { isRead: true }
      );
    } else {
      // For regular users, mark only their notifications as read
      result = await this.notificationRepository.update(
        { recipientId: userId, isRead: false },
        { isRead: true }
      );
    }

    return result.affected !== undefined && result.affected > 0;
  }

  // Send scheduled notifications
  async sendScheduledNotifications(): Promise<number> {
    const now = new Date();

    // Find all scheduled notifications that are due using query builder
    const scheduledNotifications = await this.notificationRepository
      .createQueryBuilder('notification')
      .where('notification.status = :status', { status: NotificationStatus.SCHEDULED })
      .andWhere('notification.scheduledFor <= :now', { now })
      .getMany();

    if (scheduledNotifications.length === 0) return 0;

    // Update their status to sent
    await this.notificationRepository.update(
      scheduledNotifications.map(n => n.id),
      { status: NotificationStatus.SENT }
    );

    return scheduledNotifications.length;
  }

  // Get admin notifications
  async getAdminNotifications(options: {
    page?: number;
    limit?: number;
    read?: boolean;
  }): Promise<{ data: Notification[]; pagination: { total: number; pages: number; page: number; limit: number } }> {
    const { page = 1, limit = 10, read } = options;

    let queryBuilder = this.notificationRepository.createQueryBuilder('notification')
      .where('notification.isAdminOnly = :isAdminOnly', { isAdminOnly: true });

    if (read !== undefined) {
      queryBuilder = queryBuilder.andWhere('notification.isRead = :read', { read });
    }

    // Count total records for pagination
    const total = await queryBuilder.getCount();

    // Add pagination
    const skip = (page - 1) * limit;
    queryBuilder = queryBuilder
      .orderBy('notification.createdAt', 'DESC')
      .skip(skip)
      .take(limit);

    // Execute query
    const notifications = await queryBuilder.getMany();

    return {
      data: notifications,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit
      }
    };
  }
}
