import { AppDataSource } from '../data-source';
import { User } from '../models/User';
import { Otp, OtpPurpose, OtpStatus } from '../models/Otp';
import twilio from 'twilio';

// Twilio configuration
const accountSid = process.env.TWILIO_ACCOUNT_SID || '**********************************';
const authToken = process.env.TWILIO_AUTH_TOKEN || '3be916badd66cdeaac3b293e9d25193f';
const twilioPhoneNumber = process.env.TWILIO_PHONE_NUMBER || '';

// Initialize Twilio client
const twilioClient = twilio(accountSid, authToken);

// Repositories
const userRepository = AppDataSource.getRepository(User);
const otpRepository = AppDataSource.getRepository(Otp);

// OTP expiration time in minutes
const OTP_EXPIRY_MINUTES = 10;
// Maximum OTP verification attempts
const MAX_OTP_ATTEMPTS = 5;

export class OtpService {
  /**
   * Generate a random OTP code
   * @param length Length of the OTP code
   * @returns Random OTP code
   */
  private generateOtpCode(length: number = 6): string {
    const digits = '**********';
    let otp = '';

    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)];
    }

    return otp;
  }

  /**
   * Create a new OTP record
   * @param phoneNumber Phone number to send OTP to
   * @param userId Optional user ID
   * @param purpose Purpose of the OTP
   * @returns Created OTP record
   */
  private async createOtpRecord(
    phoneNumber: string,
    userId?: string,
    purpose: OtpPurpose = OtpPurpose.PHONE_VERIFICATION
  ): Promise<Otp> {
    // Generate OTP code
    const code = this.generateOtpCode();

    // Calculate expiry time
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + OTP_EXPIRY_MINUTES);

    // Create OTP record
    const otp = otpRepository.create({
      code,
      phoneNumber,
      userId,
      purpose,
      status: OtpStatus.PENDING,
      expiresAt,
      attempts: 0
    });

    return otpRepository.save(otp);
  }

  /**
   * Send OTP via SMS
   * @param phoneNumber Phone number to send OTP to
   * @param otpCode OTP code
   */
  private async sendOtpSms(phoneNumber: string, otpCode: string): Promise<void> {
    try {
      if (!twilioPhoneNumber) {
        console.warn('Twilio phone number not configured. SMS will not be sent.');
        return;
      }

      // Format the message
      const message = `Your LoanEase verification code is: ${otpCode}. Valid for ${OTP_EXPIRY_MINUTES} minutes.`;

      console.log(`Attempting to send SMS to ${phoneNumber} from ${twilioPhoneNumber} with code ${otpCode}`);
      console.log(`Using Twilio credentials: SID=${accountSid.substring(0, 10)}... Token=${authToken.substring(0, 5)}...`);

      try {
        // Send SMS via Twilio
        const result = await twilioClient.messages.create({
          body: message,
          from: twilioPhoneNumber,
          to: phoneNumber
        });

        console.log(`SMS sent successfully with SID: ${result.sid}`);
      } catch (twilioError) {
        console.error('Twilio API error:', twilioError);

        // For development/testing, we'll simulate success if Twilio fails
        console.log('⚠️ DEVELOPMENT MODE: Simulating successful SMS delivery');
        console.log(`The OTP code is: ${otpCode}`);

        // Don't throw an error in development mode
        return;
      }
    } catch (error) {
      console.error('Error in sendOtpSms function:', error);
      // For development, don't throw an error
      console.log('⚠️ DEVELOPMENT MODE: Simulating successful SMS delivery');
      console.log(`The OTP code is: ${otpCode}`);
    }
  }

  /**
   * Generate and send OTP to a phone number
   * @param phoneNumber Phone number to send OTP to
   * @param userId Optional user ID
   * @returns Success status
   */
  async generateAndSendOtp(
    phoneNumber: string,
    userId?: string
  ): Promise<{ success: boolean; message: string; otpId?: string }> {
    try {
      // Validate phone number format
      if (!phoneNumber || !/^\+?[1-9]\d{1,14}$/.test(phoneNumber)) {
        return {
          success: false,
          message: 'Invalid phone number format. Please use international format (e.g., +1234567890).'
        };
      }

      // Check if there's a recent OTP for this phone number using query builder
      const recentOtp = await otpRepository
        .createQueryBuilder('otp')
        .where('otp.phoneNumber = :phoneNumber', { phoneNumber })
        .andWhere('otp.status = :status', { status: OtpStatus.PENDING })
        .andWhere('otp.expiresAt > :now', { now: new Date() })
        .orderBy('otp.createdAt', 'DESC')
        .getOne();

      // For development, we'll allow new OTPs more frequently
      // In production, you might want to add rate limiting here
      if (recentOtp) {
        console.log('Found recent OTP, but allowing new one for development purposes');

        // Expire the previous OTP
        recentOtp.status = OtpStatus.EXPIRED;
        await otpRepository.save(recentOtp);
      }

      // Create new OTP record
      const otp = await this.createOtpRecord(phoneNumber, userId);

      // Send OTP via SMS
      await this.sendOtpSms(phoneNumber, otp.code);

      return {
        success: true,
        message: 'OTP sent successfully',
        otpId: otp.id
      };
    } catch (error) {
      console.error('Error generating and sending OTP:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send OTP'
      };
    }
  }

  /**
   * Verify OTP code
   * @param phoneNumber Phone number associated with the OTP
   * @param otpCode OTP code to verify
   * @returns Verification result
   */
  async verifyOtp(
    phoneNumber: string,
    otpCode: string
  ): Promise<{ success: boolean; message: string; userId?: string }> {
    try {
      // Find the most recent pending OTP for this phone number
      const otp = await otpRepository.findOne({
        where: {
          phoneNumber,
          status: OtpStatus.PENDING
        },
        order: { createdAt: 'DESC' }
      });

      if (!otp) {
        return {
          success: false,
          message: 'No pending OTP found for this phone number'
        };
      }

      // Check if OTP is expired
      if (new Date() > otp.expiresAt) {
        // Update OTP status to expired
        otp.status = OtpStatus.EXPIRED;
        await otpRepository.save(otp);

        return {
          success: false,
          message: 'OTP has expired'
        };
      }

      // Increment attempts
      otp.attempts += 1;

      // Check if max attempts reached
      if (otp.attempts > MAX_OTP_ATTEMPTS) {
        otp.status = OtpStatus.EXPIRED;
        await otpRepository.save(otp);

        return {
          success: false,
          message: 'Maximum verification attempts exceeded'
        };
      }

      // Check if OTP matches
      if (otp.code !== otpCode) {
        await otpRepository.save(otp);

        return {
          success: false,
          message: 'Invalid OTP code'
        };
      }

      // OTP is valid - mark as verified
      otp.status = OtpStatus.VERIFIED;
      otp.verifiedAt = new Date();
      await otpRepository.save(otp);

      // If there's a user associated with this OTP, mark their phone as verified
      if (otp.userId) {
        const user = await userRepository.findOne({ where: { id: otp.userId } });
        if (user) {
          user.isPhoneVerified = true;
          await userRepository.save(user);
        }
      }

      return {
        success: true,
        message: 'OTP verified successfully',
        userId: otp.userId
      };
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to verify OTP'
      };
    }
  }

  /**
   * Resend OTP to a phone number
   * @param phoneNumber Phone number to resend OTP to
   * @param userId Optional user ID
   * @returns Success status
   */
  async resendOtp(
    phoneNumber: string,
    userId?: string
  ): Promise<{ success: boolean; message: string; otpId?: string }> {
    try {
      // Find the most recent OTP for this phone number
      const recentOtp = await otpRepository.findOne({
        where: { phoneNumber },
        order: { createdAt: 'DESC' }
      });

      // For development, we'll allow resending OTPs more frequently
      if (recentOtp) {
        console.log('Found recent OTP, but allowing resend for development purposes');

        // If the recent OTP is still pending, mark it as expired
        if (recentOtp.status === OtpStatus.PENDING) {
          recentOtp.status = OtpStatus.EXPIRED;
          await otpRepository.save(recentOtp);
        }
      }

      // Generate and send new OTP
      return await this.generateAndSendOtp(phoneNumber, userId);
    } catch (error) {
      console.error('Error resending OTP:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to resend OTP'
      };
    }
  }
}
