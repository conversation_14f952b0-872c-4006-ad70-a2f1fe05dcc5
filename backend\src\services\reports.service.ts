import { AppDataSource } from '../data-source';
import { User, UserRole, UserStatus } from '../models/User';
import { Loan, LoanStatus } from '../models/Loan';
import { Transaction, TransactionType, TransactionStatus } from '../models/Transaction';
import { Document } from '../models/Document';

const userRepository = AppDataSource.getRepository(User);
const loanRepository = AppDataSource.getRepository(Loan);
const transactionRepository = AppDataSource.getRepository(Transaction);
const documentRepository = AppDataSource.getRepository(Document);

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface FinancialReports {
  cashFlow: any;
  profitLoss: any;
  outstandingLoans: any;
  repaymentAnalysis: any;
  transactionSummary: any;
}

export interface UserReports {
  userGrowth: any;
  userActivity: any;
}

export interface LoanReports {
  loanApplications: any;
  loanPerformance: any;
  loanTypes: any;
  loanAging: any;
}

export interface OperationalReports {
  systemPerformance: any;
  staffActivity: any;
  auditLog: any;
}

export class ReportsService {

  // Helper method to get date range based on period
  private getDateRange(period: string): DateRange {
    const now = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(now.getMonth() - 1); // Default to month
    }

    return { startDate, endDate: now };
  }

  // FINANCIAL REPORTS
  async getCashFlowReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    // Get all completed transactions in the period
    const transactions = await transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.status = :status', { status: TransactionStatus.COMPLETED })
      .andWhere('transaction.completedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const inflows = transactions
      .filter(t => t.type === TransactionType.DEPOSIT || t.type === TransactionType.LOAN_REPAYMENT)
      .reduce((sum, t) => sum + Number(t.amount), 0);

    const outflows = transactions
      .filter(t => t.type === TransactionType.WITHDRAWAL || t.type === TransactionType.LOAN_DISBURSEMENT)
      .reduce((sum, t) => sum + Number(t.amount), 0);

    const netCashFlow = inflows - outflows;

    return {
      period,
      inflows: Number(inflows.toFixed(2)),
      outflows: Number(outflows.toFixed(2)),
      netCashFlow: Number(netCashFlow.toFixed(2)),
      transactionCount: transactions.length,
      generatedAt: new Date()
    };
  }

  async getProfitLossReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    // Get loan repayments (revenue from interest)
    const repayments = await transactionRepository
      .createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.user', 'user')
      .where('transaction.type = :type', { type: TransactionType.LOAN_REPAYMENT })
      .andWhere('transaction.status = :status', { status: TransactionStatus.COMPLETED })
      .andWhere('transaction.completedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    // Get loans disbursed (cost)
    const disbursements = await transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.type = :type', { type: TransactionType.LOAN_DISBURSEMENT })
      .andWhere('transaction.status = :status', { status: TransactionStatus.COMPLETED })
      .andWhere('transaction.completedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const totalRepayments = repayments.reduce((sum, t) => sum + Number(t.amount), 0);
    const totalDisbursements = disbursements.reduce((sum, t) => sum + Number(t.amount), 0);

    // Estimate interest revenue (simplified calculation)
    const estimatedInterestRevenue = totalRepayments * 0.15; // Assuming average 15% interest
    const grossProfit = estimatedInterestRevenue;
    const operatingExpenses = totalDisbursements * 0.05; // Assuming 5% operating costs
    const netProfit = grossProfit - operatingExpenses;

    return {
      period,
      revenue: Number(estimatedInterestRevenue.toFixed(2)),
      costs: Number(operatingExpenses.toFixed(2)),
      grossProfit: Number(grossProfit.toFixed(2)),
      netProfit: Number(netProfit.toFixed(2)),
      profitMargin: totalRepayments > 0 ? Number(((netProfit / totalRepayments) * 100).toFixed(2)) : 0,
      generatedAt: new Date()
    };
  }

  async getOutstandingLoansReport(period: string): Promise<any> {
    // Get all active loans
    const activeLoans = await loanRepository
      .createQueryBuilder('loan')
      .leftJoinAndSelect('loan.user', 'user')
      .where('loan.status IN (:...statuses)', {
        statuses: [LoanStatus.APPROVED, LoanStatus.DISBURSED, LoanStatus.OVERDUE]
      })
      .getMany();

    const totalOutstanding = activeLoans.reduce((sum, loan) => {
      const principal = Number(loan.amount);
      const interestRate = Number(loan.interestRate);
      const interestAmount = (principal * interestRate) / 100;
      const totalAmount = principal + interestAmount;
      const amountPaid = Number(loan.amountPaid);
      return sum + (totalAmount - amountPaid);
    }, 0);

    const totalPrincipal = activeLoans.reduce((sum, loan) => sum + Number(loan.amount), 0);
    const totalPaid = activeLoans.reduce((sum, loan) => sum + Number(loan.amountPaid), 0);

    // Count by status
    const statusBreakdown = {
      approved: activeLoans.filter(l => l.status === LoanStatus.APPROVED).length,
      disbursed: activeLoans.filter(l => l.status === LoanStatus.DISBURSED).length,
      overdue: activeLoans.filter(l => l.status === LoanStatus.OVERDUE).length
    };

    return {
      period,
      totalOutstanding: Number(totalOutstanding.toFixed(2)),
      totalPrincipal: Number(totalPrincipal.toFixed(2)),
      totalPaid: Number(totalPaid.toFixed(2)),
      activeLoanCount: activeLoans.length,
      statusBreakdown,
      averageLoanAmount: activeLoans.length > 0 ? Number((totalPrincipal / activeLoans.length).toFixed(2)) : 0,
      generatedAt: new Date()
    };
  }

  async getRepaymentAnalysisReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    // Get all loans
    const allLoans = await loanRepository.find();

    // Calculate repayment metrics
    const totalLoans = allLoans.length;
    const paidLoans = allLoans.filter(l => l.status === LoanStatus.PAID).length;
    const overdueLoans = allLoans.filter(l => l.status === LoanStatus.OVERDUE).length;
    const activeLoans = allLoans.filter(l =>
      l.status === LoanStatus.DISBURSED || l.status === LoanStatus.APPROVED
    ).length;

    const repaymentRate = totalLoans > 0 ? Number(((paidLoans / totalLoans) * 100).toFixed(2)) : 0;
    const defaultRate = totalLoans > 0 ? Number(((overdueLoans / totalLoans) * 100).toFixed(2)) : 0;

    // Get recent repayments
    const recentRepayments = await transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.type = :type', { type: TransactionType.LOAN_REPAYMENT })
      .andWhere('transaction.status = :status', { status: TransactionStatus.COMPLETED })
      .andWhere('transaction.completedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const totalRepaymentsAmount = recentRepayments.reduce((sum, t) => sum + Number(t.amount), 0);

    return {
      period,
      totalLoans,
      paidLoans,
      activeLoans,
      overdueLoans,
      repaymentRate,
      defaultRate,
      recentRepaymentsCount: recentRepayments.length,
      recentRepaymentsAmount: Number(totalRepaymentsAmount.toFixed(2)),
      generatedAt: new Date()
    };
  }

  async getTransactionSummaryReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    const transactions = await transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const summary = {
      total: transactions.length,
      completed: transactions.filter(t => t.status === TransactionStatus.COMPLETED).length,
      pending: transactions.filter(t => t.status === TransactionStatus.PENDING).length,
      failed: transactions.filter(t => t.status === TransactionStatus.FAILED).length,
      byType: {
        deposits: transactions.filter(t => t.type === TransactionType.DEPOSIT).length,
        withdrawals: transactions.filter(t => t.type === TransactionType.WITHDRAWAL).length,
        disbursements: transactions.filter(t => t.type === TransactionType.LOAN_DISBURSEMENT).length,
        repayments: transactions.filter(t => t.type === TransactionType.LOAN_REPAYMENT).length
      },
      totalAmount: Number(transactions.reduce((sum, t) => sum + Number(t.amount), 0).toFixed(2))
    };

    return {
      period,
      ...summary,
      generatedAt: new Date()
    };
  }

  // USER REPORTS
  async getUserGrowthReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    const newUsers = await userRepository
      .createQueryBuilder('user')
      .where('user.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('user.role = :role', { role: UserRole.CUSTOMER })
      .getMany();

    const totalUsers = await userRepository.count({
      where: { role: UserRole.CUSTOMER }
    });

    // Group by day/week/month based on period
    const growthData = this.groupUsersByTimeInterval(newUsers, period);

    return {
      period,
      newUsersCount: newUsers.length,
      totalUsers,
      growthRate: totalUsers > 0 ? Number(((newUsers.length / totalUsers) * 100).toFixed(2)) : 0,
      growthData,
      generatedAt: new Date()
    };
  }

  async getUserActivityReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    const activeUsers = await userRepository
      .createQueryBuilder('user')
      .where('user.lastLoginAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('user.role = :role', { role: UserRole.CUSTOMER })
      .getMany();

    const totalUsers = await userRepository.count({
      where: { role: UserRole.CUSTOMER }
    });

    const verificationStats = {
      emailVerified: await userRepository.count({
        where: { role: UserRole.CUSTOMER, isEmailVerified: true }
      }),
      phoneVerified: await userRepository.count({
        where: { role: UserRole.CUSTOMER, isPhoneVerified: true }
      }),
      faceVerified: await userRepository.count({
        where: { role: UserRole.CUSTOMER, isFaceVerified: true }
      })
    };

    return {
      period,
      activeUsers: activeUsers.length,
      totalUsers,
      activityRate: totalUsers > 0 ? Number(((activeUsers.length / totalUsers) * 100).toFixed(2)) : 0,
      verificationStats,
      generatedAt: new Date()
    };
  }

  // Helper method to group users by time interval
  private groupUsersByTimeInterval(users: User[], period: string): any[] {
    // Implementation for grouping users by day/week/month
    // This is a simplified version - you can enhance it based on your needs
    const grouped: { [key: string]: number } = {};

    users.forEach(user => {
      const date = new Date(user.createdAt);
      let key: string;

      switch (period) {
        case 'week':
          key = date.toISOString().split('T')[0]; // Daily grouping for week
          break;
        case 'month':
          key = `${date.getFullYear()}-W${Math.ceil(date.getDate() / 7)}`; // Weekly grouping for month
          break;
        default:
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`; // Monthly grouping
      }

      grouped[key] = (grouped[key] || 0) + 1;
    });

    return Object.entries(grouped).map(([period, count]) => ({ period, count }));
  }

  // LOAN REPORTS
  async getLoanApplicationsReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    const applications = await loanRepository
      .createQueryBuilder('loan')
      .where('loan.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const statusBreakdown = {
      pending: applications.filter(l => l.status === LoanStatus.PENDING).length,
      approved: applications.filter(l => l.status === LoanStatus.APPROVED).length,
      rejected: applications.filter(l => l.status === LoanStatus.REJECTED).length,
      disbursed: applications.filter(l => l.status === LoanStatus.DISBURSED).length
    };

    const approvalRate = applications.length > 0 ?
      Number((((statusBreakdown.approved + statusBreakdown.disbursed) / applications.length) * 100).toFixed(2)) : 0;

    const totalApplicationAmount = applications.reduce((sum, loan) => sum + Number(loan.amount), 0);
    const averageApplicationAmount = applications.length > 0 ?
      Number((totalApplicationAmount / applications.length).toFixed(2)) : 0;

    return {
      period,
      totalApplications: applications.length,
      statusBreakdown,
      approvalRate,
      totalApplicationAmount: Number(totalApplicationAmount.toFixed(2)),
      averageApplicationAmount,
      generatedAt: new Date()
    };
  }

  async getLoanPerformanceReport(period: string): Promise<any> {
    const allLoans = await loanRepository.find();

    const performanceMetrics = {
      totalLoans: allLoans.length,
      paidLoans: allLoans.filter(l => l.status === LoanStatus.PAID).length,
      overdueLoans: allLoans.filter(l => l.status === LoanStatus.OVERDUE).length,
      activeLoans: allLoans.filter(l => l.status === LoanStatus.DISBURSED).length
    };

    const defaultRate = performanceMetrics.totalLoans > 0 ?
      Number(((performanceMetrics.overdueLoans / performanceMetrics.totalLoans) * 100).toFixed(2)) : 0;

    const completionRate = performanceMetrics.totalLoans > 0 ?
      Number(((performanceMetrics.paidLoans / performanceMetrics.totalLoans) * 100).toFixed(2)) : 0;

    return {
      period,
      ...performanceMetrics,
      defaultRate,
      completionRate,
      generatedAt: new Date()
    };
  }

  async getLoanTypesReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    const loans = await loanRepository
      .createQueryBuilder('loan')
      .where('loan.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    // Group by purpose
    const purposeBreakdown: { [key: string]: { count: number; totalAmount: number } } = {};

    loans.forEach(loan => {
      const purpose = loan.purpose || 'Other';
      if (!purposeBreakdown[purpose]) {
        purposeBreakdown[purpose] = { count: 0, totalAmount: 0 };
      }
      purposeBreakdown[purpose].count++;
      purposeBreakdown[purpose].totalAmount += Number(loan.amount);
    });

    // Group by amount ranges
    const amountRanges = {
      'Under E100': loans.filter(l => Number(l.amount) < 100).length,
      'E100-E300': loans.filter(l => Number(l.amount) >= 100 && Number(l.amount) < 300).length,
      'E300-E600': loans.filter(l => Number(l.amount) >= 300 && Number(l.amount) <= 600).length,
      'Over E600': loans.filter(l => Number(l.amount) > 600).length
    };

    return {
      period,
      totalLoans: loans.length,
      purposeBreakdown,
      amountRanges,
      generatedAt: new Date()
    };
  }

  async getLoanAgingReport(period: string): Promise<any> {
    const activeLoans = await loanRepository
      .createQueryBuilder('loan')
      .where('loan.status IN (:...statuses)', {
        statuses: [LoanStatus.DISBURSED, LoanStatus.OVERDUE]
      })
      .getMany();

    const now = new Date();
    const agingBuckets = {
      'Current (0-30 days)': 0,
      'Past Due (31-60 days)': 0,
      'Delinquent (61-90 days)': 0,
      'Default (90+ days)': 0
    };

    activeLoans.forEach(loan => {
      if (loan.dueDate) {
        const daysPastDue = Math.floor((now.getTime() - loan.dueDate.getTime()) / (1000 * 60 * 60 * 24));

        if (daysPastDue <= 0) {
          agingBuckets['Current (0-30 days)']++;
        } else if (daysPastDue <= 30) {
          agingBuckets['Current (0-30 days)']++;
        } else if (daysPastDue <= 60) {
          agingBuckets['Past Due (31-60 days)']++;
        } else if (daysPastDue <= 90) {
          agingBuckets['Delinquent (61-90 days)']++;
        } else {
          agingBuckets['Default (90+ days)']++;
        }
      }
    });

    return {
      period,
      totalActiveLoans: activeLoans.length,
      agingBuckets,
      generatedAt: new Date()
    };
  }

  // OPERATIONAL REPORTS
  async getSystemPerformanceReport(period: string): Promise<any> {
    // This would typically integrate with monitoring systems
    // For now, we'll provide basic metrics
    return {
      period,
      uptime: 99.9,
      averageResponseTime: 150, // ms
      totalRequests: 10000,
      errorRate: 0.1,
      databaseConnections: 5,
      activeUsers: 25,
      generatedAt: new Date()
    };
  }

  async getStaffActivityReport(period: string): Promise<any> {
    const { startDate, endDate } = this.getDateRange(period);

    const adminUsers = await userRepository
      .createQueryBuilder('user')
      .where('user.role IN (:...roles)', { roles: [UserRole.ADMIN, UserRole.STAFF] })
      .andWhere('user.lastLoginAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    // Get loan actions (approvals, rejections) by admins
    const loanActions = await loanRepository
      .createQueryBuilder('loan')
      .where('loan.approvedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .orWhere('loan.status = :rejected AND loan.updatedAt BETWEEN :startDate AND :endDate',
        { rejected: LoanStatus.REJECTED, startDate, endDate })
      .getMany();

    return {
      period,
      activeStaff: adminUsers.length,
      loanActionsCount: loanActions.length,
      averageActionsPerStaff: adminUsers.length > 0 ?
        Number((loanActions.length / adminUsers.length).toFixed(2)) : 0,
      generatedAt: new Date()
    };
  }

  async getAuditLogReport(period: string): Promise<any> {
    // This would typically integrate with an audit logging system
    // For now, we'll provide basic metrics based on available data
    const { startDate, endDate } = this.getDateRange(period);

    const recentLoans = await loanRepository
      .createQueryBuilder('loan')
      .where('loan.updatedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const recentUsers = await userRepository
      .createQueryBuilder('user')
      .where('user.updatedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    return {
      period,
      loanChanges: recentLoans.length,
      userChanges: recentUsers.length,
      totalChanges: recentLoans.length + recentUsers.length,
      securityEvents: 0, // Would be populated from security logs
      generatedAt: new Date()
    };
  }

  // MAIN REPORT AGGREGATION METHODS
  async getFinancialReports(period: string): Promise<FinancialReports> {
    const [cashFlow, profitLoss, outstandingLoans, repaymentAnalysis, transactionSummary] =
      await Promise.all([
        this.getCashFlowReport(period),
        this.getProfitLossReport(period),
        this.getOutstandingLoansReport(period),
        this.getRepaymentAnalysisReport(period),
        this.getTransactionSummaryReport(period)
      ]);

    return {
      cashFlow,
      profitLoss,
      outstandingLoans,
      repaymentAnalysis,
      transactionSummary
    };
  }

  async getUserReports(period: string): Promise<UserReports> {
    const [userGrowth, userActivity] = await Promise.all([
      this.getUserGrowthReport(period),
      this.getUserActivityReport(period)
    ]);

    return {
      userGrowth,
      userActivity
    };
  }

  async getLoanReports(period: string): Promise<LoanReports> {
    const [loanApplications, loanPerformance, loanTypes, loanAging] =
      await Promise.all([
        this.getLoanApplicationsReport(period),
        this.getLoanPerformanceReport(period),
        this.getLoanTypesReport(period),
        this.getLoanAgingReport(period)
      ]);

    return {
      loanApplications,
      loanPerformance,
      loanTypes,
      loanAging
    };
  }

  async getOperationalReports(period: string): Promise<OperationalReports> {
    const [systemPerformance, staffActivity, auditLog] = await Promise.all([
      this.getSystemPerformanceReport(period),
      this.getStaffActivityReport(period),
      this.getAuditLogReport(period)
    ]);

    return {
      systemPerformance,
      staffActivity,
      auditLog
    };
  }

  async getAllReports(period: string): Promise<{
    financial: FinancialReports;
    user: UserReports;
    loan: LoanReports;
    operational: OperationalReports;
  }> {
    const [financial, user, loan, operational] = await Promise.all([
      this.getFinancialReports(period),
      this.getUserReports(period),
      this.getLoanReports(period),
      this.getOperationalReports(period)
    ]);

    return {
      financial,
      user,
      loan,
      operational
    };
  }
}
