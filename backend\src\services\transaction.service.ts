import { AppDataSource } from '../data-source';
import { Transaction, TransactionType, TransactionStatus } from '../models/Transaction';
import { User } from '../models/User';

const transactionRepository = AppDataSource.getRepository(Transaction);
const userRepository = AppDataSource.getRepository(User);

export class TransactionService {
  // Get all transactions for a user
  async getUserTransactions(userId: string): Promise<Transaction[]> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Get transactions
    return transactionRepository.find({
      where: { user: { id: userId } },
      order: { createdAt: 'DESC' }
    });
  }

  // Get a specific transaction
  async getTransactionById(transactionId: string, userId?: string): Promise<Transaction> {
    // Find transaction
    const queryOptions: any = { where: { id: transactionId } };
    if (userId) {
      queryOptions.where.user = { id: userId };
    }

    const transaction = await transactionRepository.findOne(queryOptions);
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    return transaction;
  }

  // Create a deposit transaction
  async createDepositTransaction(
    userId: string,
    amount: number,
    reference?: string,
    description?: string
  ): Promise<Transaction> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Validate amount
    if (amount <= 0) {
      throw new Error('Amount must be greater than zero');
    }

    // Create transaction
    const transaction = new Transaction();
    transaction.user = user;
    transaction.amount = amount;
    transaction.type = TransactionType.DEPOSIT;
    transaction.status = TransactionStatus.PENDING;
    transaction.reference = reference || `DEP-${Date.now()}`;
    transaction.description = description || `Deposit of $${amount}`;

    // Save transaction
    return transactionRepository.save(transaction);
  }

  // Create a withdrawal transaction
  async createWithdrawalTransaction(
    userId: string,
    amount: number,
    reference?: string,
    description?: string
  ): Promise<Transaction> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Validate amount
    if (amount <= 0) {
      throw new Error('Amount must be greater than zero');
    }

    // Create transaction
    const transaction = new Transaction();
    transaction.user = user;
    transaction.amount = amount;
    transaction.type = TransactionType.WITHDRAWAL;
    transaction.status = TransactionStatus.PENDING;
    transaction.reference = reference || `WIT-${Date.now()}`;
    transaction.description = description || `Withdrawal of $${amount}`;

    // Save transaction
    return transactionRepository.save(transaction);
  }

  // Update transaction status
  async updateTransactionStatus(
    transactionId: string,
    status: TransactionStatus,
    metadata?: any,
    failureReason?: string
  ): Promise<Transaction> {
    // Find transaction
    const transaction = await transactionRepository.findOne({
      where: { id: transactionId }
    });
    if (!transaction) {
      throw new Error('Transaction not found');
    }

    // Update status
    transaction.status = status;

    // Update metadata if provided
    if (metadata) {
      transaction.metadata = JSON.stringify(metadata);
    }

    // Update timestamps based on status
    if (status === TransactionStatus.COMPLETED) {
      transaction.completedAt = new Date();
    } else if (status === TransactionStatus.FAILED) {
      transaction.failedAt = new Date();
      transaction.failureReason = failureReason;
    }

    // Save transaction
    return transactionRepository.save(transaction);
  }

  // Get all transactions (admin only)
  async getAllTransactions(options: {
    page: number;
    limit: number;
    search?: string;
    type?: string;
    status?: string;
  }): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    pages: number;
  }> {
    const { page, limit, search, type, status } = options;

    // Build query conditions
    const queryBuilder = transactionRepository
      .createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.user', 'user')
      .orderBy('transaction.createdAt', 'DESC');

    // Apply search filter
    if (search) {
      queryBuilder.andWhere(
        '(transaction.id ILIKE :search OR transaction.reference ILIKE :search OR user.fullName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply type filter
    if (type && type !== 'all') {
      if (type === 'repayment' || type === 'repayments') {
        queryBuilder.andWhere('transaction.type = :type', { type: 'loan_repayment' });
      } else if (type === 'disbursement' || type === 'disbursements') {
        queryBuilder.andWhere('transaction.type = :type', { type: 'loan_disbursement' });
      } else {
        queryBuilder.andWhere('transaction.type = :type', { type });
      }
    }

    // Apply status filter
    if (status && status !== 'all') {
      queryBuilder.andWhere('transaction.status = :status', { status });
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const transactions = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    // Transform transactions for frontend
    const transformedTransactions = transactions.map(transaction => ({
      id: transaction.id,
      userId: transaction.user.id,
      userName: transaction.user.fullName,
      userPhoneNumber: transaction.user.phoneNumber,
      loanId: this.extractLoanIdFromReference(transaction.reference),
      amount: parseFloat(transaction.amount.toString()),
      type: this.mapTransactionType(transaction.type),
      method: this.determinePaymentMethod(transaction),
      status: transaction.status,
      date: transaction.createdAt.toISOString(),
      reference: transaction.reference,
      description: transaction.description,
      completedAt: transaction.completedAt?.toISOString(),
      failedAt: transaction.failedAt?.toISOString(),
      failureReason: transaction.failureReason,
      metadata: transaction.metadata ? JSON.parse(transaction.metadata) : null
    }));

    return {
      transactions: transformedTransactions,
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    };
  }

  // Helper method to extract loan ID from reference
  private extractLoanIdFromReference(reference?: string): string | null {
    if (!reference) return null;

    // Extract loan ID from references like "DISBURSE-LOAN-123" or "REPAY-LOAN-123"
    const match = reference.match(/(?:DISBURSE-|REPAY-)(LOAN-[A-Z0-9]+)/);
    return match ? match[1] : null;
  }

  // Helper method to map transaction type to frontend format
  private mapTransactionType(type: string): 'repayment' | 'disbursement' | 'deposit' | 'withdrawal' {
    switch (type) {
      case 'loan_repayment':
        return 'repayment';
      case 'loan_disbursement':
        return 'disbursement';
      case 'deposit':
        return 'deposit';
      case 'withdrawal':
        return 'withdrawal';
      default:
        return 'disbursement';
    }
  }

  // Helper method to determine payment method
  private determinePaymentMethod(transaction: Transaction): 'mobile_money' | 'bank_transfer' | 'cash' {
    // Check metadata for payment method
    if (transaction.metadata) {
      try {
        const metadata = JSON.parse(transaction.metadata);
        if (metadata.paymentMethod) {
          return metadata.paymentMethod;
        }
        if (metadata.provider === 'mobile_money') {
          return 'mobile_money';
        }
      } catch (e) {
        // Ignore JSON parse errors
      }
    }

    // Check reference for payment method indicators
    if (transaction.reference?.includes('MOBILE')) {
      return 'mobile_money';
    }
    if (transaction.reference?.includes('BANK')) {
      return 'bank_transfer';
    }

    // Default based on transaction type
    if (transaction.type === 'loan_disbursement') {
      return 'mobile_money'; // Most disbursements are via mobile money
    }

    return 'cash'; // Default for repayments
  }

  // Get transaction statistics for a user
  async getUserTransactionStatistics(userId: string): Promise<any> {
    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new Error('User not found');
    }

    // Get all transactions for the user
    const transactions = await transactionRepository.find({
      where: { user: { id: userId } }
    });

    // Calculate statistics
    const totalDeposits = transactions
      .filter(t => t.type === TransactionType.DEPOSIT && t.status === TransactionStatus.COMPLETED)
      .reduce((total, t) => total + t.amount, 0);

    const totalWithdrawals = transactions
      .filter(t => t.type === TransactionType.WITHDRAWAL && t.status === TransactionStatus.COMPLETED)
      .reduce((total, t) => total + t.amount, 0);

    const totalLoanDisbursements = transactions
      .filter(t => t.type === TransactionType.LOAN_DISBURSEMENT && t.status === TransactionStatus.COMPLETED)
      .reduce((total, t) => total + t.amount, 0);

    const totalLoanRepayments = transactions
      .filter(t => t.type === TransactionType.LOAN_REPAYMENT && t.status === TransactionStatus.COMPLETED)
      .reduce((total, t) => total + t.amount, 0);

    const pendingTransactions = transactions
      .filter(t => t.status === TransactionStatus.PENDING)
      .length;

    const accountBalance = totalDeposits + totalLoanDisbursements - totalWithdrawals - totalLoanRepayments;

    return {
      totalDeposits,
      totalWithdrawals,
      totalLoanDisbursements,
      totalLoanRepayments,
      pendingTransactions,
      accountBalance,
      transactionCount: transactions.length
    };
  }
}