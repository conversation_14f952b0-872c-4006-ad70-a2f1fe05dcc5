import twilio from 'twilio';
import { AppDataSource } from '../data-source';
import { User } from '../models/User';

// Twilio configuration
const accountSid = process.env.TWILIO_ACCOUNT_SID || '';
const authToken = process.env.TWILIO_AUTH_TOKEN || '';
const verifyServiceSid = process.env.TWILIO_VERIFY_SERVICE_SID || '';

// Initialize Twilio client
const twilioClient = twilio(accountSid, authToken);

// Repository for user data
const userRepository = AppDataSource.getRepository(User);

export class TwilioVerifyService {
  /**
   * Send verification code via Twilio Verify
   * @param phoneNumber Phone number to send verification code to
   * @returns Success status
   */
  async sendVerificationCode(
    phoneNumber: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Validate phone number format
      if (!phoneNumber || !/^\+?[1-9]\d{1,14}$/.test(phoneNumber)) {
        return {
          success: false,
          message: 'Invalid phone number format. Please use international format (e.g., +**********).'
        };
      }

      // Format the phone number if needed
      let formattedPhoneNumber = phoneNumber;
      if (!phoneNumber.startsWith('+')) {
        // Assuming Eswatini country code (+268)
        formattedPhoneNumber = `+268${phoneNumber.replace(/^0+/, '')}`;
      }

      // Check if Verify service SID is configured
      if (!verifyServiceSid) {
        console.warn('Twilio Verify Service SID not configured. Using development mode.');

        // For development, log a message
        console.log(`⚠️ DEVELOPMENT MODE: Simulating verification code delivery`);
        console.log(`A verification code would be sent to ${formattedPhoneNumber}`);

        return {
          success: true,
          message: 'Verification code sent successfully (development mode)'
        };
      }

      // Send verification code via Twilio Verify API
      try {
        console.log(`Sending verification code to ${formattedPhoneNumber} via SMS`);

        const verification = await twilioClient.verify.v2
          .services(verifyServiceSid)
          .verifications.create({
            to: formattedPhoneNumber,
            channel: 'sms'
          });

        console.log(`Verification sent with status: ${verification.status}`);

        return {
          success: true,
          message: 'Verification code sent successfully'
        };
      } catch (twilioError) {
        console.error('Twilio Verify API error:', twilioError);

        // For development, simulate success if Twilio fails
        if (process.env.NODE_ENV !== 'production') {
          console.log(`⚠️ DEVELOPMENT MODE: Simulating verification code delivery after Twilio error`);

          return {
            success: true,
            message: 'Verification code sent successfully (development mode)'
          };
        }

        throw twilioError;
      }
    } catch (error) {
      console.error('Error sending verification code:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send verification code'
      };
    }
  }

  /**
   * Verify code submitted by user
   * @param phoneNumber Phone number to verify
   * @param code Verification code submitted by user
   * @returns Verification result
   */
  async verifyCode(
    phoneNumber: string,
    code: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Validate phone number format
      if (!phoneNumber || !/^\+?[1-9]\d{1,14}$/.test(phoneNumber)) {
        return {
          success: false,
          message: 'Invalid phone number format. Please use international format (e.g., +**********).'
        };
      }

      // Format the phone number if needed
      let formattedPhoneNumber = phoneNumber;
      if (!phoneNumber.startsWith('+')) {
        // Assuming Eswatini country code (+268)
        formattedPhoneNumber = `+268${phoneNumber.replace(/^0+/, '')}`;
      }

      // Check if Verify service SID is configured
      if (!verifyServiceSid) {
        console.warn('Twilio Verify Service SID not configured. Using development mode.');

        // For development, accept any code with 6 digits
        if (code.length === 6 && /^\d+$/.test(code)) {
          console.log(`⚠️ DEVELOPMENT MODE: Simulating successful verification`);

          // Update user's phone verification status
          await this.updateUserPhoneVerificationStatus(formattedPhoneNumber);

          return {
            success: true,
            message: 'Verification successful (development mode)'
          };
        } else {
          return {
            success: false,
            message: 'Invalid verification code. Please enter a 6-digit code.'
          };
        }
      }

      // Verify code via Twilio Verify API
      try {
        console.log(`Verifying code for ${formattedPhoneNumber}`);

        const verificationCheck = await twilioClient.verify.v2
          .services(verifyServiceSid)
          .verificationChecks.create({
            to: formattedPhoneNumber,
            code: code
          });

        console.log(`Verification check status: ${verificationCheck.status}`);

        if (verificationCheck.status === 'approved') {
          // Update user's phone verification status
          await this.updateUserPhoneVerificationStatus(formattedPhoneNumber);

          return {
            success: true,
            message: 'Verification successful'
          };
        } else {
          return {
            success: false,
            message: 'Invalid verification code. Please try again.'
          };
        }
      } catch (twilioError) {
        console.error('Twilio Verify API error:', twilioError);

        // For development, simulate success if Twilio fails
        if (process.env.NODE_ENV !== 'production' && code.length === 6 && /^\d+$/.test(code)) {
          console.log(`⚠️ DEVELOPMENT MODE: Simulating successful verification after Twilio error`);

          // Update user's phone verification status
          await this.updateUserPhoneVerificationStatus(formattedPhoneNumber);

          return {
            success: true,
            message: 'Verification successful (development mode)'
          };
        }

        return {
          success: false,
          message: 'Failed to verify code. Please try again.'
        };
      }
    } catch (error) {
      console.error('Error verifying code:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to verify code'
      };
    }
  }

  /**
   * Update user's phone verification status
   * @param phoneNumber Phone number to update
   */
  private async updateUserPhoneVerificationStatus(phoneNumber: string): Promise<void> {
    try {
      // Find user by phone number
      const user = await userRepository.findOne({ where: { phoneNumber } });

      if (user) {
        // Update user's phone verification status
        user.isPhoneVerified = true;
        await userRepository.save(user);
        console.log(`Updated phone verification status for user ${user.id}`);
      } else {
        console.log(`No user found with phone number ${phoneNumber}`);
      }
    } catch (error) {
      console.error('Error updating user phone verification status:', error);
    }
  }
}
