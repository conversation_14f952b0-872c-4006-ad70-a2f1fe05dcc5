import { AppDataSource } from '../data-source';
import { User, UserRole, UserStatus } from '../models/User';
import { Loan } from '../models/Loan';
import { hashPassword } from '../utils/auth';
import { Like, ILike } from 'typeorm';

const userRepository = AppDataSource.getRepository(User);
const loanRepository = AppDataSource.getRepository(Loan);
const DEFAULT_PASSWORD = 'password123';

export class UserService {
  // Get all users with pagination and filtering
  async getAllUsers(
    page: number = 1,
    limit: number = 10,
    search: string = '',
    status?: UserStatus,
    role?: UserRole
  ): Promise<{ users: any[]; total: number }> {
    const skip = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any = {};

    if (search) {
      whereConditions.fullName = ILike(`%${search}%`);
    }

    if (status) {
      whereConditions.status = status;
    }

    if (role) {
      whereConditions.role = role;
    }

    // Get total count
    const total = await userRepository.count({
      where: whereConditions
    });

    // Get users with pagination
    const users = await userRepository.find({
      where: whereConditions,
      skip,
      take: limit,
      order: {
        createdAt: 'DESC'
      }
    });

    // Get loan counts for each user
    const usersWithLoanCounts = await Promise.all(
      users.map(async (user) => {
        const loanCount = await loanRepository.count({
          where: { user: { id: user.id } }
        });

        return {
          id: user.id,
          studentId: user.studentId,
          name: user.fullName,
          email: user.email,
          phone: user.phoneNumber,
          role: user.role,
          status: user.status,
          isEmailVerified: user.isEmailVerified,
          isPhoneVerified: user.isPhoneVerified,
          isFaceVerified: user.isFaceVerified,
          passwordChanged: user.passwordChanged,
          profileImage: user.profileImage,
          faceImage: user.faceImage,
          loans: loanCount,
          joinDate: user.createdAt,
          lastLogin: user.lastLoginAt
        };
      })
    );

    return { users: usersWithLoanCounts, total };
  }

  // Get user by ID
  async getUserById(userId: string): Promise<any> {
    const user = await userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Get loan count
    const loanCount = await loanRepository.count({
      where: { user: { id: user.id } }
    });

    return {
      id: user.id,
      studentId: user.studentId,
      name: user.fullName,
      email: user.email,
      phone: user.phoneNumber,
      role: user.role,
      status: user.status,
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      isFaceVerified: user.isFaceVerified,
      passwordChanged: user.passwordChanged,
      profileImage: user.profileImage,
      faceImage: user.faceImage,
      loans: loanCount,
      joinDate: user.createdAt,
      lastLogin: user.lastLoginAt,
      updatedAt: user.updatedAt
    };
  }

  // Create new user
  async createUser(userData: any): Promise<any> {
    // Check if user already exists
    const existingUserByEmail = await userRepository.findOne({
      where: { email: userData.email }
    });

    if (existingUserByEmail) {
      throw new Error('User with this email already exists');
    }

    if (userData.phoneNumber) {
      const existingUserByPhone = await userRepository.findOne({
        where: { phoneNumber: userData.phoneNumber }
      });

      if (existingUserByPhone) {
        throw new Error('User with this phone number already exists');
      }
    }

    if (userData.studentId) {
      const existingUserByStudentId = await userRepository.findOne({
        where: { studentId: userData.studentId }
      });

      if (existingUserByStudentId) {
        throw new Error('User with this student ID already exists');
      }
    }

    // Create new user
    const user = new User();
    user.fullName = userData.name;
    user.email = userData.email;
    user.phoneNumber = userData.phone;
    user.studentId = userData.studentId;
    user.role = userData.role || UserRole.CUSTOMER;
    user.status = userData.status || UserStatus.ACTIVE;

    // Set default password
    user.password = await hashPassword(DEFAULT_PASSWORD);
    user.passwordChanged = false;

    // Optional fields
    if (userData.faceImage) {
      user.faceImage = userData.faceImage;
    }

    if (userData.profileImage) {
      user.profileImage = userData.profileImage;
    }

    // Save user
    const savedUser = await userRepository.save(user);

    return {
      id: savedUser.id,
      studentId: savedUser.studentId,
      name: savedUser.fullName,
      email: savedUser.email,
      phone: savedUser.phoneNumber,
      role: savedUser.role,
      status: savedUser.status,
      isEmailVerified: savedUser.isEmailVerified,
      isPhoneVerified: savedUser.isPhoneVerified,
      isFaceVerified: savedUser.isFaceVerified,
      passwordChanged: savedUser.passwordChanged,
      profileImage: savedUser.profileImage,
      faceImage: savedUser.faceImage,
      loans: 0,
      joinDate: savedUser.createdAt,
      lastLogin: savedUser.lastLoginAt
    };
  }

  // Update user
  async updateUser(userId: string, userData: any): Promise<any> {
    const user = await userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check email uniqueness if changing email
    if (userData.email && userData.email !== user.email) {
      const existingUserByEmail = await userRepository.findOne({
        where: { email: userData.email }
      });

      if (existingUserByEmail) {
        throw new Error('User with this email already exists');
      }

      user.email = userData.email;
    }

    // Check phone uniqueness if changing phone
    if (userData.phone && userData.phone !== user.phoneNumber) {
      const existingUserByPhone = await userRepository.findOne({
        where: { phoneNumber: userData.phone }
      });

      if (existingUserByPhone) {
        throw new Error('User with this phone number already exists');
      }

      user.phoneNumber = userData.phone;
    }

    // Check studentId uniqueness if changing studentId
    if (userData.studentId && userData.studentId !== user.studentId) {
      const existingUserByStudentId = await userRepository.findOne({
        where: { studentId: userData.studentId }
      });

      if (existingUserByStudentId) {
        throw new Error('User with this student ID already exists');
      }

      user.studentId = userData.studentId;
    }

    // Update fields
    if (userData.name) {
      user.fullName = userData.name;
    }

    if (userData.role) {
      user.role = userData.role;
    }

    if (userData.status) {
      user.status = userData.status;
    }

    if (userData.faceImage) {
      user.faceImage = userData.faceImage;
    }

    if (userData.profileImage) {
      user.profileImage = userData.profileImage;
    }

    // Save updated user
    const updatedUser = await userRepository.save(user);

    // Get loan count
    const loanCount = await loanRepository.count({
      where: { user: { id: updatedUser.id } }
    });

    return {
      id: updatedUser.id,
      studentId: updatedUser.studentId,
      name: updatedUser.fullName,
      email: updatedUser.email,
      phone: updatedUser.phoneNumber,
      role: updatedUser.role,
      status: updatedUser.status,
      isEmailVerified: updatedUser.isEmailVerified,
      isPhoneVerified: updatedUser.isPhoneVerified,
      isFaceVerified: updatedUser.isFaceVerified,
      passwordChanged: updatedUser.passwordChanged,
      profileImage: updatedUser.profileImage,
      faceImage: updatedUser.faceImage,
      loans: loanCount,
      joinDate: updatedUser.createdAt,
      lastLogin: updatedUser.lastLoginAt
    };
  }

  // Delete user
  async deleteUser(userId: string): Promise<void> {
    const user = await userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check if user has loans
    const loanCount = await loanRepository.count({
      where: { user: { id: userId } }
    });

    if (loanCount > 0) {
      throw new Error('Cannot delete user with active loans');
    }

    await userRepository.remove(user);
  }

  // Update user status
  async updateUserStatus(userId: string, status: UserStatus): Promise<any> {
    const user = await userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    user.status = status;
    const updatedUser = await userRepository.save(user);

    // Get loan count
    const loanCount = await loanRepository.count({
      where: { user: { id: updatedUser.id } }
    });

    return {
      id: updatedUser.id,
      studentId: updatedUser.studentId,
      name: updatedUser.fullName,
      email: updatedUser.email,
      phone: updatedUser.phoneNumber,
      role: updatedUser.role,
      status: updatedUser.status,
      isEmailVerified: updatedUser.isEmailVerified,
      isPhoneVerified: updatedUser.isPhoneVerified,
      isFaceVerified: updatedUser.isFaceVerified,
      passwordChanged: updatedUser.passwordChanged,
      profileImage: updatedUser.profileImage,
      faceImage: updatedUser.faceImage,
      loans: loanCount,
      joinDate: updatedUser.createdAt,
      lastLogin: updatedUser.lastLoginAt
    };
  }

  // Reset user password
  async resetUserPassword(userId: string): Promise<string> {
    const user = await userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Reset to default password
    user.password = await hashPassword(DEFAULT_PASSWORD);
    user.passwordChanged = false;

    await userRepository.save(user);

    return DEFAULT_PASSWORD;
  }

  // Update user profile (for current user)
  async updateProfile(userId: string, profileData: any): Promise<any> {
    const user = await userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check email uniqueness if changing email
    if (profileData.email && profileData.email !== user.email) {
      const existingUserByEmail = await userRepository.findOne({
        where: { email: profileData.email }
      });

      if (existingUserByEmail) {
        throw new Error('User with this email already exists');
      }

      user.email = profileData.email;
    }

    // Check phone uniqueness if changing phone
    if (profileData.phoneNumber && profileData.phoneNumber !== user.phoneNumber) {
      const existingUserByPhone = await userRepository.findOne({
        where: { phoneNumber: profileData.phoneNumber }
      });

      if (existingUserByPhone) {
        throw new Error('User with this phone number already exists');
      }

      user.phoneNumber = profileData.phoneNumber;
    }

    // Update name if provided
    if (profileData.name) {
      user.fullName = profileData.name;
    }

    // Save updated user
    const updatedUser = await userRepository.save(user);

    return {
      id: updatedUser.id,
      name: updatedUser.fullName,
      email: updatedUser.email,
      phoneNumber: updatedUser.phoneNumber,
      role: updatedUser.role,
      status: updatedUser.status,
      isEmailVerified: updatedUser.isEmailVerified,
      isPhoneVerified: updatedUser.isPhoneVerified,
      isFaceVerified: updatedUser.isFaceVerified,
      profileImage: updatedUser.profileImage,
      faceImage: updatedUser.faceImage
    };
  }

  // Upload profile image
  async uploadProfileImage(userId: string, imageData: string): Promise<any> {
    try {
      console.log('Uploading profile image for user:', userId);
      console.log('Image data length:', imageData ? imageData.length : 0);

      const user = await userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Store the base64 image data directly
      user.profileImage = imageData;

      // Save the user with the new profile image
      const updatedUser = await userRepository.save(user);
      console.log('Profile image updated successfully for user:', userId);

      return {
        id: updatedUser.id,
        name: updatedUser.fullName,
        profileImage: updatedUser.profileImage
      };
    } catch (error) {
      console.error('Error in uploadProfileImage service:', error);
      throw error;
    }
  }

  // Get user statistics
  async getUserStatistics(): Promise<any> {
    const totalUsers = await userRepository.count();
    const activeUsers = await userRepository.count({
      where: { status: UserStatus.ACTIVE }
    });
    const pendingUsers = await userRepository.count({
      where: { status: UserStatus.PENDING }
    });
    const inactiveUsers = await userRepository.count({
      where: { status: UserStatus.INACTIVE }
    });
    const suspendedUsers = await userRepository.count({
      where: { status: UserStatus.SUSPENDED }
    });

    const adminUsers = await userRepository.count({
      where: { role: UserRole.ADMIN }
    });
    const staffUsers = await userRepository.count({
      where: { role: UserRole.STAFF }
    });
    const customerUsers = await userRepository.count({
      where: { role: UserRole.CUSTOMER }
    });

    const verifiedEmailUsers = await userRepository.count({
      where: { isEmailVerified: true }
    });
    const verifiedPhoneUsers = await userRepository.count({
      where: { isPhoneVerified: true }
    });
    const verifiedFaceUsers = await userRepository.count({
      where: { isFaceVerified: true }
    });

    // Get new users in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newUsers = await userRepository.count({
      where: {
        createdAt: thirtyDaysAgo
      }
    });

    return {
      totalUsers,
      activeUsers,
      pendingUsers,
      inactiveUsers,
      suspendedUsers,
      adminUsers,
      staffUsers,
      customerUsers,
      verifiedEmailUsers,
      verifiedPhoneUsers,
      verifiedFaceUsers,
      newUsers
    };
  }
}
