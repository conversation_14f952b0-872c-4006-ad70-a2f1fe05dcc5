import { LoanService } from '../services/loan.service';

export class LoanTasks {
  private loanService: LoanService;

  constructor() {
    this.loanService = new LoanService();
  }

  // Check for overdue loans and create notifications
  async checkOverdueLoans(): Promise<void> {
    try {
      console.log('Running scheduled task: checkOverdueLoans');
      const notificationCount = await this.loanService.checkOverdueLoans();
      console.log(`Created ${notificationCount} overdue loan notifications`);
    } catch (error) {
      console.error('Error in checkOverdueLoans task:', error);
    }
  }

  // Apply late payment penalties and create notifications
  async applyLatePaymentPenalties(): Promise<void> {
    try {
      console.log('Running scheduled task: applyLatePaymentPenalties');
      const penaltyCount = await this.loanService.applyLatePaymentPenalties();
      console.log(`Applied late payment penalties to ${penaltyCount} loans`);
    } catch (error) {
      console.error('Error in applyLatePaymentPenalties task:', error);
    }
  }
}
