import { LoanTasks } from './loan-tasks';
import { NotificationService } from '../services/notification.service';

export class Scheduler {
  private loanTasks: LoanTasks;
  private notificationService: NotificationService;
  private intervals: NodeJS.Timeout[] = [];

  constructor() {
    this.loanTasks = new LoanTasks();
    this.notificationService = new NotificationService();
  }

  // Start all scheduled tasks
  start(): void {
    console.log('Starting scheduled tasks...');
    
    // Check for overdue loans every day at midnight
    this.scheduleDaily(0, 0, async () => {
      await this.loanTasks.checkOverdueLoans();
    });
    
    // Apply late payment penalties every day at 1 AM
    this.scheduleDaily(1, 0, async () => {
      await this.loanTasks.applyLatePaymentPenalties();
    });
    
    // Send scheduled notifications every hour
    this.scheduleHourly(async () => {
      try {
        const count = await this.notificationService.sendScheduledNotifications();
        if (count > 0) {
          console.log(`Sent ${count} scheduled notifications`);
        }
      } catch (error) {
        console.error('Error sending scheduled notifications:', error);
      }
    });
    
    console.log('Scheduled tasks started');
  }

  // Stop all scheduled tasks
  stop(): void {
    console.log('Stopping scheduled tasks...');
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
    console.log('Scheduled tasks stopped');
  }

  // Schedule a task to run at a specific time every day
  private scheduleDaily(hour: number, minute: number, task: () => Promise<void>): void {
    const interval = setInterval(() => {
      const now = new Date();
      if (now.getHours() === hour && now.getMinutes() === minute) {
        task().catch(error => console.error('Error running scheduled task:', error));
      }
    }, 60 * 1000); // Check every minute
    
    this.intervals.push(interval);
  }

  // Schedule a task to run every hour
  private scheduleHourly(task: () => Promise<void>): void {
    const interval = setInterval(() => {
      task().catch(error => console.error('Error running hourly task:', error));
    }, 60 * 60 * 1000); // Run every hour
    
    this.intervals.push(interval);
    
    // Also run immediately on startup
    task().catch(error => console.error('Error running hourly task on startup:', error));
  }
}
