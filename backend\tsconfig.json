{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}