"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { LayoutDashboard, Users, CreditCard, Settings, Bell, LogOut, Wallet, ShieldAlert, FileText, BarChart3 } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { motion } from "framer-motion"

export function AdminAnimatedSidebar() {
  const pathname = usePathname()
  const [open, setOpen] = useState(false)
  const [adminUser, setAdminUser] = useState<any>(null)

  // Load admin user data from localStorage
  useEffect(() => {
    try {
      const userJson = localStorage.getItem("adminUser")
      if (userJson) {
        const userData = JSON.parse(userJson)
        setAdminUser(userData)
      }
    } catch (error) {
      console.error("Error loading admin user data in sidebar:", error)
    }
  }, [])

  useEffect(() => {
    const handleToggleSidebar = (e: CustomEvent) => {
      if (window.innerWidth < 768) {
        // Only apply on mobile
        setOpen(e.detail.isOpen)
      }
    }

    // Add event listener for the custom event
    document.addEventListener("toggle-sidebar", handleToggleSidebar as EventListener)

    // Clean up
    return () => {
      document.removeEventListener("toggle-sidebar", handleToggleSidebar as EventListener)
    }
  }, [])

  const links = [
    {
      label: "Dashboard",
      href: "/admin/dashboard",
      icon: <LayoutDashboard className="h-5 w-5 text-white" />,
    },
    {
      label: "Users",
      href: "/admin/users",
      icon: <Users className="h-5 w-5 text-white" />,
    },
    {
      label: "Loans",
      href: "/admin/loans",
      icon: <CreditCard className="h-5 w-5 text-white" />,
    },
    {
      label: "Payments",
      href: "/admin/payments",
      icon: <Wallet className="h-5 w-5 text-white" />,
    },
    
    {
      label: "Reports",
      href: "/admin/reports",
      icon: <BarChart3 className="h-5 w-5 text-white" />,
    },
    {
      label: "Documents",
      href: "/admin/documents",
      icon: <FileText className="h-5 w-5 text-white" />,
    },
    {
      label: "Notifications",
      href: "/admin/notifications",
      icon: <Bell className="h-5 w-5 text-white" />,
    },
    {
      label: "Settings",
      href: "/admin/settings",
      icon: <Settings className="h-5 w-5 text-white" />,
    },
    {
      label: "Logout",
      href: "/admin/login",
      icon: <LogOut className="h-5 w-5 text-white" />,
    },
  ]

  return (
    <div
      className={cn(
        "h-screen bg-blue-900 dark:bg-blue-950 text-white transition-all duration-300 ease-in-out",
        "md:relative absolute z-50", // Make it absolute on mobile
        !open && "md:translate-x-0 -translate-x-full", // Hide off-screen when closed on mobile
      )}
      onMouseEnter={() => window.innerWidth >= 768 && setOpen(true)}
      onMouseLeave={() => window.innerWidth >= 768 && setOpen(false)}
    >
      <div className="flex flex-col h-full p-4">
        <div className="flex items-center mb-8 transition-all duration-300 ease-in-out overflow-hidden">
          {open ? <Logo /> : <LogoIcon />}
        </div>

        <div className="flex-1 overflow-y-auto">
          <nav className="space-y-2">
            {links.map((link, idx) => (
              <Link
                key={idx}
                href={link.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-all duration-200 hover:bg-blue-800",
                  pathname === link.href ? "bg-blue-800" : "",
                )}
              >
                {link.icon}
                {open && (
                  <motion.span
                    initial={{ opacity: 0, width: 0 }}
                    animate={{ opacity: 1, width: "auto" }}
                    exit={{ opacity: 0, width: 0 }}
                    transition={{ duration: 0.3 }}
                    className="text-white whitespace-nowrap overflow-hidden"
                  >
                    {link.label}
                  </motion.span>
                )}
              </Link>
            ))}
          </nav>
        </div>

        <div className="mt-auto pt-4 border-t border-blue-800">
          <Link
            href="/admin/profile"
            className="flex items-center gap-3 px-3 py-2 rounded-md hover:bg-blue-800 transition-all duration-200"
          >
            <Avatar className="h-7 w-7 border border-white">
              {adminUser?.faceImage ? (
                <AvatarImage
                  src={adminUser.faceImage}
                  alt={adminUser.name || "Admin"}
                  onError={(e) => {
                    console.error('Error loading sidebar profile image');
                    e.currentTarget.src = '/placeholder.svg?height=28&width=28';
                  }}
                />
              ) : (
                <AvatarImage src="/placeholder.svg?height=28&width=28" alt="Admin" />
              )}
              <AvatarFallback>{adminUser?.name ? adminUser.name.charAt(0).toUpperCase() : "A"}</AvatarFallback>
            </Avatar>
            {open && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.3 }}
                className="text-white overflow-hidden"
              >
                {adminUser?.name || "Admin User"}
              </motion.span>
            )}
          </Link>
        </div>
      </div>
    </div>
  )
}

export const Logo = () => {
  return (
    <Link
      href="/admin/dashboard"
      className="font-normal flex space-x-2 items-center text-sm text-white py-1 relative z-20 transition-all duration-300"
    >
      <div className="bg-blue-600 text-white p-1.5 rounded">
        <ShieldAlert className="h-5 w-5" />
      </div>
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="font-medium text-white whitespace-pre"
      >
        Umlamleli Admin
      </motion.span>
    </Link>
  )
}

export const LogoIcon = () => {
  return (
    <Link
      href="/admin/dashboard"
      className="font-normal flex space-x-2 items-center text-sm text-white py-1 relative z-20 transition-all duration-300"
    >
      <div className="bg-blue-600 text-white p-1.5 rounded">
        <ShieldAlert className="h-5 w-5" />
      </div>
    </Link>
  )
}

