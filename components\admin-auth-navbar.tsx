import Link from "next/link"
import { ShieldAlert } from "lucide-react"

export function AdminAuthNavbar() {
  return (
    <header className="w-full bg-white/90 border-b">
      <div className="container mx-auto px-4 py-4">
        <Link href="/admin/login" className="flex items-center gap-2">
          <div className="bg-blue-600 text-white p-1.5 rounded">
            <ShieldAlert className="h-6 w-6" />
          </div>
          <span className="text-xl font-bold text-blue-900">
            Umlamleli <span className="text-red-600">Admin</span>
          </span>
        </Link>
      </div>
    </header>
  )
}

