"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu, Search, ShieldAlert } from "lucide-react"
import { adminApi } from "@/lib/admin-api"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Link from "next/link"
import { AdminNotificationBell } from "./admin-notification-bell"

export function AdminHeader() {
  const router = useRouter()
  // Change the existing mobile menu button to toggle the sidebar instead
  // First, let's update the state variable name to be more descriptive
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [adminUser, setAdminUser] = useState<any>(null)

  // Load admin user data from localStorage
  useEffect(() => {
    try {
      const userJson = localStorage.getItem("adminUser")
      if (userJson) {
        const userData = JSON.parse(userJson)
        setAdminUser(userData)
      }
    } catch (error) {
      console.error("Error loading admin user data:", error)
    }
  }, [])

  const handleLogout = () => {
    // Use the adminApi logout function to clear tokens
    adminApi.logout()
    // Also clear the legacy authentication flag
    localStorage.removeItem("adminAuthenticated")
    // Redirect to login page
    router.push("/admin/login")
  }

  // We need to pass this state to the sidebar component
  // Add this near the end of the component, before the return statement
  useEffect(() => {
    // This dispatches a custom event that the sidebar can listen for
    document.dispatchEvent(
      new CustomEvent("toggle-sidebar", {
        detail: { isOpen: isSidebarOpen },
      }),
    )
  }, [isSidebarOpen])

  return (
    <header className="bg-white border-b sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and mobile menu button */}
          <div className="flex items-center">
            {/* Then modify the button to toggle the sidebar */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden mr-2"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              aria-label="Toggle sidebar"
            >
              <Menu className="h-6 w-6" />
              <span className="sr-only">Toggle sidebar</span>
            </Button>
            <Link href="/admin/dashboard" className="flex items-center gap-2">
              <div className="bg-blue-600 text-white p-1.5 rounded">
                <ShieldAlert className="h-6 w-6" />
              </div>
              <span className="text-xl font-bold text-blue-900 hidden md:inline-block">Admin Portal</span>
            </Link>
          </div>

          {/* Search bar - visible on desktop */}
          <div className="hidden md:flex items-center flex-1 max-w-md mx-4">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Right side icons */}
          <div className="flex items-center space-x-4">
            <AdminNotificationBell />

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                  <Avatar className="h-10 w-10 border-2 border-gray-800 dark:border-gray-200">
                    <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Admin" />
                    <AvatarFallback>A</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {adminUser?.name || "Admin User"}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {adminUser?.email || "<EMAIL>"}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => router.push("/admin/profile")}>Profile</DropdownMenuItem>
                <DropdownMenuItem onClick={() => router.push("/admin/settings")}>Settings</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  )
}

