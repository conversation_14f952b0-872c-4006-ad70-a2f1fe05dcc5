"use client"

import { useState, useEffect } from "react"
import { Bell, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { adminApi } from "@/lib/admin-api"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { formatRelativeTime } from "@/lib/utils/date"

type Notification = {
  id: string
  title: string
  message: string
  type: string
  status: string
  isRead: boolean
  createdAt: string
  updatedAt: string
}

export function AdminNotificationBell() {
  const router = useRouter()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isOpen, setIsOpen] = useState(false)

  // Fetch notifications when dropdown is opened
  const fetchNotifications = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await adminApi.getNotifications(1, 5) // Get first 5 notifications

      if (response.success) {
        setNotifications(response.data)
      } else {
        setError("Failed to fetch notifications")
        console.error("Failed to fetch notifications:", response.message)
      }
    } catch (err) {
      console.error("Error fetching notifications:", err)
      setError(err instanceof Error ? err.message : "An error occurred while fetching notifications")
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch notifications when dropdown is opened
  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
  }, [isOpen])

  // Fetch unread count on initial load
  useEffect(() => {
    const fetchUnreadCount = async () => {
      try {
        // For admin notifications, we'll use the regular getNotifications endpoint with a filter
        const response = await adminApi.getNotifications(1, 1, '', '', 'sent')
        if (response.success && response.pagination) {
          // We only need the count, not the actual notifications
          const count = response.pagination.total
          if (count > 0) {
            // If there are unread notifications, update the state with just the count info
            setNotifications(prev => prev.length > 0 ? prev : Array(Math.min(count, 3)).fill({ isRead: false } as any))
          }
        }
      } catch (error) {
        console.error("Error fetching unread count:", error)
      }
    }

    fetchUnreadCount()
  }, [])

  const unreadCount = notifications.filter((n) => !n.isRead).length

  // Format the date using our utility function
  const formatDate = (dateString: string) => {
    return formatRelativeTime(dateString);
  };

  return (
    <DropdownMenu onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 flex items-center justify-center text-[10px] text-white font-bold">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex justify-between">
          <span>Notifications</span>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {isLoading ? (
          <div className="text-center py-4">
            <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading notifications...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500 text-sm">
            <p>{error}</p>
            <Button
              variant="ghost"
              size="sm"
              className="mt-2 text-xs"
              onClick={fetchNotifications}
            >
              Try Again
            </Button>
          </div>
        ) : notifications.length > 0 ? (
          <div className="max-h-[300px] overflow-auto">
            {notifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className="flex flex-col items-start py-2 px-4 hover:bg-blue-50 cursor-pointer"
              >
                <div className="flex w-full justify-between">
                  <span className="font-medium text-sm">
                    {notification.title}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {notification.createdAt ? formatDate(notification.createdAt) : ''}
                  </span>
                </div>
                <p className="text-sm mt-1">{notification.message}</p>
              </DropdownMenuItem>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">No notifications</div>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem className="justify-center text-blue-600" onClick={() => router.push("/admin/notifications/view")}>
          View all notifications
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
