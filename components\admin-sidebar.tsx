"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Users,
  CreditCard,
  Settings,
  BarChart3,
  FileText,
  Bell,
  ChevronLeft,
  ChevronRight,
  Wallet,
  ShieldAlert,
} from "lucide-react"
import { Button } from "@/components/ui/button"

interface SidebarItem {
  title: string
  href: string
  icon: React.ElementType
}

export function AdminSidebar() {
  const pathname = usePathname()
  const [collapsed, setCollapsed] = useState(false)

  const sidebarItems: SidebarItem[] = [
    {
      title: "Dashboard",
      href: "/admin/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Users",
      href: "/admin/users",
      icon: Users,
    },
    {
      title: "Loans",
      href: "/admin/loans",
      icon: CreditCard,
    },
    {
      title: "Payments",
      href: "/admin/payments",
      icon: Wallet,
    },
    {
      title: "Reports",
      href: "/admin/reports",
      icon: BarChart3,
    },
    {
      title: "Documents",
      href: "/admin/documents",
      icon: FileText,
    },
    {
      title: "Notifications",
      href: "/admin/notifications",
      icon: Bell,
    },
    {
      title: "Settings",
      href: "/admin/settings",
      icon: Settings,
    },
  ]

  return (
    <div className={cn("bg-white border-r h-screen transition-all duration-300 relative", collapsed ? "w-20" : "w-64")}>
      <div className="p-4 flex items-center justify-center border-b">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <ShieldAlert className="h-6 w-6 text-blue-600" />
            <span className="font-bold text-lg">Admin Panel</span>
          </div>
        )}
        {collapsed && <ShieldAlert className="h-6 w-6 text-blue-600" />}
      </div>

      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-3 top-20 bg-white border rounded-full shadow-md"
        onClick={() => setCollapsed(!collapsed)}
      >
        {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
      </Button>

      <nav className="p-4 space-y-2 overflow-y-auto max-h-[calc(100vh-5rem)]">
        {sidebarItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
              pathname === item.href ? "bg-blue-50 text-blue-700" : "text-gray-600 hover:bg-gray-100",
              collapsed && "justify-center px-2",
            )}
          >
            <item.icon className={cn("h-5 w-5", pathname === item.href && "text-blue-600")} />
            {!collapsed && <span>{item.title}</span>}
          </Link>
        ))}
      </nav>
    </div>
  )
}

