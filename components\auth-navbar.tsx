import Link from "next/link"
import { CreditCard } from "lucide-react"

export function AuthNavbar() {
  return (
    <header className="w-full bg-white/90 border-b">
      <div className="container mx-auto px-4 py-4">
        <Link href="/" className="flex items-center gap-2">
          <div className="bg-blue-600 text-white p-1.5 rounded">
            <CreditCard className="h-6 w-6" />
          </div>
          <span className="text-xl font-bold text-blue-900">Umlamleli</span>
        </Link>
      </div>
    </header>
  )
}

