"use client"

import type React from "react"

import { BackgroundGradientAnimation } from "@/components/ui/background-gradient-animation"
import { cn } from "@/lib/utils"

interface BackgroundWrapperProps {
  children: React.ReactNode
  className?: string
}

// Modify the BackgroundWrapper component to properly handle scrolling
export function BackgroundWrapper({ children, className }: BackgroundWrapperProps) {
  return (
    <>
      {/* Fixed background that doesn't scroll */}
      <BackgroundGradientAnimation
        gradientBackgroundStart="rgb(0, 51, 102)"
        gradientBackgroundEnd="rgb(0, 17, 82)"
        firstColor="18, 113, 255"
        secondColor="80, 100, 255"
        thirdColor="100, 220, 255"
        fourthColor="120, 160, 255"
        fifthColor="100, 180, 255"
        pointerColor="140, 100, 255"
        containerClassName="!fixed inset-0 -z-10"
        className="z-10"
      />

      {/* Scrollable content container */}
      <div className={cn("min-h-screen w-full overflow-auto", className)}>{children}</div>
    </>
  )
}

