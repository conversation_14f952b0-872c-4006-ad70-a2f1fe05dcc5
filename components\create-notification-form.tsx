"use client"

import type React from "react"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Clock, Users } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface CreateNotificationFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (notificationData: any) => void
}

export function CreateNotificationForm({ isOpen, onClose, onSubmit }: CreateNotificationFormProps) {
  const [formData, setFormData] = useState({
    title: "",
    message: "",
    type: "system",
    recipientType: "all",
    specificRecipient: "",
    sendType: "now",
    scheduledDate: new Date(),
    scheduledTime: "12:00",
  })

  const [date, setDate] = useState<Date | undefined>(new Date())

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setDate(date)
      setFormData((prev) => ({ ...prev, scheduledDate: date }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    let status = "draft"
    if (formData.sendType === "now") {
      status = "sent"
    } else if (formData.sendType === "schedule") {
      status = "scheduled"
    }

    let recipients = "All Users"
    if (formData.recipientType === "specific") {
      recipients = formData.specificRecipient
    }

    const currentDate = new Date()
    let notificationDate = currentDate.toISOString()

    if (formData.sendType === "schedule" && date) {
      const [hours, minutes] = formData.scheduledTime.split(":").map(Number)
      const scheduledDate = new Date(date)
      scheduledDate.setHours(hours, minutes)
      notificationDate = scheduledDate.toISOString()
    }

    onSubmit({
      id: `NOTIF-${Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, "0")}`,
      title: formData.title,
      message: formData.message,
      type: formData.type,
      recipients,
      status,
      date: notificationDate,
    })

    resetForm()
  }

  const resetForm = () => {
    setFormData({
      title: "",
      message: "",
      type: "system",
      recipientType: "all",
      specificRecipient: "",
      sendType: "now",
      scheduledDate: new Date(),
      scheduledTime: "12:00",
    })
    setDate(new Date())
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          resetForm()
          onClose()
        }
      }}
    >
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Notification</DialogTitle>
          <DialogDescription>Create a new notification to send to users</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Notification Title</Label>
              <Input
                id="title"
                name="title"
                placeholder="Enter notification title"
                value={formData.title}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Notification Message</Label>
              <Textarea
                id="message"
                name="message"
                placeholder="Enter notification message"
                value={formData.message}
                onChange={handleChange}
                required
                className="min-h-[100px]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Notification Type</Label>
              <Select value={formData.type} onValueChange={(value) => handleSelectChange("type", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="payment">Payment</SelectItem>
                  <SelectItem value="loan">Loan</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="alert">Alert</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Recipients</Label>
              <RadioGroup
                value={formData.recipientType}
                onValueChange={(value) => handleSelectChange("recipientType", value)}
                className="flex flex-col space-y-1"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all-users" />
                  <Label htmlFor="all-users" className="cursor-pointer">
                    All Users
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="specific" id="specific-user" />
                  <Label htmlFor="specific-user" className="cursor-pointer">
                    Specific User
                  </Label>
                </div>
              </RadioGroup>

              {formData.recipientType === "specific" && (
                <div className="pt-2">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <Input
                      name="specificRecipient"
                      placeholder="Enter user ID (e.g., USR-001)"
                      value={formData.specificRecipient}
                      onChange={handleChange}
                      required={formData.recipientType === "specific"}
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>When to Send</Label>
              <RadioGroup
                value={formData.sendType}
                onValueChange={(value) => handleSelectChange("sendType", value)}
                className="flex flex-col space-y-1"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="now" id="send-now" />
                  <Label htmlFor="send-now" className="cursor-pointer">
                    Send Immediately
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="schedule" id="schedule" />
                  <Label htmlFor="schedule" className="cursor-pointer">
                    Schedule for Later
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="draft" id="save-draft" />
                  <Label htmlFor="save-draft" className="cursor-pointer">
                    Save as Draft
                  </Label>
                </div>
              </RadioGroup>

              {formData.sendType === "schedule" && (
                <div className="pt-2 space-y-4">
                  <div className="flex flex-col space-y-2">
                    <Label>Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {date ? format(date, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar mode="single" selected={date} onSelect={handleDateChange} initialFocus />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="flex flex-col space-y-2">
                    <Label>Time</Label>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <Input
                        type="time"
                        name="scheduledTime"
                        value={formData.scheduledTime}
                        onChange={handleChange}
                        required={formData.sendType === "schedule"}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                resetForm()
                onClose()
              }}
            >
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              {formData.sendType === "now"
                ? "Send Notification"
                : formData.sendType === "schedule"
                  ? "Schedule Notification"
                  : "Save as Draft"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

