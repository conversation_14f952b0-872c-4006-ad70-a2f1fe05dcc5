"use client"

import React, { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, X, FileText, Image, AlertCircle } from "lucide-react"
import { toast } from "sonner"
import { documentApi } from "@/lib/api"

interface DocumentUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onUploadSuccess: () => void
  userId?: string
  loanId?: string
}

const documentTypes = [
  { value: 'id', label: 'ID Document', icon: Image, description: 'Identity documents (images only)' },
  { value: 'collateral', label: 'Collateral', icon: Image, description: 'Collateral photos (images only)' },
  { value: 'contract', label: 'Contract', icon: FileText, description: 'Contract documents (PDF, Word)' },
  { value: 'statement', label: 'Statement', icon: FileText, description: 'Financial statements (PDF, Word)' },
  { value: 'other', label: 'Other', icon: FileText, description: 'Other documents (PDF, Word, images)' },
]

const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
const allowedDocumentTypes = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
]

export default function DocumentUploadModal({
  isOpen,
  onClose,
  onUploadSuccess,
  userId,
  loanId,
}: DocumentUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [documentType, setDocumentType] = useState<string>("")
  const [targetUserId, setTargetUserId] = useState<string>(userId || "")
  const [targetLoanId, setTargetLoanId] = useState<string>(loanId || "")
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  const resetForm = () => {
    setSelectedFile(null)
    setDocumentType("")
    setTargetUserId(userId || "")
    setTargetLoanId(loanId || "")
    setIsUploading(false)
    setDragActive(false)
  }

  const validateFileType = (file: File, docType: string): boolean => {
    if (docType === 'id' || docType === 'collateral') {
      return allowedImageTypes.includes(file.type)
    } else {
      return allowedDocumentTypes.includes(file.type) || allowedImageTypes.includes(file.type)
    }
  }

  const getFileTypeError = (docType: string): string => {
    if (docType === 'id' || docType === 'collateral') {
      return 'Please select an image file (JPG, PNG, GIF, WebP)'
    } else {
      return 'Please select a document file (PDF, Word, Excel, Text) or image'
    }
  }

  const handleFileSelect = (file: File) => {
    // Check file size (20MB limit)
    if (file.size > 20 * 1024 * 1024) {
      toast.error("File too large", {
        description: "Please select a file smaller than 20MB"
      })
      return
    }

    // Validate file type if document type is selected
    if (documentType && !validateFileType(file, documentType)) {
      toast.error("Invalid file type", {
        description: getFileTypeError(documentType)
      })
      return
    }

    setSelectedFile(file)
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }

  const handleUpload = async () => {
    if (!selectedFile || !documentType) {
      toast.error("Missing information", {
        description: "Please select a file and document type"
      })
      return
    }

    // Validate file type again
    if (!validateFileType(selectedFile, documentType)) {
      toast.error("Invalid file type", {
        description: getFileTypeError(documentType)
      })
      return
    }

    setIsUploading(true)

    try {
      await documentApi.uploadDocument(
        selectedFile,
        documentType,
        targetUserId || undefined,
        targetLoanId || undefined
      )

      toast.success("Document uploaded successfully", {
        description: "The document has been uploaded and is pending review"
      })

      onUploadSuccess()
      resetForm()
      onClose()
    } catch (error) {
      console.error('Upload error:', error)
      toast.error("Upload failed", {
        description: error instanceof Error ? error.message : "Failed to upload document"
      })
    } finally {
      setIsUploading(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B'
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB'
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }

  const selectedDocType = documentTypes.find(type => type.value === documentType)

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          resetForm()
          onClose()
        }
      }}
    >
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
          <DialogDescription>
            Upload a new document to the system. Select the document type and file.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Document Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="documentType">Document Type *</Label>
            <Select value={documentType} onValueChange={setDocumentType}>
              <SelectTrigger>
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center gap-2">
                      <type.icon className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-xs text-muted-foreground">{type.description}</div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* User ID (optional) */}
          {!userId && (
            <div className="space-y-2">
              <Label htmlFor="userId">User ID (optional)</Label>
              <Input
                id="userId"
                placeholder="Enter user ID if uploading for a specific user"
                value={targetUserId}
                onChange={(e) => setTargetUserId(e.target.value)}
              />
            </div>
          )}

          {/* Loan ID (optional) */}
          {!loanId && (
            <div className="space-y-2">
              <Label htmlFor="loanId">Loan ID (optional)</Label>
              <Input
                id="loanId"
                placeholder="Enter loan ID if document is related to a specific loan"
                value={targetLoanId}
                onChange={(e) => setTargetLoanId(e.target.value)}
              />
            </div>
          )}

          {/* File Upload Area */}
          <div className="space-y-2">
            <Label>File *</Label>
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive
                  ? 'border-blue-500 bg-blue-50'
                  : selectedFile
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {selectedFile ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    {selectedFile.type.startsWith('image/') ? (
                      <Image className="h-8 w-8 text-green-600" />
                    ) : (
                      <FileText className="h-8 w-8 text-green-600" />
                    )}
                    <div>
                      <p className="font-medium text-green-700">{selectedFile.name}</p>
                      <p className="text-sm text-green-600">{formatFileSize(selectedFile.size)}</p>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedFile(null)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                  <div>
                    <p className="text-gray-600">
                      Drag and drop a file here, or{' '}
                      <label className="text-blue-600 hover:text-blue-700 cursor-pointer underline">
                        browse
                        <input
                          type="file"
                          className="hidden"
                          onChange={handleFileChange}
                          accept={
                            documentType === 'id' || documentType === 'collateral'
                              ? 'image/*'
                              : 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt'
                          }
                        />
                      </label>
                    </p>
                    <p className="text-sm text-gray-500">Maximum file size: 20MB</p>
                    {selectedDocType && (
                      <div className="flex items-center justify-center gap-1 mt-2">
                        <AlertCircle className="h-4 w-4 text-blue-500" />
                        <p className="text-sm text-blue-600">{selectedDocType.description}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              resetForm()
              onClose()
            }}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleUpload}
            disabled={!selectedFile || !documentType || isUploading}
          >
            {isUploading ? (
              <>
                <Upload className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload Document
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
