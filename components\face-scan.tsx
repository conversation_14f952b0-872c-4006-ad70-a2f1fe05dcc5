"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import dynamic from 'next/dynamic'
import { But<PERSON> } from "@/components/ui/button"
import { Camera, CheckCircle2, RefreshCw, AlertCircle, Loader2, XCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { FaceRecognitionService } from '../lib/face-recognition'
import { useAuth } from "@/lib/auth-context"
import { loadFaceApiModels, areModelsLoaded } from '@/lib/load-models'

// Dynamic imports to prevent SSR
const DynamicVideo = dynamic<React.DetailedHTMLProps<React.VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>>(
  () => Promise.resolve(({ ...props }) => <video {...props} />), {
    ssr: false
  }
);

interface FaceScanProps {
  onVerificationComplete: (success: boolean) => void
  onFaceRegistered: (descriptor: Float32Array) => void
  isVerifying?: boolean
  referenceDescriptor?: Float32Array
}

export default function FaceScan({
  onVerificationComplete,
  onFaceRegistered,
  isVerifying = false,
  referenceDescriptor
}: FaceScanProps) {
  // Debug log props
  console.log('FaceScan props:', {
    isVerifying,
    hasReferenceDescriptor: !!referenceDescriptor,
    referenceDescriptorLength: referenceDescriptor ? referenceDescriptor.length : 'N/A'
  });
  const { user } = useAuth()
  const [isCapturing, setIsCapturing] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [faceDetected, setFaceDetected] = useState(false)
  const [verificationResult, setVerificationResult] = useState<boolean | null>(null)
  const [similarity, setSimilarity] = useState<number | null>(null)
  const [descriptor, setDescriptor] = useState<Float32Array | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [isInitializing, setIsInitializing] = useState(true)
  const [initAttempts, setInitAttempts] = useState(0)
  const [isCameraAvailable, setIsCameraAvailable] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  // Track media stream for cleanup
  const mediaStreamRef = useRef<MediaStream | null>(null)
  const [isModelReady, setIsModelReady] = useState(false)

  // Create service instance only on client side
  const [faceRecognitionService] = useState(() => {
    if (typeof window === 'undefined') return null;
    return new FaceRecognitionService();
  });

  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imgRef = useRef<HTMLImageElement>(null)
  const isMounted = useRef(false)

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  const cleanup = useCallback(() => {
    console.log('Cleanup called');

    // Clear any messages
    setError(null);
    setSuccessMessage(null);

    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => {
        console.log('Stopping track:', track.kind);
        track.stop();
      });
      videoRef.current.srcObject = null;
    }
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }
  }, []);

  const initFaceRecognition = useCallback(async () => {
    let timeoutId: NodeJS.Timeout | null = null;

    try {
      setIsInitializing(true);
      console.log('Initializing face recognition...');

      // Set a timeout to prevent endless loading
      timeoutId = setTimeout(() => {
        console.warn('Face recognition initialization timed out after 20 seconds');
        setError('Face detection initialization timed out. Please try refreshing the page.');
        setIsInitializing(false);
      }, 20000);

      // First try to load models directly using our simplified approach
      try {
        if (!areModelsLoaded()) {
          console.log('Loading face-api.js models directly...');
          await loadFaceApiModels();
          console.log('Models loaded successfully using direct approach');
        } else {
          console.log('Models already loaded, skipping direct loading');
        }
      } catch (modelError) {
        console.error('Error loading models directly:', modelError);
        // Continue with normal initialization even if direct loading fails
      }

      if (!faceRecognitionService) {
        throw new Error('Face recognition service not available');
      }

      const success = await faceRecognitionService.initialize();

      // Clear timeout as we got a response
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (!success) {
        if (initAttempts < 3) {
          console.warn(`Face recognition initialization failed, attempt ${initAttempts + 1}/3. Retrying...`);
          setInitAttempts(prev => prev + 1);
          setTimeout(initFaceRecognition, 2000);
          return;
        } else {
          throw new Error('Failed to initialize face recognition after 3 attempts');
        }
      }

      setIsModelReady(true);

      // Check if this is a first-time scan
      if (user && user.id) {
        // User is already verified
        console.log('User face verification status:', user.isFaceVerified ? 'Verified' : 'Not verified');
      }

      setIsInitializing(false);
      console.log('Face recognition initialized successfully');
    } catch (err) {
      // Clear timeout as we got an error
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      console.error('Error initializing face recognition:', err);
      setError('Failed to initialize face recognition system. Please try refreshing the page or click retry.');
      setIsInitializing(false);
      cleanup();
    }
  }, [user, initAttempts, cleanup, faceRecognitionService]);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    initFaceRecognition();

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      cleanup();
    };
  }, [initFaceRecognition, cleanup]);

  const startCamera = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Wait for next tick to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100));

      // Double check component is still mounted
      if (!isMounted.current) {
        console.log("Component unmounted before camera could start");
        return;
      }

      // Ensure the video element is ready before proceeding
      if (!videoRef.current) {
        console.error("Video element not available");
        // Instead of retrying immediately, set an error
        setError('Cannot access video element. Please try refreshing the page.');
        setIsLoading(false);
        return;
      }

      console.log('Video element found, requesting camera access...');

      // Add a timeout for camera access
      const cameraPromise = navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'user',
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      });

      // Set timeout for camera access
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Camera access timeout')), 10000);
      });

      // Race the camera promise against the timeout
      const mediaStream = await Promise.race([
        cameraPromise,
        timeoutPromise
      ]) as MediaStream;

      // Check if component is still mounted
      if (!isMounted.current) {
        // Clean up the stream if component unmounted
        mediaStream.getTracks().forEach(track => track.stop());
        return;
      }

      // Double check video element is still available
      if (!videoRef.current) {
        // Clean up the stream if video element is no longer available
        mediaStream.getTracks().forEach(track => track.stop());
        throw new Error('Video element not found after camera access');
      }

      console.log('Camera access granted, setting up video element...');
      videoRef.current.srcObject = mediaStream;

      // Wait for metadata and start playing with timeout
      await Promise.race([
        new Promise<boolean>((resolve, reject) => {
          const video = videoRef.current;
          if (!video) {
            reject(new Error('Video element not found during metadata loading'));
            return;
          }

          // Handle both already loaded metadata and future loading
          if (video.readyState >= 2) {
            // Metadata already loaded
            video.play()
              .then(() => {
                console.log('Video playback started successfully (metadata was already loaded)');
                mediaStreamRef.current = mediaStream;
                resolve(true);
              })
              .catch(playError => {
                console.error('Error playing video (metadata was already loaded):', playError);
                reject(playError);
              });
          } else {
            // Wait for metadata to load
            video.onloadedmetadata = () => {
              video.play()
                .then(() => {
                  console.log('Video playback started successfully');
                  mediaStreamRef.current = mediaStream;
                  resolve(true);
                })
                .catch(playError => {
                  console.error('Error playing video:', playError);
                  reject(playError);
                });
            };
          }
        }),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Video metadata loading timeout')), 10000)
        )
      ]);

      setIsLoading(false);
    } catch (err) {
      console.error('Error in startCamera:', err);
      setError(`Failed to access camera: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setIsLoading(false);
      cleanup();
    }
  }, [cleanup]);

  // Add a useEffect specifically to handle camera initialization when component is mounted
  useEffect(() => {
    // Only try to start camera when the component is mounted, models are ready,
    // and camera is available
    if (isMounted.current && isModelReady && !isInitializing && isCameraAvailable) {
      // Give the DOM time to actually render the video element
      const timer = setTimeout(() => {
        console.log("Starting camera with delay to ensure DOM is ready");
        startCamera();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isModelReady, isInitializing, isCameraAvailable, startCamera]);

  useEffect(() => {
    async function checkCameraAvailability() {
      try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          setIsCameraAvailable(false)
          return
        }

        const stream = await navigator.mediaDevices.getUserMedia({ video: true })
        setIsCameraAvailable(true)
        stream.getTracks().forEach(track => track.stop())
      } catch (err) {
        console.error("Camera availability check failed:", err)
        setIsCameraAvailable(false)
      }
    }

    checkCameraAvailability()
  }, [])

  const captureImage = async () => {
    if (!videoRef.current || !canvasRef.current || !imgRef.current) {
      setError('Required references not found');
      return;
    }

    if (!faceRecognitionService) {
      setError('Face recognition service not available');
      return;
    }

    try {
      setIsCapturing(true);
      setError(null);
      setSuccessMessage(null);

      const ctx = canvasRef.current.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }

      canvasRef.current.width = videoRef.current.videoWidth;
      canvasRef.current.height = videoRef.current.videoHeight;

      ctx.drawImage(videoRef.current, 0, 0, canvasRef.current.width, canvasRef.current.height);

      imgRef.current.src = canvasRef.current.toDataURL('image/jpeg');

      await new Promise((resolve, reject) => {
        if (!imgRef.current) {
          reject(new Error('Image reference not found'));
          return;
        }

        imgRef.current.onload = resolve;
        imgRef.current.onerror = () => reject(new Error('Failed to load captured image'));
      });

      setIsProcessing(true);
      // Check if face is detected
      await faceRecognitionService.detectFace(imgRef.current);
      setFaceDetected(true);

      const faceDescriptor = await faceRecognitionService.getFaceDescriptor(imgRef.current);
      console.log('Face descriptor obtained:', faceDescriptor ? 'Success' : 'Failed');
      setDescriptor(faceDescriptor);

      // After capturing, automatically proceed based on verification mode
      if (isVerifying && referenceDescriptor) {
        // For returning users, automatically verify
        console.log('Auto-verifying face after capture (returning user)');
        await verifyFace();
      } else {
        // For new users, show success message
        console.log('Face captured for new user, waiting for registration');
        setSuccessMessage('Face captured successfully! Click "Register Face" to continue.');
      }

      setIsProcessing(false);
    } catch (error) {
      console.error('Image capture failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to capture image');
      setFaceDetected(false);
      setDescriptor(null);
    } finally {
      setIsCapturing(false);
      setIsProcessing(false);
    }
  };

  const verifyFace = async () => {
    console.log('verifyFace function called');

    if (!imgRef.current) {
      console.error('Image reference not found');
      setError('Image reference not found');
      return;
    }

    if (!faceRecognitionService) {
      console.error('Face recognition service not available');
      setError('Face recognition service not available');
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);
      setSuccessMessage(null);

      console.log('Verification mode:', isVerifying);
      console.log('Has reference descriptor:', !!referenceDescriptor);
      console.log('Descriptor length:', referenceDescriptor ? referenceDescriptor.length : 'N/A');

      // If we have a reference descriptor, perform verification
      if (isVerifying && referenceDescriptor) {
        console.log('Verifying face against stored descriptor');

        // Make sure we have a face descriptor to compare
        if (!descriptor) {
          console.log('No current face descriptor available, getting one now...');
          const newDescriptor = await faceRecognitionService.getFaceDescriptor(imgRef.current);
          setDescriptor(newDescriptor);
          console.log('New descriptor obtained, length:', newDescriptor.length);

          // Use the new descriptor for verification
          const result = await faceRecognitionService.verifyFace(imgRef.current, referenceDescriptor);
          console.log('Verification result:', result);
          setVerificationResult(result.verified);
          setSimilarity(result.similarity);

          // Call the parent component's callback with the result
          console.log('Calling onVerificationComplete with result:', result.verified);
          onVerificationComplete(result.verified);

          // Show appropriate message
          if (result.verified) {
            setSuccessMessage(`Face verification successful! Similarity: ${(result.similarity * 100).toFixed(1)}%`);
          } else {
            setError(`Face verification failed. Similarity: ${(result.similarity * 100).toFixed(1)}% (threshold: 60%)`);
          }
        } else {
          console.log('Using existing descriptor for verification');
          // We already have a descriptor, use it directly
          const result = await faceRecognitionService.verifyFace(imgRef.current, referenceDescriptor);
          console.log('Verification result:', result);
          setVerificationResult(result.verified);
          setSimilarity(result.similarity);

          // Call the parent component's callback with the result
          console.log('Calling onVerificationComplete with result:', result.verified);
          onVerificationComplete(result.verified);

          // Show appropriate message
          if (result.verified) {
            setSuccessMessage(`Face verification successful! Similarity: ${(result.similarity * 100).toFixed(1)}%`);
          } else {
            setError(`Face verification failed. Similarity: ${(result.similarity * 100).toFixed(1)}% (threshold: 60%)`);
          }
        }
      } else {
        // For first-time users, just get the descriptor and register it
        console.log('First-time user, registering face');
        const faceDescriptor = await faceRecognitionService.getFaceDescriptor(imgRef.current);
        console.log('Face descriptor obtained for registration, length:', faceDescriptor.length);
        setDescriptor(faceDescriptor);
        setSuccessMessage('Face captured successfully! Click "Register Face" to continue.');
      }
    } catch (error) {
      console.error('Face verification failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to verify face');
      setVerificationResult(false);
    } finally {
      // Add a small delay before removing the processing state to ensure UI updates are visible
      setTimeout(() => {
        setIsProcessing(false);
        console.log('Processing state reset');
      }, 500);
    }
  };

  // Reset function used in UI buttons
  const handleReset = useCallback(() => {
    setDescriptor(null);
    setError(null);
    startCamera();
  }, [startCamera]);

  useEffect(() => {
    if (isVerifying && referenceDescriptor && imgRef.current) {
      verifyFace();
    }
  }, [isVerifying, referenceDescriptor]);

  if (isInitializing) {
    return (
      <div className="space-y-6 text-center">
        <h2 className="text-2xl font-bold mb-4">Initializing Face Verification</h2>
        <div className="flex justify-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        </div>
        <p className="text-muted-foreground">
          Loading face recognition models, please wait...
        </p>
      </div>
    );
  }

  if (!isModelReady && !isInitializing) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTitle>Face Recognition Error</AlertTitle>
          <AlertDescription>
            Failed to load face recognition models. Please refresh the page and try again.
          </AlertDescription>
        </Alert>

        <Button onClick={() => window.location.reload()} className="w-full">
          Refresh Page
        </Button>
      </div>
    );
  }

  if (isCameraAvailable === false) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTitle>Camera Access Required</AlertTitle>
          <AlertDescription>
            Camera access is required for face verification. Please enable camera access in your browser settings and refresh the page.
          </AlertDescription>
        </Alert>

        <Button onClick={() => window.location.reload()} className="w-full">
          Refresh Page
        </Button>
      </div>
    );
  }

  if (error) {
  return (
    <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        <Button onClick={() => {
          setError(null);
          setIsInitializing(true);
          setInitAttempts(0);
          initFaceRecognition();
        }} className="w-full">
          <RefreshCw className="mr-2 h-4 w-4" />
          Retry
        </Button>

        <Button
          onClick={handleReset}
          className="w-full mt-2"
          variant="outline"
        >
          Reset and Restart Camera
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Display mode indicator */}
      <div className="text-center mb-2">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isVerifying ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
          {isVerifying ? 'Face Verification Mode' : 'Face Registration Mode'}
        </span>
      </div>
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {successMessage && (
        <Alert className="bg-green-50 border-green-200 text-green-800">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      {/* Video container with responsive aspect ratio */}
      <div className="relative w-full overflow-hidden rounded-lg bg-black aspect-[4/3]">
        {/* Always render the video element, but hide it when loading */}
        <DynamicVideo
          key="face-scan-video"
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="h-full w-full object-cover"
          style={{ display: isLoading ? 'none' : 'block' }}
        />

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
        )}

        <canvas
          ref={canvasRef}
          className="hidden"
          width={640}
          height={480}
        />
        <img
          ref={imgRef}
          className="hidden"
          crossOrigin="anonymous"
          alt="Captured face"
          title="Captured face for verification"
        />
      </div>

      {/* Controls in a separate container below the video */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-4 w-full max-w-md mx-auto">
          <Button
            variant="secondary"
            size="lg"
            className="w-full"
            onClick={captureImage}
            disabled={isCapturing || isProcessing || isLoading}
          >
            {isCapturing ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Camera className="mr-2 h-4 w-4" />
            )}
            {isCapturing ? 'Capturing...' : 'Capture Image'}
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="w-full"
            onClick={handleReset}
            disabled={isCapturing || isProcessing || isLoading}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Reset
          </Button>

          {/* Show different buttons based on whether it's verification or registration */}
          {isVerifying ? (
            <Button
              variant="default"
              size="lg"
              className="w-full sm:col-span-2"
              onClick={() => {
                console.log('Verify Face button clicked');
                if (!descriptor) {
                  setError('No face detected. Please capture your face first.');
                  return;
                }
                verifyFace();
              }}
              disabled={isCapturing || isProcessing || isLoading || !descriptor}
            >
              <CheckCircle2 className="mr-2 h-4 w-4" />
              Verify Face
            </Button>
          ) : (
            <Button
              variant="default"
              size="lg"
              className="w-full sm:col-span-2"
              onClick={() => {
                console.log('Register Face button clicked, descriptor:', descriptor ? 'Available' : 'Not available');
                if (descriptor) {
                  try {
                    // Call the parent component's callback to store the descriptor
                    onFaceRegistered(descriptor);
                    console.log('Face registration callback executed');

                    // Show success message
                    setError(null);
                    setSuccessMessage('Face registered successfully! Proceeding to next step...');

                    // Add a visual confirmation
                    setIsProcessing(true);

                    // The parent component will handle navigation to the next step
                    // We just provide visual feedback here
                    setTimeout(() => {
                      setIsProcessing(false);
                    }, 1000);
                  } catch (err) {
                    console.error('Error in face registration callback:', err);
                    setError('Failed to register face. Please try again.');
                  }
                } else {
                  setError('No face descriptor available. Please capture your face again.');
                }
              }}
              disabled={!descriptor || isProcessing || isLoading}
            >
              <CheckCircle2 className="mr-2 h-4 w-4" />
              Register Face
            </Button>
          )}
      </div>

      {isProcessing && (
        <div className="flex items-center justify-center space-x-2 text-muted-foreground mt-4">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Processing...</span>
        </div>
      )}

      {faceDetected && !isVerifying && (
        <Alert className="mt-4">
          <CheckCircle2 className="h-4 w-4" />
          <AlertTitle>Face Detected</AlertTitle>
          <AlertDescription>
            A face was successfully detected in the image.
          </AlertDescription>
        </Alert>
      )}

      {verificationResult !== null && (
        <Alert variant={verificationResult ? "default" : "destructive"} className="mt-4">
          {verificationResult ? (
            <CheckCircle2 className="h-4 w-4" />
          ) : (
            <XCircle className="h-4 w-4" />
          )}
          <AlertTitle>
            {verificationResult ? 'Verification Successful' : 'Verification Failed'}
          </AlertTitle>
          <AlertDescription>
            {verificationResult
              ? 'Face verification completed successfully.'
              : 'Face verification failed. Please try again.'}
            {similarity !== null && ` Similarity: ${(similarity * 100).toFixed(1)}%`}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

