"use client"

import React, { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"
import { adminApi } from "@/lib/admin-api"

interface Loan {
  id: string
  userId: string
  userName: string
  userPhoneNumber?: string // Add phone number field
  amount: number
  purpose: string
  status: "approved" | "pending" | "rejected" | "disbursed" | "paid"
  applicationDate: string
  approvalDate?: string
  dueDate?: string
  interestRate: number
  principalAmount: number
  totalPayment: number
  amountDue: number
  isPaid: boolean
  paidDate?: string
}

interface EditLoanFormProps {
  isOpen: boolean
  onClose: () => void
  loan: Loan | null
  onSuccess: () => void
}

export function EditLoanForm({ isOpen, onClose, loan, onSuccess }: EditLoanFormProps) {
  const [formData, setFormData] = useState<{
    amount: string
    purpose: string
    interestRate: string
    dueDate: string
    status: string
  }>({
    amount: "",
    purpose: "",
    interestRate: "",
    dueDate: "",
    status: "",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Initialize form with loan data when it changes
  useEffect(() => {
    if (loan) {
      setFormData({
        amount: loan.amount.toString(),
        purpose: loan.purpose,
        interestRate: loan.interestRate.toString(),
        dueDate: loan.dueDate ? new Date(loan.dueDate).toISOString().split("T")[0] : "",
        status: loan.status,
      })
    }
  }, [loan])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.amount || isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = "Please enter a valid amount greater than 0"
    }

    if (!formData.purpose.trim()) {
      newErrors.purpose = "Purpose is required"
    }

    if (!formData.interestRate || isNaN(Number(formData.interestRate)) || Number(formData.interestRate) < 0) {
      newErrors.interestRate = "Please enter a valid interest rate (0 or greater)"
    }

    if (formData.status === "approved" || formData.status === "disbursed") {
      if (!formData.dueDate) {
        newErrors.dueDate = "Due date is required for approved or disbursed loans"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    if (!loan) {
      toast.error("No loan selected for editing")
      return
    }

    try {
      setIsLoading(true)

      // Calculate the new total payment based on the updated amount and interest rate
      const amount = Number(formData.amount)
      const interestRate = Number(formData.interestRate)
      const interestAmount = (amount * interestRate) / 100
      const totalPayment = amount + interestAmount

      const loanData = {
        amount,
        purpose: formData.purpose,
        interestRate,
        dueDate: formData.dueDate || undefined,
        status: formData.status,
        principalAmount: amount,
        totalPayment,
        amountDue: totalPayment,
      }

      const response = await adminApi.updateLoan(loan.id, loanData)

      if (response.success) {
        toast.success("Loan updated successfully")
        onSuccess()
        onClose()
      } else {
        toast.error(response.message || "Failed to update loan")
      }
    } catch (error) {
      console.error("Error updating loan:", error)
      toast.error("An error occurred while updating the loan")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Loan</DialogTitle>
          <DialogDescription>
            Update the loan details for {loan?.userName} (ID: {loan?.id})
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Principal Amount (E)</Label>
              <Input
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                placeholder="Enter amount"
                disabled={isLoading}
              />
              {errors.amount && <p className="text-sm text-red-500">{errors.amount}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="interestRate">Interest Rate (%)</Label>
              <Input
                id="interestRate"
                name="interestRate"
                value={formData.interestRate}
                onChange={handleChange}
                placeholder="Enter interest rate"
                disabled={isLoading}
              />
              {errors.interestRate && <p className="text-sm text-red-500">{errors.interestRate}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="purpose">Purpose</Label>
            <Input
              id="purpose"
              name="purpose"
              value={formData.purpose}
              onChange={handleChange}
              placeholder="Enter loan purpose"
              disabled={isLoading}
            />
            {errors.purpose && <p className="text-sm text-red-500">{errors.purpose}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                name="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={handleChange}
                disabled={isLoading || formData.status === "pending" || formData.status === "rejected"}
              />
              {errors.dueDate && <p className="text-sm text-red-500">{errors.dueDate}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange("status", value)}
                disabled={isLoading}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="disbursed">Disbursed</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
