"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Download, Loader2, TrendingUp, TrendingDown, Minus, AlertCircle } from "lucide-react"
import { toast } from "sonner"
import { reportsApi } from "@/lib/api"

interface FunctionalReportCardProps {
  title: string
  description: string
  icon: React.ReactNode
  period: string
  reportType: 'financial' | 'user' | 'loan' | 'operational'
  reportName: string
  className?: string
}

interface ReportData {
  [key: string]: any
  generatedAt?: string
}

export default function FunctionalReportCard({
  title,
  description,
  icon,
  period,
  reportType,
  reportName,
  className = ""
}: FunctionalReportCardProps) {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [exporting, setExporting] = useState(false)

  // Fetch report data
  const fetchReportData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await reportsApi.getSpecificReport(reportType, reportName, period)
      
      if (response.success) {
        setReportData(response.data)
      } else {
        setError('Failed to load report data')
      }
    } catch (error) {
      console.error('Error fetching report data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load report')
    } finally {
      setLoading(false)
    }
  }

  // Load data when component mounts or period changes
  useEffect(() => {
    fetchReportData()
  }, [period, reportType, reportName])

  // Handle export
  const handleExport = async (format: 'json' | 'csv') => {
    try {
      setExporting(true)
      
      const { blob, filename } = await reportsApi.exportReport(reportType, reportName, period, format)
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast.success(`Report exported as ${format.toUpperCase()}`)
    } catch (error) {
      console.error('Error exporting report:', error)
      toast.error('Failed to export report', {
        description: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setExporting(false)
    }
  }

  // Render key metrics based on report type
  const renderKeyMetrics = () => {
    if (!reportData) return null

    switch (reportName) {
      case 'cash-flow':
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Net Cash Flow</span>
              <span className={`font-semibold ${reportData.netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                E{reportData.netCashFlow?.toLocaleString() || '0'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Inflows</span>
              <span className="font-medium text-green-600">E{reportData.inflows?.toLocaleString() || '0'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Outflows</span>
              <span className="font-medium text-red-600">E{reportData.outflows?.toLocaleString() || '0'}</span>
            </div>
          </div>
        )

      case 'profit-loss':
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Net Profit</span>
              <span className={`font-semibold ${reportData.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                E{reportData.netProfit?.toLocaleString() || '0'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Revenue</span>
              <span className="font-medium">E{reportData.revenue?.toLocaleString() || '0'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Profit Margin</span>
              <span className="font-medium">{reportData.profitMargin || 0}%</span>
            </div>
          </div>
        )

      case 'outstanding-loans':
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Total Outstanding</span>
              <span className="font-semibold text-blue-600">
                E{reportData.totalOutstanding?.toLocaleString() || '0'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Active Loans</span>
              <span className="font-medium">{reportData.activeLoanCount || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Average Amount</span>
              <span className="font-medium">E{reportData.averageLoanAmount?.toLocaleString() || '0'}</span>
            </div>
          </div>
        )

      case 'repayment-analysis':
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Repayment Rate</span>
              <span className="font-semibold text-green-600">{reportData.repaymentRate || 0}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Default Rate</span>
              <span className="font-medium text-red-600">{reportData.defaultRate || 0}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Overdue Loans</span>
              <span className="font-medium">{reportData.overdueLoans || 0}</span>
            </div>
          </div>
        )

      case 'transaction-summary':
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Total Transactions</span>
              <span className="font-semibold">{reportData.total || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Completed</span>
              <span className="font-medium text-green-600">{reportData.completed || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Total Amount</span>
              <span className="font-medium">E{reportData.totalAmount?.toLocaleString() || '0'}</span>
            </div>
          </div>
        )

      case 'user-growth':
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">New Users</span>
              <span className="font-semibold text-blue-600">{reportData.newUsersCount || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Total Users</span>
              <span className="font-medium">{reportData.totalUsers || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Growth Rate</span>
              <span className="font-medium text-green-600">{reportData.growthRate || 0}%</span>
            </div>
          </div>
        )

      case 'user-activity':
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Active Users</span>
              <span className="font-semibold text-blue-600">{reportData.activeUsers || 0}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Activity Rate</span>
              <span className="font-medium">{reportData.activityRate || 0}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Face Verified</span>
              <span className="font-medium">{reportData.verificationStats?.faceVerified || 0}</span>
            </div>
          </div>
        )

      default:
        // Generic display for other reports
        return (
          <div className="space-y-2">
            {Object.entries(reportData)
              .filter(([key]) => !['period', 'generatedAt'].includes(key))
              .slice(0, 3)
              .map(([key, value]) => (
                <div key={key} className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                  <span className="font-medium">
                    {typeof value === 'number' ? value.toLocaleString() : String(value)}
                  </span>
                </div>
              ))}
          </div>
        )
    }
  }

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          {icon}
          <div>
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <CardDescription className="text-xs">{description}</CardDescription>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleExport('json')}
            disabled={exporting || loading || !!error}
            className="h-8 w-8 p-0"
          >
            {exporting ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Download className="h-3 w-3" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Loading...</span>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8 text-center">
            <div>
              <AlertCircle className="h-6 w-6 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-red-600">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchReportData}
                className="mt-2"
              >
                Retry
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {renderKeyMetrics()}
            
            {reportData?.generatedAt && (
              <div className="pt-2 border-t">
                <p className="text-xs text-muted-foreground">
                  Generated: {new Date(reportData.generatedAt).toLocaleString()}
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
