"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"

interface LoanAgreementProps {
  onAccept: () => void
}

export function LoanAgreement({ onAccept }: LoanAgreementProps) {
  const [agreed, setAgreed] = useState(false)

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-center mb-4">Loan Agreement</h2>
        <p className="text-muted-foreground text-center mb-6">Please read and accept the terms and conditions below</p>
      </div>

      <ScrollArea className="h-64 rounded-md border p-4">
        <div className="space-y-4">
          <h3 className="font-semibold">Terms and Conditions</h3>

          <p>
            This Loan Agreement ("Agreement") is entered into between the Lender and the Borrower as identified in the
            loan application.
          </p>

          <h4 className="font-medium mt-4">1. Loan Amount and Interest</h4>
          <p>
            The Lender agrees to lend the Borrower the amount specified in the loan application. The loan shall bear
            interest at the rate specified in the loan details.
          </p>

          <h4 className="font-medium mt-4">2. Repayment</h4>
          <p>
            The Borrower agrees to repay the loan amount plus interest according to the repayment schedule. Payments
            shall be made via the specified payment method.
          </p>

          <h4 className="font-medium mt-4">3. Late Payments</h4>
          <p>
            Any payment not received within 5 days of the due date shall be subject to a late fee. Continued late
            payments may result in additional penalties.
          </p>

          <h4 className="font-medium mt-4">4. Collateral</h4>
          <p>
            The Borrower agrees to provide collateral as described in the loan application. The Lender shall have the
            right to claim the collateral in case of default.
          </p>

          <h4 className="font-medium mt-4">5. Default</h4>
          <p>
            The Borrower shall be in default if any payment is late by more than 30 days. In case of default, the entire
            unpaid balance shall become immediately due.
          </p>

          <h4 className="font-medium mt-4">6. Privacy Policy</h4>
          <p>
            The Lender will collect and process personal data in accordance with the Privacy Policy. This includes face
            verification and contact information.
          </p>

          <h4 className="font-medium mt-4">7. Governing Law</h4>
          <p>
            This Agreement shall be governed by and construed in accordance with the laws of the jurisdiction specified
            in the loan details.
          </p>
        </div>
      </ScrollArea>

      <div className="flex items-center space-x-2">
        <Checkbox id="terms" checked={agreed} onCheckedChange={(checked) => setAgreed(checked === true)} />
        <label
          htmlFor="terms"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          I have read and agree to the terms and conditions
        </label>
      </div>

      <div className="flex justify-center">
        <Button onClick={onAccept} disabled={!agreed} className="w-full md:w-auto bg-blue-600 hover:bg-blue-700">
          Continue
        </Button>
      </div>
    </div>
  )
}

