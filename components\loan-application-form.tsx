"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { loanApi } from "@/lib/api"
import { toast } from "sonner"

interface LoanFormData {
  loanAmount: string
  purpose: string
  collateral: string
  phoneNumber: string
  termInMonths: string
  loanId?: string
  applicationDate?: string
}

interface LoanApplicationFormProps {
  formData: LoanFormData
  updateFormData: (data: Partial<LoanFormData>) => void
  onSubmit: () => void
}

export function LoanApplicationForm({ formData, updateFormData, onSubmit }: LoanApplicationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [availableCredit, setAvailableCredit] = useState(600) // Default to max
  const [isLoadingCredit, setIsLoadingCredit] = useState(true)

  // Fetch available credit when component mounts
  useEffect(() => {
    const fetchAvailableCredit = async () => {
      try {
        setIsLoadingCredit(true)
        const response = await loanApi.getAvailableCredit()
        if (response.success) {
          setAvailableCredit(response.data.availableCredit)
        }
      } catch (error) {
        console.error('Error fetching available credit:', error)
        toast.error('Failed to fetch available credit')
      } finally {
        setIsLoadingCredit(false)
      }
    }

    fetchAvailableCredit()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsSubmitting(true)

    try {
      // Basic form validation
      if (!formData.loanAmount || Number(formData.loanAmount) <= 0) {
        setError("Please enter a valid loan amount")
        setIsSubmitting(false)
        return
      }

      // Check against available credit
      if (Number(formData.loanAmount) > availableCredit) {
        setError(`Loan amount exceeds your available credit of E${availableCredit.toFixed(2)}`)
        setIsSubmitting(false)
        return
      }

      if (!formData.purpose) {
        setError("Please select a purpose for your loan")
        setIsSubmitting(false)
        return
      }

      if (!formData.collateral) {
        setError("Please provide collateral information")
        setIsSubmitting(false)
        return
      }

      if (!formData.phoneNumber) {
        setError("Please enter your phone number")
        setIsSubmitting(false)
        return
      }

      if (!formData.termInMonths) {
        setError("Please select a loan term")
        setIsSubmitting(false)
        return
      }

      // Prepare data for API call
      const loanApplication = {
        amount: Number(formData.loanAmount),
        purpose: formData.purpose,
        collateral: formData.collateral,
        phoneNumber: formData.phoneNumber,
        termInMonths: Number(formData.termInMonths)
      }

      console.log("Submitting loan application:", loanApplication)

      // Call the backend API
      const response = await loanApi.applyForLoan(loanApplication)

      if (response.success) {
        console.log("Loan application successful:", response.data)

        // Update form data with loan ID and application date from response
        updateFormData({
          loanId: response.data.id || `LOAN-${Math.floor(100000 + Math.random() * 900000)}`,
          applicationDate: response.data.createdAt || new Date().toISOString()
        })

        toast.success("Loan application submitted successfully")
    onSubmit()
      } else {
        setError(response.message || "Failed to submit loan application")
      }
    } catch (err: any) {
      console.error("Loan application error:", err)
      setError(err.message || "An error occurred while submitting your loan application")
      toast.error("Loan application failed", {
        description: err.message || "Please try again later"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-center mb-4">Loan Application</h2>
        <p className="text-muted-foreground text-center mb-6">Please provide the details for your loan request</p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="loanAmount">Loan Amount (E)</Label>
          <Input
            id="loanAmount"
            type="number"
            placeholder="Enter amount"
            value={formData.loanAmount}
            onChange={(e) => updateFormData({ loanAmount: e.target.value })}
            required
            min="100"
            max={availableCredit.toString()}
            disabled={isSubmitting || isLoadingCredit}
          />
          {isLoadingCredit ? (
            <p className="text-xs text-muted-foreground mt-1">Loading available credit...</p>
          ) : (
            <p className="text-xs text-muted-foreground mt-1">
              Available credit: <span className="font-semibold text-green-600">E{availableCredit.toFixed(2)}</span>
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="purpose">Purpose of Loan</Label>
          <Select
            value={formData.purpose}
            onValueChange={(value) => updateFormData({ purpose: value })}
            disabled={isSubmitting}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select purpose" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="personal">Personal Expenses</SelectItem>
              <SelectItem value="education">Education</SelectItem>
              <SelectItem value="medical">Medical Expenses</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="termInMonths">Loan Term</Label>
          <Select
            value={formData.termInMonths}
            onValueChange={(value) => updateFormData({ termInMonths: value })}
            disabled={isSubmitting}
            required
          >
            <SelectTrigger>
              <SelectValue placeholder="Select loan term" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0.5">2 weeks</SelectItem>
              <SelectItem value="1">1 month</SelectItem>
              <SelectItem value="2">2 months</SelectItem>
              <SelectItem value="3">3 months</SelectItem>
              <SelectItem value="4">4 months</SelectItem>
              <SelectItem value="5">5 months</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground mt-1">Shorter terms have lower interest rates</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="collateral">Collateral (Item Description)</Label>
          <Textarea
            id="collateral"
            placeholder="Describe the item you're providing as collateral"
            value={formData.collateral}
            onChange={(e) => updateFormData({ collateral: e.target.value })}
            required
            rows={3}
            disabled={isSubmitting}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phoneNumber">Phone Number</Label>
          <Input
            id="phoneNumber"
            type="tel"
            placeholder="Enter your phone number"
            value={formData.phoneNumber}
            onChange={(e) => updateFormData({ phoneNumber: e.target.value })}
            required
            disabled={isSubmitting}
          />
          <p className="text-xs text-muted-foreground">We'll send a verification code to this number</p>
        </div>

        <div className="pt-4 flex flex-col sm:flex-row gap-4">
          <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Continue"}
          </Button>
        </div>
      </form>
    </div>
  )
}

