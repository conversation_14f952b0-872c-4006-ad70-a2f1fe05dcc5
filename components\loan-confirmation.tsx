"use client"

import { But<PERSON> } from "@/components/ui/button"

interface LoanFormData {
  loanAmount: string
  purpose: string
  collateral: string
  phoneNumber: string
  loanId?: string
  applicationDate?: string
}

interface LoanConfirmationProps {
  formData: LoanFormData
  onConfirm: () => void
  onBack: () => void
}

export function LoanConfirmation({ formData, onConfirm, onBack }: LoanConfirmationProps) {
  // Format the application date
  const formattedDate = formData.applicationDate
    ? new Date(formData.applicationDate).toLocaleDateString()
    : new Date().toLocaleDateString()

  // Map purpose value to readable text
  const getPurposeText = (purpose: string) => {
    const purposeMap: Record<string, string> = {
      personal: "Personal Expenses",
      education: "Education",
      medical: "Medical Expenses",
      other: "Other",
    }
    return purposeMap[purpose] || purpose
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-center mb-4">Confirm Loan Details</h2>
        <p className="text-muted-foreground text-center mb-6">Please review your loan application details</p>
      </div>

      <div className="space-y-4 border rounded-lg p-6">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="text-muted-foreground">Loan ID:</div>
          <div className="font-medium">{formData.loanId}</div>

          <div className="text-muted-foreground">Application Date:</div>
          <div className="font-medium">{formattedDate}</div>

          <div className="text-muted-foreground">Loan Amount:</div>
          <div className="font-medium">E{Number.parseFloat(formData.loanAmount).toLocaleString()}</div>

          <div className="text-muted-foreground">Purpose:</div>
          <div className="font-medium">{getPurposeText(formData.purpose)}</div>

          <div className="text-muted-foreground">Phone Number:</div>
          <div className="font-medium">{formData.phoneNumber}</div>
        </div>

        <div className="pt-2">
          <div className="text-muted-foreground text-sm mb-1">Collateral:</div>
          <div className="text-sm border rounded p-3 bg-muted/30">{formData.collateral}</div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button onClick={onConfirm} className="bg-blue-600 hover:bg-blue-700">
          Confirm & Submit
        </Button>

        <Button
          variant="outline"
          className="text-red-500"
          onClick={() => {
            if (window.confirm("Are you sure you want to cancel your loan application? All progress will be lost.")) {
              window.location.href = "/dashboard"
            }
          }}
        >
          Cancel Application
        </Button>
      </div>
    </div>
  )
}

