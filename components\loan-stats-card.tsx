"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CreditCard } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

interface LoanStatsCardProps {
  isLoading?: boolean
  data?: {
    activeLoans: number
    totalLoanAmount: number
    approvalRate: number
    totalOutstandingAmount: number
  }
}

export function LoanStatsCard({ isLoading = false, data }: LoanStatsCardProps) {
  const [stats, setStats] = useState(data || {
    activeLoans: 0,
    totalLoanAmount: 0,
    approvalRate: 0,
    totalOutstandingAmount: 0
  })

  useEffect(() => {
    if (data) {
      setStats(data)
    }
  }, [data])

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-3 w-[100px]" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-[100px] mb-2" />
          <Skeleton className="h-3 w-[150px]" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Active Loans</CardTitle>
        <CardDescription>Total outstanding loans</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <CreditCard className="h-4 w-4 text-blue-600" />
          <div className="text-2xl font-bold">{stats.activeLoans}</div>
        </div>
        <div className="flex items-center mt-1">
          <p className="text-xs text-muted-foreground">
            E{stats.totalOutstandingAmount.toLocaleString('en-SZ', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} outstanding
          </p>
          <span className={`text-xs ml-2 ${stats.approvalRate >= 50 ? 'text-green-500' : 'text-amber-500'}`}>
            {stats.approvalRate.toFixed(1)}% approval
          </span>
        </div>
      </CardContent>
    </Card>
  )
}

