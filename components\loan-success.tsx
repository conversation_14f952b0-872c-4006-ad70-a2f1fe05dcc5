"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle2 } from "lucide-react"

interface LoanSuccessProps {
  onBackToDashboard: () => void
}

export function LoanSuccess({ onBackToDashboard }: LoanSuccessProps) {
  // Create a direct method to navigate to dashboard
  const navigateToDashboard = () => {
    // First try the callback
    try {
      onBackToDashboard();
    } catch (error) {
      console.error("Error navigating with callback:", error);
    }
    
    // Then use a fallback direct navigation
    setTimeout(() => {
      window.location.href = "/dashboard";
    }, 100);
  };

  return (
    <div className="space-y-6 py-6 text-center">
      <div className="flex justify-center">
        <div className="rounded-full bg-green-100 p-3">
          <CheckCircle2 className="h-16 w-16 text-green-600" />
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Application Successful!</h2>
        <p className="text-muted-foreground mb-6">
          Your loan application has been submitted successfully and is pending approval. You will receive a notification
          once your application is reviewed.
        </p>
      </div>

      <div className="border rounded-lg p-6 bg-muted/30 text-left mb-6">
        <h3 className="font-medium mb-2">What happens next?</h3>
        <ul className="space-y-2 text-sm">
          <li>• Our team will review your application</li>
          <li>• You may be contacted for additional information if needed</li>
          <li>• Once approved, funds will be disbursed to your mobile money account</li>
          <li>• You can track the status of your application in your dashboard</li>
        </ul>
      </div>

      <Button onClick={navigateToDashboard} className="bg-blue-600 hover:bg-blue-700">
        Back to Dashboard
      </Button>
    </div>
  )
}

