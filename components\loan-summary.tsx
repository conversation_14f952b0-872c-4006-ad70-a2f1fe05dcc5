import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { CalendarClock, DollarSign, PiggyBank } from "lucide-react";

interface Loan {
  id: string;
  amount: number;
  status: string;
  dueDate: string;
  interestRate: number;
  termInMonths: number;  // Backend returns termInMonths, not term
  amountPaid?: number;   // Backend returns amountPaid, not repaidAmount
  nextPaymentAmount?: number;
  nextPaymentDate?: string;
  // Legacy support for old field names
  term?: number;
  repaidAmount?: number;
}

interface LoanSummaryProps {
  loans: Loan[];
}

export function LoanSummary({ loans }: LoanSummaryProps) {

  // Calculate summary data from actual loans
  const activeLoans = loans.filter(loan =>
    ['approved', 'disbursed'].includes(loan.status?.toLowerCase())
  );

  const totalOwed = activeLoans.reduce((total, loan) => {
    // Use amountPaid from backend, fallback to repaidAmount for legacy support
    const repaidAmount = loan.amountPaid ?? loan.repaidAmount ?? 0;
    return total + (loan.amount - repaidAmount);
  }, 0);

  const nextPayment = activeLoans.sort((a, b) => {
    const dateA = a.nextPaymentDate ? new Date(a.nextPaymentDate) : new Date(a.dueDate);
    const dateB = b.nextPaymentDate ? new Date(b.nextPaymentDate) : new Date(b.dueDate);
    return dateA.getTime() - dateB.getTime();
  })[0];

  const nextPaymentDate = nextPayment?.nextPaymentDate || nextPayment?.dueDate;
  const nextPaymentAmount = nextPayment?.nextPaymentAmount || 0;

  // Calculate average interest rate
  const avgInterestRate = activeLoans.length
    ? activeLoans.reduce((sum, loan) => sum + loan.interestRate, 0) / activeLoans.length
    : 0;

  // Calculate progress
  const totalLoanAmount = activeLoans.reduce((sum, loan) => sum + loan.amount, 0);
  const totalRepaid = activeLoans.reduce((sum, loan) => {
    // Use amountPaid from backend, fallback to repaidAmount for legacy support
    return sum + (loan.amountPaid ?? loan.repaidAmount ?? 0);
  }, 0);
  const progress = totalLoanAmount > 0
    ? Math.round((totalRepaid / totalLoanAmount) * 100)
    : 0;

  return (
    <Card className="bg-white/95">
      <CardHeader>
        <CardTitle className="text-xl">Loan Summary</CardTitle>
        <CardDescription>Overview of your current loans</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-6 md:grid-cols-3">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-3 rounded-full">
              <DollarSign className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Amount Owed</p>
              <p className="text-xl font-bold">E{totalOwed.toLocaleString()}</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-3 rounded-full">
              <CalendarClock className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Next Payment Due</p>
              <p className="text-xl font-bold">
                {nextPaymentDate
                  ? new Date(nextPaymentDate).toLocaleDateString()
                  : 'No payment scheduled'}
              </p>
              {nextPaymentAmount > 0 && <p className="text-sm">E{nextPaymentAmount.toLocaleString()}</p>}
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="bg-blue-100 p-3 rounded-full">
              <PiggyBank className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Active Loans</p>
              <p className="text-xl font-bold">{activeLoans.length}</p>
              <p className="text-sm">{avgInterestRate.toFixed(1)}% Avg Interest</p>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm text-muted-foreground">Repayment Progress</span>
            <span className="text-sm font-medium">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        <div className="mt-6">
          <p className="text-sm text-muted-foreground">Loan Statuses</p>
          <ul className="list-disc list-inside">
            {activeLoans.map(loan => (
              <li key={loan.id} className="text-sm">
                Loan {loan.id.slice(-8)}: {loan.status.charAt(0).toUpperCase() + loan.status.slice(1).toLowerCase()}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

