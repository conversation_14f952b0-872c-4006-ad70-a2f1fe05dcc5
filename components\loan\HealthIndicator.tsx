"use client"

import React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON><PERSON>ipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { InfoIcon } from "lucide-react"

interface HealthIndicatorProps {
  score: number
  size?: "sm" | "md" | "lg"
  showLabel?: boolean
  className?: string
}

interface HealthScoreDetails {
  components?: {
    paymentTimeliness: number
    amountPaid: number
    timeToMaturity: number
  }
  status: string
  details: string
}

export function HealthIndicator({ 
  score, 
  size = "md", 
  showLabel = false,
  className = ""
}: HealthIndicatorProps) {
  // Determine color based on score
  const getColor = () => {
    if (score < 20) return "bg-red-500";
    if (score < 40) return "bg-orange-500";
    if (score < 60) return "bg-yellow-500";
    if (score < 80) return "bg-blue-500";
    return "bg-green-500";
  };
  
  // Determine text color based on score
  const getTextColor = () => {
    if (score < 20) return "text-red-600";
    if (score < 40) return "text-orange-600";
    if (score < 60) return "text-yellow-600";
    if (score < 80) return "text-blue-600";
    return "text-green-600";
  };
  
  // Determine status text based on score
  const getStatusText = () => {
    if (score < 20) return "Critical";
    if (score < 40) return "Poor";
    if (score < 60) return "Fair";
    if (score < 80) return "Good";
    return "Excellent";
  };
  
  // Determine size classes
  const getSizeClasses = () => {
    switch (size) {
      case "sm": return "w-8 h-8 text-xs";
      case "lg": return "w-16 h-16 text-xl";
      default: return "w-12 h-12 text-sm";
    }
  };
  
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="relative">
        {/* Circular progress indicator */}
        <div className={`rounded-full ${getSizeClasses()} flex items-center justify-center text-white font-medium ${getColor()}`}>
          {score}
        </div>
        
        {/* Progress ring (optional enhancement) */}
        <svg className={`absolute top-0 left-0 ${getSizeClasses()}`} viewBox="0 0 36 36">
          <path
            className="stroke-current opacity-25"
            fill="none"
            strokeWidth="3"
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
          />
          <path
            className={`stroke-current ${getColor()}`}
            fill="none"
            strokeWidth="3"
            strokeDasharray={`${score}, 100`}
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
          />
        </svg>
      </div>
      
      {/* Status label (optional) */}
      {showLabel && (
        <div className="flex flex-col">
          <span className={`font-medium ${getTextColor()}`}>{getStatusText()}</span>
          <span className="text-xs text-gray-500">Health Score</span>
        </div>
      )}
    </div>
  );
}

interface HealthScoreDetailsProps {
  score: number
  details: HealthScoreDetails
  className?: string
}

export function HealthScoreDetails({ 
  score, 
  details,
  className = ""
}: HealthScoreDetailsProps) {
  // Determine color based on score
  const getColor = (value: number) => {
    if (value < 20) return "bg-red-500";
    if (value < 40) return "bg-orange-500";
    if (value < 60) return "bg-yellow-500";
    if (value < 80) return "bg-blue-500";
    return "bg-green-500";
  };
  
  // Determine text color based on score
  const getTextColor = (value: number) => {
    if (value < 20) return "text-red-600";
    if (value < 40) return "text-orange-600";
    if (value < 60) return "text-yellow-600";
    if (value < 80) return "text-blue-600";
    return "text-green-600";
  };
  
  // Determine status text based on score
  const getStatusText = (value: number) => {
    if (value < 20) return "Critical";
    if (value < 40) return "Poor";
    if (value < 60) return "Fair";
    if (value < 80) return "Good";
    return "Excellent";
  };
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Overall score with explanation */}
      <div className="flex items-center gap-4">
        <HealthIndicator score={score} size="lg" />
        <div>
          <h4 className={`text-lg font-medium ${getTextColor(score)}`}>
            {getStatusText(score)} Health Score
          </h4>
          <p className="text-gray-600">{details.details}</p>
        </div>
      </div>
      
      {/* Component scores */}
      {details.components && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          {/* Payment Timeliness */}
          <div className="border rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center gap-1">
                <span className="font-medium">Payment Timeliness</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <InfoIcon className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Measures how timely your payments have been. Late payments reduce this score.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className={`font-medium ${getTextColor(details.components.paymentTimeliness)}`}>
                {details.components.paymentTimeliness}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getColor(details.components.paymentTimeliness)}`}
                style={{ width: `${details.components.paymentTimeliness}%` }}
              ></div>
            </div>
          </div>
          
          {/* Amount Paid */}
          <div className="border rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center gap-1">
                <span className="font-medium">Amount Paid</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <InfoIcon className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Percentage of the loan amount that has been repaid.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className={`font-medium ${getTextColor(details.components.amountPaid)}`}>
                {details.components.amountPaid}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getColor(details.components.amountPaid)}`}
                style={{ width: `${details.components.amountPaid}%` }}
              ></div>
            </div>
          </div>
          
          {/* Time to Maturity */}
          <div className="border rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center gap-1">
                <span className="font-medium">Time to Maturity</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <InfoIcon className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        Measures how much time is left before the loan is due. Lower scores indicate the due date is approaching.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className={`font-medium ${getTextColor(details.components.timeToMaturity)}`}>
                {details.components.timeToMaturity}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getColor(details.components.timeToMaturity)}`}
                style={{ width: `${details.components.timeToMaturity}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
