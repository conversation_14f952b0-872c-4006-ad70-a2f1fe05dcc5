"use client"

import React, { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { LoanStatus } from "@/lib/constants"

export interface LoanFiltersProps {
  onFilterChange: (filters: LoanFilters) => void;
  className?: string;
}

export interface LoanFilters {
  status?: string;
  minAmount?: number;
  maxAmount?: number;
  startDate?: string;
  endDate?: string;
  search?: string;
}

const STORAGE_KEY = 'loan_history_filters';

export function LoanFilters({ onFilterChange, className }: LoanFiltersProps) {
  const [filters, setFilters] = useState<LoanFilters>({});
  const [searchInput, setSearchInput] = useState("");
  const [minAmountInput, setMinAmountInput] = useState("");
  const [maxAmountInput, setMaxAmountInput] = useState("");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Load filters from localStorage on component mount
  useEffect(() => {
    const savedFilters = localStorage.getItem(STORAGE_KEY);
    if (savedFilters) {
      try {
        const parsedFilters = JSON.parse(savedFilters);

        // Convert date strings back to Date objects if they exist
        if (parsedFilters.startDate) {
          setStartDate(new Date(parsedFilters.startDate));
        }
        if (parsedFilters.endDate) {
          setEndDate(new Date(parsedFilters.endDate));
        }

        // Set input fields
        setSearchInput(parsedFilters.search || "");
        setMinAmountInput(parsedFilters.minAmount?.toString() || "");
        setMaxAmountInput(parsedFilters.maxAmount?.toString() || "");

        // Apply filters
        setFilters(parsedFilters);
        onFilterChange(parsedFilters);
      } catch (error) {
        console.error("Error parsing saved filters:", error);
        localStorage.removeItem(STORAGE_KEY);
      }
    }
  }, [onFilterChange]);

  // Save filters to localStorage whenever they change
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(filters));
    }
  }, [filters]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    updateFilters({ search: searchInput || undefined });
  };

  const handleStatusChange = (value: string) => {
    updateFilters({ status: value === "all" ? undefined : value });
  };

  const handleAmountFilterChange = () => {
    const minAmount = minAmountInput ? parseFloat(minAmountInput) : undefined;
    const maxAmount = maxAmountInput ? parseFloat(maxAmountInput) : undefined;

    updateFilters({ minAmount, maxAmount });
  };

  const handleDateFilterChange = () => {
    const formattedStartDate = startDate ? format(startDate, 'yyyy-MM-dd') : undefined;
    const formattedEndDate = endDate ? format(endDate, 'yyyy-MM-dd') : undefined;

    updateFilters({ startDate: formattedStartDate, endDate: formattedEndDate });
  };

  const updateFilters = (newFilters: Partial<LoanFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };

    // Remove undefined values
    Object.keys(updatedFilters).forEach(key => {
      if (updatedFilters[key as keyof LoanFilters] === undefined) {
        delete updatedFilters[key as keyof LoanFilters];
      }
    });

    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchInput("");
    setMinAmountInput("");
    setMaxAmountInput("");
    setStartDate(undefined);
    setEndDate(undefined);
    onFilterChange({});
    localStorage.removeItem(STORAGE_KEY);
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex flex-col sm:flex-row gap-2">
        {/* Search input */}
        <form onSubmit={handleSearch} className="flex-1 flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by purpose or ID..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button type="submit" variant="default">Search</Button>
        </form>

        {/* Status filter */}
        <div className="flex gap-2">
          <Select
            value={filters.status || "all"}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value={LoanStatus.PENDING}>Pending</SelectItem>
              <SelectItem value={LoanStatus.APPROVED}>Approved</SelectItem>
              <SelectItem value={LoanStatus.DISBURSED}>Disbursed</SelectItem>
              <SelectItem value={LoanStatus.PAID}>Paid</SelectItem>
              <SelectItem value={LoanStatus.OVERDUE}>Overdue</SelectItem>
              <SelectItem value={LoanStatus.REJECTED}>Rejected</SelectItem>
            </SelectContent>
          </Select>

          {/* Advanced filters button */}
          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filters</span>
                {hasActiveFilters && (
                  <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                    {Object.keys(filters).length}
                  </span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">Advanced Filters</h4>

                {/* Amount range */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">Amount Range</h5>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Min"
                      type="number"
                      value={minAmountInput}
                      onChange={(e) => setMinAmountInput(e.target.value)}
                    />
                    <Input
                      placeholder="Max"
                      type="number"
                      value={maxAmountInput}
                      onChange={(e) => setMaxAmountInput(e.target.value)}
                    />
                  </div>
                  <Button
                    onClick={handleAmountFilterChange}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    Apply Amount Filter
                  </Button>
                </div>

                {/* Date range */}
                <div className="space-y-2">
                  <h5 className="text-sm font-medium">Date Range</h5>
                  <div className="flex flex-col gap-2">
                    <div className="flex flex-col gap-1">
                      <span className="text-xs text-gray-500">Start Date</span>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "justify-start text-left font-normal",
                              !startDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {startDate ? format(startDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={startDate}
                            onSelect={setStartDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="flex flex-col gap-1">
                      <span className="text-xs text-gray-500">End Date</span>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "justify-start text-left font-normal",
                              !endDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {endDate ? format(endDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={endDate}
                            onSelect={setEndDate}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                  <Button
                    onClick={handleDateFilterChange}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    Apply Date Filter
                  </Button>
                </div>

                <div className="flex justify-between pt-2">
                  <Button
                    onClick={() => setIsFilterOpen(false)}
                    variant="outline"
                    size="sm"
                  >
                    Close
                  </Button>
                  <Button
                    onClick={clearFilters}
                    variant="destructive"
                    size="sm"
                    disabled={!hasActiveFilters}
                  >
                    Clear All
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Clear filters button */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="icon"
              onClick={clearFilters}
              className="hidden sm:flex"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Active filters display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 text-sm">
          <span className="text-gray-500">Active filters:</span>
          {filters.status && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              Status: {filters.status.charAt(0).toUpperCase() + filters.status.slice(1)}
            </span>
          )}
          {(filters.minAmount || filters.maxAmount) && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              Amount: {filters.minAmount ? `E${filters.minAmount}` : 'E0'} - {filters.maxAmount ? `E${filters.maxAmount}` : 'Any'}
            </span>
          )}
          {(filters.startDate || filters.endDate) && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              Date: {filters.startDate ? filters.startDate : 'Any'} - {filters.endDate ? filters.endDate : 'Any'}
            </span>
          )}
          {filters.search && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              Search: "{filters.search}"
            </span>
          )}
        </div>
      )}
    </div>
  );
}
