"use client"

import React, { useState, useEffect } from "react"
import { Loader2 } from "lucide-react"
import { loanApi } from "@/lib/api"

interface PaymentEvent {
  date: string
  amount: number
  type: 'disbursement' | 'repayment'
  status: 'completed' | 'upcoming' | 'overdue'
  transactionId: string | null
}

interface PaymentTimelineProps {
  loanId: string
}

export function PaymentTimeline({ loanId }: PaymentTimelineProps) {
  const [schedule, setSchedule] = useState<PaymentEvent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setIsLoading(true)
        setError(null)
        const response = await loanApi.getLoanPaymentSchedule(loanId)
        if (response.success) {
          setSchedule(response.data || [])
        } else {
          setError(response.message || "Failed to load payment schedule")
        }
      } catch (error) {
        console.error("Error fetching payment schedule:", error)
        setError("An error occurred while loading the payment schedule")
      } finally {
        setIsLoading(false)
      }
    }

    if (loanId) {
      fetchSchedule()
    }
  }, [loanId])

  const formatDate = (dateString: string) => {
    try {
      // Parse the date
      const date = new Date(dateString)

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return "Invalid date"
      }

      // Format as DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0')
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // Months are 0-indexed
      const year = date.getFullYear()

      return `${day}/${month}/${year}`
    } catch (e) {
      return "Invalid date"
    }
  }

  const formatCurrency = (amount: number) => {
    return `E${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-4">
        <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
        <span className="ml-2 text-sm text-blue-500">Loading payment schedule...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-4 text-red-500 text-sm">
        <p>{error}</p>
      </div>
    )
  }

  if (schedule.length === 0) {
    return (
      <div className="text-center py-4 text-gray-500 text-sm">
        <p>No payment schedule available for this loan.</p>
      </div>
    )
  }

  // Determine if we need to use a more compact layout for many events
  const useCompactLayout = schedule.length > 3;

  return (
    <div className="mt-4">
      <h4 className="font-medium mb-3 text-blue-900">Payment Timeline</h4>

      {/* Desktop and tablet view */}
      <div className="relative hidden sm:block">
        {/* Timeline line */}
        <div className="absolute top-4 left-0 right-0 h-0.5 bg-gray-200" />

        {/* Timeline events */}
        <div className="relative flex justify-between">
          {schedule.map((event, index) => (
            <div key={index} className="flex flex-col items-center">
              {/* Event dot */}
              <div
                className={`w-4 h-4 rounded-full z-10 ${
                  event.status === 'completed' ? 'bg-green-500' :
                  event.status === 'overdue' ? 'bg-red-500' : 'bg-blue-500'
                }`}
              />

              {/* Event details */}
              <div className="mt-2 text-xs text-center max-w-[80px]">
                <div className="font-medium">{formatDate(event.date)}</div>
                <div>{formatCurrency(event.amount)}</div>
                <div className="capitalize text-gray-500">{event.type}</div>
                <div className={`text-xs mt-1 font-medium ${
                  event.status === 'completed' ? 'text-green-600' :
                  event.status === 'overdue' ? 'text-red-600' : 'text-blue-600'
                }`}>
                  {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Mobile view - vertical timeline */}
      <div className="sm:hidden">
        <div className="relative">
          {/* Vertical timeline line */}
          <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-gray-200" />

          {/* Timeline events */}
          <div className="space-y-6">
            {schedule.map((event, index) => (
              <div key={index} className="relative flex items-start ml-4 pl-4">
                {/* Event dot */}
                <div
                  className={`absolute -left-2 top-0 w-4 h-4 rounded-full z-10 ${
                    event.status === 'completed' ? 'bg-green-500' :
                    event.status === 'overdue' ? 'bg-red-500' : 'bg-blue-500'
                  }`}
                />

                {/* Event details */}
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{formatDate(event.date)}</div>
                      <div className="capitalize text-gray-500 text-xs">{event.type}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(event.amount)}</div>
                      <div className={`text-xs font-medium ${
                        event.status === 'completed' ? 'text-green-600' :
                        event.status === 'overdue' ? 'text-red-600' : 'text-blue-600'
                      }`}>
                        {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
