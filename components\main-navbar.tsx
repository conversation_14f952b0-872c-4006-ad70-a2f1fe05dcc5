"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CreditCard, Menu, X } from "lucide-react"
import { cn } from "@/lib/utils"

export function MainNavbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  return (
    <header className="w-full bg-blue-950/90 border-b sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2">
            <div className="bg-blue-600 text-white p-1.5 rounded">
              <CreditCard className="h-6 w-6" />
            </div>
            <span className="text-xl font-bold text-blue-900"><PERSON><PERSON><PERSON><PERSON></span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className="text-white hover:text-blue-200 font-medium border border-white/30 rounded-md px-3 py-1.5 transition-colors hover:border-blue-200"
            >
              Home
            </Link>
            <Link
              href="/about"
              className="text-white hover:text-blue-200 font-medium border border-white/30 rounded-md px-3 py-1.5 transition-colors hover:border-blue-200"
            >
              About Us
            </Link>
            <Link
              href="/service"
              className="text-white hover:text-blue-200 font-medium border border-white/30 rounded-md px-3 py-1.5 transition-colors hover:border-blue-200"
            >
              Services
            </Link>
            <Link
              href="/how-it-works"
              className="text-white hover:text-blue-200 font-medium border border-white/30 rounded-md px-3 py-1.5 transition-colors hover:border-blue-200"
            >
              How It Works
            </Link>
            <Button asChild className="bg-blue-600 hover:bg-blue-700 ml-4">
              <Link href="/login">Sign In</Link>
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <button className="md:hidden p-2 rounded-md text-white" onClick={toggleMobileMenu} aria-label="Toggle menu">
            {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className={cn("md:hidden bg-blue-950/90 border-b", mobileMenuOpen ? "block" : "hidden")}>
        <div className="container mx-auto px-4 py-4 space-y-4">
          <Link
            href="/"
            className="block text-white hover:text-blue-200 font-medium py-2 px-3 border border-white/30 rounded-md mb-2 transition-colors hover:border-blue-200"
            onClick={() => setMobileMenuOpen(false)}
          >
            Home
          </Link>
          <Link
            href="/about"
            className="block text-white hover:text-blue-200 font-medium py-2 px-3 border border-white/30 rounded-md mb-2 transition-colors hover:border-blue-200"
            onClick={() => setMobileMenuOpen(false)}
          >
            About Us
          </Link>
          <Link
            href="/service"
            className="block text-white hover:text-blue-200 font-medium py-2 px-3 border border-white/30 rounded-md mb-2 transition-colors hover:border-blue-200"
            onClick={() => setMobileMenuOpen(false)}
          >
            Services
          </Link>
          <Link
            href="/how-it-works"
            className="block text-white hover:text-blue-200 font-medium py-2 px-3 border border-white/30 rounded-md mb-2 transition-colors hover:border-blue-200"
            onClick={() => setMobileMenuOpen(false)}
          >
            How It Works
          </Link>
          <Button asChild className="w-full bg-blue-600 hover:bg-blue-700 mt-4">
            <Link href="/login" onClick={() => setMobileMenuOpen(false)}>
              Sign In
            </Link>
          </Button>
        </div>
      </div>
    </header>
  )
}

