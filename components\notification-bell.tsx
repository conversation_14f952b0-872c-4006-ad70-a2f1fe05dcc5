"use client"

import { useState, useEffect } from "react"
import { Bell, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import { notificationApi } from "@/lib/api"
import { toast } from "sonner"
import { formatRelativeTime } from "@/lib/utils/date"

type Notification = {
  id: string
  title: string
  message: string
  type: string
  status: string
  isRead: boolean
  createdAt: string
  updatedAt: string
}

export function NotificationBell() {
  const router = useRouter()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isOpen, setIsOpen] = useState(false)

  // Fetch notifications when dropdown is opened
  const fetchNotifications = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await notificationApi.getUserNotifications(1, 5) // Get first 5 notifications

      if (response.success) {
        setNotifications(response.data)
      } else {
        setError("Failed to fetch notifications")
        console.error("Failed to fetch notifications:", response.message)
      }
    } catch (err) {
      console.error("Error fetching notifications:", err)
      setError(err instanceof Error ? err.message : "An error occurred while fetching notifications")
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch notifications when dropdown is opened
  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
  }, [isOpen])

  // Fetch unread count on initial load
  useEffect(() => {
    const fetchUnreadCount = async () => {
      try {
        const response = await notificationApi.getUserNotifications(1, 1, false) // Get unread notifications
        if (response.success && response.pagination) {
          // We only need the count, not the actual notifications
          const count = response.pagination.total
          if (count > 0) {
            // If there are unread notifications, update the state with just the count info
            // This avoids having to load all notifications just to show the badge
            setNotifications(prev => prev.length > 0 ? prev : Array(count).fill({ isRead: false } as any))
          }
        }
      } catch (error) {
        console.error("Error fetching unread count:", error)
      }
    }

    fetchUnreadCount()
  }, [])

  const unreadCount = notifications.filter((n) => !n.isRead).length

  const markAsRead = async (id: string) => {
    try {
      const response = await notificationApi.markAsRead(id)
      if (response.success) {
        // Update the local state
        setNotifications(notifications.map((n) => (n.id === id ? { ...n, isRead: true } : n)))
      } else {
        toast.error("Failed to mark notification as read")
      }
    } catch (error) {
      console.error("Error marking notification as read:", error)
      toast.error("Error marking notification as read")
    }
  }

  const markAllAsRead = async () => {
    try {
      const response = await notificationApi.markAllAsRead()
      if (response.success) {
        // Update the local state
        setNotifications(notifications.map((n) => ({ ...n, isRead: true })))
        toast.success("All notifications marked as read")
      } else {
        toast.error("Failed to mark all notifications as read")
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      toast.error("Error marking all notifications as read")
    }
  }

  // Format the date using our utility function
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) {
      return 'Unknown time';
    }
    return formatRelativeTime(dateString);
  };

  return (
    <DropdownMenu onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-red-500">
              {unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end" forceMount>
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-auto text-xs text-blue-600 hover:text-blue-800 p-0"
              onClick={markAllAsRead}
              disabled={isLoading}
            >
              Mark all as read
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {isLoading ? (
          <div className="text-center py-4">
            <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading notifications...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500 text-sm">
            <p>{error}</p>
            <Button
              variant="ghost"
              size="sm"
              className="mt-2 text-xs"
              onClick={fetchNotifications}
            >
              Try Again
            </Button>
          </div>
        ) : notifications.length > 0 ? (
          <DropdownMenuGroup className="max-h-[300px] overflow-auto">
            {notifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={`flex flex-col items-start py-2 px-4 ${!notification.isRead ? "bg-blue-50" : ""}`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="flex w-full justify-between">
                  <span className="font-medium text-sm">
                    {notification.title}
                  </span>
                  <span className="text-xs text-muted-foreground">{formatDate(notification.createdAt)}</span>
                </div>
                <p className="text-sm mt-1">{notification.message}</p>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        ) : (
          <div className="text-center py-4 text-muted-foreground">No notifications</div>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem className="justify-center text-blue-600" onClick={() => router.push("/notifications")}>
          View all notifications
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

