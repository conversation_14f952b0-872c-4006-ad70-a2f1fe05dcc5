"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, RefreshCw } from "lucide-react"
import { otpApi } from "@/lib/api"
import { toast } from "sonner"

interface OtpVerificationProps {
  phoneNumber: string
  onVerify: () => void
}

export function OtpVerification({ phoneNumber, onVerify }: OtpVerificationProps) {
  // Add a ref to track whether OTP has been sent
  const otpSentRef = useRef(false)

  const [otp, setOtp] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [isVerifying, setIsVerifying] = useState(false)
  const [timeLeft, setTimeLeft] = useState(60)
  const [canResend, setCanResend] = useState(false)

  // Send verification code when component mounts
  useEffect(() => {
    const sendInitialOtp = async () => {
      // Only send OTP if it hasn't been sent yet
      if (otpSentRef.current) {
        console.log("OTP already sent, skipping duplicate send");
        return;
      }

      // Mark as sent before the API call to prevent race conditions
      otpSentRef.current = true;

      try {
        // Format the phone number if needed
        let formattedPhoneNumber = phoneNumber;

        // If the phone number doesn't start with +, add the country code
        if (!phoneNumber.startsWith('+')) {
          // Assuming Eswatini country code (+268)
          formattedPhoneNumber = `+268${phoneNumber.replace(/^0+/, '')}`;
        }

        console.log("Sending OTP to", formattedPhoneNumber);
        const response = await otpApi.sendOtp(formattedPhoneNumber);

        if (!response.success) {
          setError(response.message || "Failed to send verification code");
          setCanResend(true);
          setTimeLeft(0);
          // Reset the ref if there's an error so we can try again
          otpSentRef.current = false;
        } else {
          toast.success("Verification code sent successfully via SMS");
        }
      } catch (error) {
        console.error("Error sending initial verification code:", error);
        setError("Failed to send verification code. Please try again.");
        setCanResend(true);
        setTimeLeft(0);
        // Reset the ref if there's an error so we can try again
        otpSentRef.current = false;
      }
    };

    sendInitialOtp();
  }, [phoneNumber]);

  // Countdown timer for resend
  useEffect(() => {
    // Start countdown timer
    if (timeLeft > 0 && !canResend) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (timeLeft === 0 && !canResend) {
      setCanResend(true)
    }
  }, [timeLeft, canResend])

  const handleVerify = async () => {
    if (otp.length < 4) {
      setError("Please enter a valid verification code")
      return
    }

    setError(null)
    setIsVerifying(true)

    try {
      // Format the phone number if needed
      let formattedPhoneNumber = phoneNumber;

      // If the phone number doesn't start with +, add the country code
      if (!phoneNumber.startsWith('+')) {
        // Assuming Eswatini country code (+268)
        formattedPhoneNumber = `+268${phoneNumber.replace(/^0+/, '')}`;
      }

      const response = await otpApi.verifyOtp(formattedPhoneNumber, otp);

      if (response.success) {
        toast.success("Phone number verified successfully");
        onVerify();
      } else {
        setError(response.message || "Invalid verification code. Please try again.");
      }
    } catch (err) {
      console.error("Error verifying code:", err);
      setError("Verification failed. Please try again.");
    } finally {
      setIsVerifying(false);
    }
  }

  const handleResendOtp = async () => {
    setCanResend(false)
    setTimeLeft(60)
    setError(null)

    // Reset the ref to allow resending
    otpSentRef.current = false;

    try {
      // Format the phone number if needed
      let formattedPhoneNumber = phoneNumber;

      // If the phone number doesn't start with +, add the country code
      if (!phoneNumber.startsWith('+')) {
        // Assuming Eswatini country code (+268)
        formattedPhoneNumber = `+268${phoneNumber.replace(/^0+/, '')}`;
      }

      // Mark as sent before the API call
      otpSentRef.current = true;

      const response = await otpApi.resendOtp(formattedPhoneNumber);

      if (response.success) {
        toast.success("Verification code resent successfully via SMS");
      } else {
        setError(response.message || "Failed to resend verification code");
        setCanResend(true);
        setTimeLeft(0);
        // Reset the ref if there's an error
        otpSentRef.current = false;
      }
    } catch (err) {
      console.error("Error resending verification code:", err);
      setError("Failed to resend verification code. Please try again.");
      setCanResend(true);
      setTimeLeft(0);
      // Reset the ref if there's an error
      otpSentRef.current = false;
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-center mb-4">Verification</h2>
        <p className="text-muted-foreground text-center mb-6">
          We've sent a verification code to {phoneNumber} via SMS
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <div className="space-y-2">
          <Input
            type="text"
            placeholder="Enter verification code"
            value={otp}
            onChange={(e) => setOtp(e.target.value.replace(/[^0-9]/g, ""))}
            maxLength={6}
            className="text-center text-lg py-6"
          />
        </div>

        <div className="text-center">
          {canResend ? (
            <Button variant="outline" onClick={handleResendOtp} className="w-full">
              Resend verification code
            </Button>
          ) : (
            <div className="flex flex-col items-center gap-1">
              <p className="text-sm text-muted-foreground">Resend code in {timeLeft} seconds</p>
              <Button variant="outline" disabled className="w-full opacity-50">
                Resend verification code
              </Button>
            </div>
          )}
        </div>

        <Button
          onClick={handleVerify}
          disabled={isVerifying || otp.length < 4}
          className="w-full bg-blue-600 hover:bg-blue-700"
        >
          {isVerifying ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Verifying...
            </>
          ) : (
            "Verify & Continue"
          )}
        </Button>
      </div>
    </div>
  )
}

