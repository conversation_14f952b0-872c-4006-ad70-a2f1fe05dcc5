"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"

interface OverviewChartProps {
  className?: string
  isLoading?: boolean
}

export function OverviewChart({ className, isLoading = false }: OverviewChartProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-5 w-[180px] mb-2" />
          <Skeleton className="h-4 w-[250px]" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Overview</CardTitle>
        <CardDescription>Loan disbursements and repayments</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="disbursements">
          <TabsList className="mb-4">
            <TabsTrigger value="disbursements">Disbursements</TabsTrigger>
            <TabsTrigger value="repayments">Repayments</TabsTrigger>
            <TabsTrigger value="combined">Combined</TabsTrigger>
          </TabsList>
          <TabsContent value="disbursements">
            {mounted ? (
              <div className="h-[300px] w-full bg-gradient-to-r from-blue-50 to-blue-100 rounded-md flex items-center justify-center">
                <p className="text-blue-500">Disbursements Chart (Placeholder)</p>
              </div>
            ) : null}
          </TabsContent>
          <TabsContent value="repayments">
            {mounted ? (
              <div className="h-[300px] w-full bg-gradient-to-r from-green-50 to-green-100 rounded-md flex items-center justify-center">
                <p className="text-green-500">Repayments Chart (Placeholder)</p>
              </div>
            ) : null}
          </TabsContent>
          <TabsContent value="combined">
            {mounted ? (
              <div className="h-[300px] w-full bg-gradient-to-r from-purple-50 to-purple-100 rounded-md flex items-center justify-center">
                <p className="text-purple-500">Combined Chart (Placeholder)</p>
              </div>
            ) : null}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

