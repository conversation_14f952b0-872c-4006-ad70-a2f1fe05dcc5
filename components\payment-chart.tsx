"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useEffect, useState } from "react"

export function PaymentChart() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment Analytics</CardTitle>
        <CardDescription>Transaction volume and trends</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="weekly">
          <TabsList className="mb-4">
            <TabsTrigger value="weekly">Weekly</TabsTrigger>
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="yearly">Yearly</TabsTrigger>
          </TabsList>
          <TabsContent value="weekly">
            {mounted ? (
              <div className="h-[300px] w-full bg-gradient-to-r from-blue-50 to-green-50 rounded-md flex items-center justify-center">
                <div className="text-center">
                  <p className="text-blue-500 font-medium">Weekly Payment Trends</p>
                  <p className="text-sm text-gray-500">Showing data for March 10-16, 2025</p>
                </div>
              </div>
            ) : null}
          </TabsContent>
          <TabsContent value="monthly">
            {mounted ? (
              <div className="h-[300px] w-full bg-gradient-to-r from-purple-50 to-blue-50 rounded-md flex items-center justify-center">
                <div className="text-center">
                  <p className="text-purple-500 font-medium">Monthly Payment Trends</p>
                  <p className="text-sm text-gray-500">Showing data for March 2025</p>
                </div>
              </div>
            ) : null}
          </TabsContent>
          <TabsContent value="yearly">
            {mounted ? (
              <div className="h-[300px] w-full bg-gradient-to-r from-amber-50 to-red-50 rounded-md flex items-center justify-center">
                <div className="text-center">
                  <p className="text-amber-500 font-medium">Yearly Payment Trends</p>
                  <p className="text-sm text-gray-500">Showing data for 2025</p>
                </div>
              </div>
            ) : null}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

