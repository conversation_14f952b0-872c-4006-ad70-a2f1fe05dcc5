"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AlertCircle, Upload, User } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/lib/auth-context"
import { toast } from "sonner"

interface ProfileInfoProps {
  onSuccess: (message: string) => void
}

export function ProfileInfo({ onSuccess }: ProfileInfoProps) {
  const { user: authUser, updateProfile, uploadProfileImage } = useAuth()

  // Initialize form data with user data from auth context
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phoneNumber: "",
    profileImage: "/placeholder.svg?height=200&width=200",
  })

  // Update form data when auth user changes
  useEffect(() => {
    if (authUser) {
      setFormData({
        fullName: authUser.name || "",
        email: authUser.email || "",
        phoneNumber: authUser.phoneNumber || "",
        profileImage: authUser.profileImage || "/placeholder.svg?height=200&width=200",
      })
    }
  }, [authUser])

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[name]
        return newErrors
      })
    }
  }

  const handleImageClick = () => {
    fileInputRef.current?.click()
  }

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      try {
        // Show a temporary local preview
        const imageUrl = URL.createObjectURL(file)
        setFormData((prev) => ({ ...prev, profileImage: imageUrl }))

        // For now, let's skip the actual upload and just show a success message
        // This is a temporary workaround until the server-side issue is fixed
        toast.success("Profile image updated locally. Server upload will be implemented soon.");

        // Uncomment this when the server-side issue is fixed
        // await uploadProfileImage(file);
      } catch (error) {
        console.error('Error handling profile image:', error);
        toast.error("Failed to update profile image. Please try again later.");
      }
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.fullName.trim()) {
      newErrors.fullName = "Full name is required"
    }



    if (!formData.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid"
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare the profile data
      const profileData = {
        name: formData.fullName,
        email: formData.email,
        phoneNumber: formData.phoneNumber,
      };

      // Update the profile
      await updateProfile(profileData);

      // Show success message
      onSuccess("Profile updated successfully")
    } catch (error) {
      setErrors({ form: "Failed to update profile. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {errors.form && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errors.form}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col items-center space-y-4 mb-6">
        <div className="relative cursor-pointer group" onClick={handleImageClick}>
          <Avatar className="h-32 w-32">
            <AvatarImage src={formData.profileImage} alt="Profile" />
            <AvatarFallback className="text-4xl">
              <User size={48} />
            </AvatarFallback>
          </Avatar>
          <div className="absolute inset-0 bg-black/30 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <Upload className="h-8 w-8 text-white" />
          </div>
        </div>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept="image/*"
          onChange={handleImageChange}
          aria-label="Upload profile picture"
        />
        <p className="text-sm text-muted-foreground">Click to upload a new profile picture</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="fullName">Full Name</Label>
          <Input
            id="fullName"
            name="fullName"
            value={formData.fullName}
            onChange={handleChange}
            placeholder="Enter your full name"
            className="w-full"
          />
          {errors.fullName && <p className="text-sm text-red-500">{errors.fullName}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Enter your email"
            className="w-full"
          />
          {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
        </div>

        <div className="space-y-2 sm:col-span-2">
          <Label htmlFor="phoneNumber">Phone Number</Label>
          <Input
            id="phoneNumber"
            name="phoneNumber"
            value={formData.phoneNumber}
            onChange={handleChange}
            placeholder="Enter your phone number"
            className="w-full"
          />
          {errors.phoneNumber && <p className="text-sm text-red-500">{errors.phoneNumber}</p>}
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" className="bg-blue-600 hover:bg-blue-700" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </form>
  )
}

