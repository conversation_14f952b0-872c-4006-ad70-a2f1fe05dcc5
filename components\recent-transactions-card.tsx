"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowDownRight, ArrowUpRight, MoreHorizontal } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"

interface Transaction {
  id: string
  type: "deposit" | "withdrawal" | "loan_disbursement" | "loan_repayment"
  amount: number
  createdAt: string
  description: string
  status: "completed" | "pending" | "failed"
  userName: string
}

interface RecentTransactionsCardProps {
  className?: string
  isLoading?: boolean
  data?: Transaction[]
}

export function RecentTransactionsCard({ className, isLoading = false, data }: RecentTransactionsCardProps) {
  const [transactions, setTransactions] = useState<Transaction[]>(data || [])

  useEffect(() => {
    if (data) {
      setTransactions(data)
    }
  }, [data])

  // Helper function to determine transaction icon and color
  const getTransactionDisplay = (type: string) => {
    switch (type) {
      case "deposit":
      case "loan_repayment":
        return {
          icon: <ArrowUpRight className="h-4 w-4 text-green-600" strokeWidth={2.5} />,
          bgColor: "bg-green-100",
          textColor: "text-green-600",
          prefix: "+"
        }
      case "withdrawal":
      case "loan_disbursement":
        return {
          icon: <ArrowDownRight className="h-4 w-4 text-red-600" strokeWidth={2.5} />,
          bgColor: "bg-red-100",
          textColor: "text-red-600",
          prefix: "-"
        }
      default:
        return {
          icon: <ArrowUpRight className="h-4 w-4 text-blue-600" strokeWidth={2.5} />,
          bgColor: "bg-blue-100",
          textColor: "text-blue-600",
          prefix: ""
        }
    }
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-5 w-[180px] mb-2" />
          <Skeleton className="h-4 w-[250px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-5">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex justify-between items-center">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-[120px]" />
                  <Skeleton className="h-3 w-[80px]" />
                </div>
                <Skeleton className="h-4 w-[80px]" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>Latest mobile money transactions</CardDescription>
        </div>
        <Button variant="ghost" size="icon">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {transactions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">No recent transactions</p>
            </div>
          ) : (
            transactions.map((transaction) => {
              const display = getTransactionDisplay(transaction.type)
              return (
                <div key={transaction.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`p-2 rounded-full ${display.bgColor}`}>
                      {display.icon}
                    </div>
                    <div>
                      <p className="text-sm font-medium">{transaction.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(transaction.createdAt).toLocaleString()} • {transaction.userName}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <p className={`text-sm font-medium ${display.textColor}`}>
                      {display.prefix}E{transaction.amount.toLocaleString('en-SZ', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}
                    </p>
                    <span
                      className={`text-xs ${
                        transaction.status === "completed"
                          ? "text-green-500"
                          : transaction.status === "pending"
                            ? "text-amber-500"
                            : "text-red-500"
                      }`}
                    >
                      {transaction.status}
                    </span>
                  </div>
                </div>
              )
            })
          )}
        </div>
      </CardContent>
    </Card>
  )
}

