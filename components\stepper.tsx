import { cn } from "@/lib/utils"
import { CheckCircle2 } from "lucide-react"

interface StepperProps {
  steps: string[]
  currentStep: number
  className?: string
}

export function Stepper({ steps, currentStep, className }: StepperProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={index} className="flex flex-col items-center">
            <div className="relative flex items-center justify-center">
              {index < currentStep ? (
                <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                  <CheckCircle2 className="h-5 w-5 text-white" />
                </div>
              ) : (
                <div
                  className={cn(
                    "h-8 w-8 rounded-full flex items-center justify-center text-sm font-medium",
                    index === currentStep ? "bg-blue-600 text-white" : "bg-blue-100 text-blue-900",
                  )}
                >
                  {index + 1}
                </div>
              )}

              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "absolute top-4 w-full h-0.5 -right-1/2",
                    index < currentStep ? "bg-blue-600" : "bg-blue-100",
                  )}
                />
              )}
            </div>
            <span
              className={cn(
                "mt-2 text-xs font-medium",
                index <= currentStep ? "text-blue-900" : "text-muted-foreground",
              )}
            >
              {step}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}

