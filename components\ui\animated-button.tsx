"use client"
import React from "react"
import { cn } from "@/lib/utils"
import { Button, type ButtonProps } from "@/components/ui/button"

export interface AnimatedButtonProps extends ButtonProps {
  children: React.ReactNode
  className?: string
  variant?: "default" | "gradient" | "outline"
}

export const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ children, className, variant = "default", ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn(
          "relative group/btn",
          variant === "gradient" &&
            "bg-gradient-to-br from-blue-600 to-blue-800 text-white shadow-[0px_1px_0px_0px_#ffffff40_inset,0px_-1px_0px_0px_#ffffff40_inset]",
          className,
        )}
        {...props}
      >
        {children}
        <BottomGradient />
      </Button>
    )
  },
)
AnimatedButton.displayName = "AnimatedButton"

const BottomGradient = () => {
  return (
    <>
      <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-cyan-500 to-transparent" />
      <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-indigo-500 to-transparent" />
    </>
  )
}

export const LabelInputContainer = ({
  children,
  className,
}: {
  children: React.ReactNode
  className?: string
}) => {
  return <div className={cn("flex flex-col space-y-2 w-full", className)}>{children}</div>
}

