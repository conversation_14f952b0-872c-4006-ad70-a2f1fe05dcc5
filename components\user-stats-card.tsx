"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Users } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

interface UserStatsCardProps {
  isLoading?: boolean
  data?: {
    totalUsers: number
    newUsersThisMonth: number
    userGrowthRate: number
    activeUsers: number
  }
}

export function UserStatsCard({ isLoading = false, data }: UserStatsCardProps) {
  // Use provided data or default values
  const stats = data || {
    totalUsers: 0,
    newUsersThisMonth: 0,
    userGrowthRate: 0,
    activeUsers: 0
  }

  console.log('UserStatsCard rendering with:', { isLoading, data, stats })

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-3 w-[100px]" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-[100px] mb-2" />
          <Skeleton className="h-3 w-[150px]" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Total Users</CardTitle>
        <CardDescription>Active account holders</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <Users className="h-4 w-4 text-blue-600" />
          <div className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</div>
        </div>
        <div className="flex items-center mt-1">
          <p className="text-xs text-muted-foreground">
            {stats.newUsersThisMonth} new users this month
          </p>
          <span className={`text-xs ml-2 ${stats.userGrowthRate >= 0 ? 'text-green-500' : 'text-red-500'}`}>
            {stats.userGrowthRate >= 0 ? '+' : ''}{stats.userGrowthRate.toFixed(1)}%
          </span>
        </div>
      </CardContent>
    </Card>
  )
}

