--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

-- Started on 2025-06-24 00:18:31

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 5014 (class 1262 OID 16388)
-- Name: loan_app; Type: DATABASE; Schema: -; Owner: postgres
--

CREATE DATABASE loan_app WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en-SZ';


ALTER DATABASE loan_app OWNER TO postgres;

\connect loan_app

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 2 (class 3079 OID 16389)
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- TOC entry 5015 (class 0 OID 0)
-- Dependencies: 2
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- TOC entry 911 (class 1247 OID 16868)
-- Name: documents_document_status_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.documents_document_status_enum AS ENUM (
    'pending',
    'approved',
    'rejected',
    'expired'
);


ALTER TYPE public.documents_document_status_enum OWNER TO postgres;

--
-- TOC entry 908 (class 1247 OID 16851)
-- Name: documents_document_type_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.documents_document_type_enum AS ENUM (
    'id',
    'collateral',
    'contract',
    'statement',
    'other'
);


ALTER TYPE public.documents_document_type_enum OWNER TO postgres;

--
-- TOC entry 872 (class 1247 OID 16514)
-- Name: loans_status_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.loans_status_enum AS ENUM (
    'pending',
    'approved',
    'rejected',
    'disbursed',
    'paid',
    'overdue'
);


ALTER TYPE public.loans_status_enum OWNER TO postgres;

--
-- TOC entry 926 (class 1247 OID 16965)
-- Name: mobile_money_provider; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.mobile_money_provider AS ENUM (
    'MTN',
    'SWAZIMOBILE',
    'INSTACASH'
);


ALTER TYPE public.mobile_money_provider OWNER TO postgres;

--
-- TOC entry 932 (class 1247 OID 16980)
-- Name: mobile_money_transaction_status; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.mobile_money_transaction_status AS ENUM (
    'PENDING',
    'SUCCESSFUL',
    'FAILED',
    'CANCELLED',
    'TIMEOUT'
);


ALTER TYPE public.mobile_money_transaction_status OWNER TO postgres;

--
-- TOC entry 929 (class 1247 OID 16972)
-- Name: mobile_money_transaction_type; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.mobile_money_transaction_type AS ENUM (
    'DISBURSEMENT',
    'COLLECTION',
    'BALANCE_INQUIRY'
);


ALTER TYPE public.mobile_money_transaction_type OWNER TO postgres;

--
-- TOC entry 914 (class 1247 OID 16905)
-- Name: mobile_money_transactions_provider_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.mobile_money_transactions_provider_enum AS ENUM (
    'MTN',
    'AIRTEL',
    'VODACOM'
);


ALTER TYPE public.mobile_money_transactions_provider_enum OWNER TO postgres;

--
-- TOC entry 920 (class 1247 OID 16920)
-- Name: mobile_money_transactions_status_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.mobile_money_transactions_status_enum AS ENUM (
    'PENDING',
    'SUCCESSFUL',
    'FAILED',
    'CANCELLED',
    'TIMEOUT'
);


ALTER TYPE public.mobile_money_transactions_status_enum OWNER TO postgres;

--
-- TOC entry 917 (class 1247 OID 16912)
-- Name: mobile_money_transactions_type_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.mobile_money_transactions_type_enum AS ENUM (
    'DISBURSEMENT',
    'COLLECTION',
    'BALANCE_INQUIRY'
);


ALTER TYPE public.mobile_money_transactions_type_enum OWNER TO postgres;

--
-- TOC entry 893 (class 1247 OID 16725)
-- Name: notifications_status_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.notifications_status_enum AS ENUM (
    'sent',
    'scheduled',
    'draft'
);


ALTER TYPE public.notifications_status_enum OWNER TO postgres;

--
-- TOC entry 890 (class 1247 OID 16706)
-- Name: notifications_type_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.notifications_type_enum AS ENUM (
    'system',
    'payment',
    'loan',
    'user',
    'alert'
);


ALTER TYPE public.notifications_type_enum OWNER TO postgres;

--
-- TOC entry 899 (class 1247 OID 16758)
-- Name: otps_purpose_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.otps_purpose_enum AS ENUM (
    'phone_verification',
    'password_reset',
    'login_verification'
);


ALTER TYPE public.otps_purpose_enum OWNER TO postgres;

--
-- TOC entry 896 (class 1247 OID 16750)
-- Name: otps_status_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.otps_status_enum AS ENUM (
    'pending',
    'verified',
    'expired'
);


ALTER TYPE public.otps_status_enum OWNER TO postgres;

--
-- TOC entry 878 (class 1247 OID 16550)
-- Name: transactions_status_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.transactions_status_enum AS ENUM (
    'pending',
    'completed',
    'failed',
    'cancelled'
);


ALTER TYPE public.transactions_status_enum OWNER TO postgres;

--
-- TOC entry 875 (class 1247 OID 16535)
-- Name: transactions_type_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.transactions_type_enum AS ENUM (
    'deposit',
    'withdrawal',
    'loan_disbursement',
    'loan_repayment'
);


ALTER TYPE public.transactions_type_enum OWNER TO postgres;

--
-- TOC entry 881 (class 1247 OID 16567)
-- Name: users_role_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.users_role_enum AS ENUM (
    'customer',
    'admin',
    'staff'
);


ALTER TYPE public.users_role_enum OWNER TO postgres;

--
-- TOC entry 884 (class 1247 OID 16583)
-- Name: users_status_enum; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.users_status_enum AS ENUM (
    'active',
    'inactive',
    'pending',
    'suspended'
);


ALTER TYPE public.users_status_enum OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 223 (class 1259 OID 16813)
-- Name: documents; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.documents (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    loan_id uuid,
    document_type public.documents_document_type_enum NOT NULL,
    file_data bytea,
    file_size integer NOT NULL,
    document_status public.documents_document_status_enum DEFAULT 'pending'::public.documents_document_status_enum NOT NULL,
    status_updated_at timestamp without time zone,
    status_updated_by uuid,
    uploaded_by uuid NOT NULL,
    upload_date timestamp without time zone DEFAULT now() NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    file_name character varying NOT NULL,
    original_file_name character varying NOT NULL,
    file_path character varying,
    mime_type character varying NOT NULL,
    rejection_reason character varying
);


ALTER TABLE public.documents OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 16480)
-- Name: loans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.loans (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    amount numeric(10,2) NOT NULL,
    "interestRate" numeric(5,2) NOT NULL,
    purpose character varying NOT NULL,
    status public.loans_status_enum DEFAULT 'pending'::public.loans_status_enum NOT NULL,
    "termInMonths" integer NOT NULL,
    "amountPaid" numeric(10,2) DEFAULT '0'::numeric NOT NULL,
    "approvedAt" timestamp without time zone,
    "disbursedAt" timestamp without time zone,
    "dueDate" timestamp without time zone,
    "paidAt" timestamp without time zone,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL,
    "updatedAt" timestamp without time zone DEFAULT now() NOT NULL,
    "userId" uuid,
    collateral text,
    "lastPenaltyDate" timestamp without time zone
);


ALTER TABLE public.loans OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 16931)
-- Name: mobile_money_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.mobile_money_transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    provider public.mobile_money_transactions_provider_enum DEFAULT 'MTN'::public.mobile_money_transactions_provider_enum NOT NULL,
    type public.mobile_money_transactions_type_enum NOT NULL,
    status public.mobile_money_transactions_status_enum DEFAULT 'PENDING'::public.mobile_money_transactions_status_enum NOT NULL,
    amount numeric(10,2) NOT NULL,
    currency character varying(3) DEFAULT 'EUR'::character varying NOT NULL,
    phone_number character varying(20) NOT NULL,
    external_id character varying(100) NOT NULL,
    reference_id character varying(100) NOT NULL,
    financial_transaction_id character varying(100),
    payer_message character varying(160),
    payee_note character varying(160),
    failure_reason character varying(500),
    metadata jsonb,
    callback_received boolean DEFAULT false NOT NULL,
    callback_data jsonb,
    processed_at timestamp without time zone,
    completed_at timestamp without time zone,
    failed_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    user_id uuid,
    loan_id uuid,
    transaction_id uuid
);


ALTER TABLE public.mobile_money_transactions OWNER TO postgres;

--
-- TOC entry 221 (class 1259 OID 16673)
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    message text NOT NULL,
    type public.notifications_type_enum DEFAULT 'system'::public.notifications_type_enum NOT NULL,
    status public.notifications_status_enum DEFAULT 'draft'::public.notifications_status_enum NOT NULL,
    "recipientId" uuid,
    "isAllUsers" boolean DEFAULT false NOT NULL,
    "scheduledFor" timestamp without time zone,
    "isRead" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL,
    "updatedAt" timestamp without time zone DEFAULT now() NOT NULL,
    "createdById" uuid,
    title character varying NOT NULL,
    "isAdminOnly" boolean DEFAULT false NOT NULL
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- TOC entry 222 (class 1259 OID 16765)
-- Name: otps; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.otps (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    code character varying NOT NULL,
    "phoneNumber" character varying NOT NULL,
    purpose public.otps_purpose_enum DEFAULT 'phone_verification'::public.otps_purpose_enum NOT NULL,
    status public.otps_status_enum DEFAULT 'pending'::public.otps_status_enum NOT NULL,
    "expiresAt" timestamp without time zone NOT NULL,
    "verifiedAt" timestamp without time zone,
    attempts integer DEFAULT 0 NOT NULL,
    "userId" uuid,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.otps OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 16497)
-- Name: transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    type public.transactions_type_enum NOT NULL,
    amount numeric(10,2) NOT NULL,
    status public.transactions_status_enum DEFAULT 'pending'::public.transactions_status_enum NOT NULL,
    reference character varying,
    description character varying,
    metadata character varying,
    "completedAt" timestamp without time zone,
    "failedAt" timestamp without time zone,
    "failureReason" character varying,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL,
    "updatedAt" timestamp without time zone DEFAULT now() NOT NULL,
    "userId" uuid
);


ALTER TABLE public.transactions OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 16461)
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    "fullName" character varying NOT NULL,
    email character varying NOT NULL,
    "phoneNumber" character varying NOT NULL,
    password character varying NOT NULL,
    role public.users_role_enum DEFAULT 'customer'::public.users_role_enum NOT NULL,
    status public.users_status_enum DEFAULT 'pending'::public.users_status_enum NOT NULL,
    "studentId" character varying,
    "isEmailVerified" boolean DEFAULT false NOT NULL,
    "isPhoneVerified" boolean DEFAULT false NOT NULL,
    "isFaceVerified" boolean DEFAULT false NOT NULL,
    "lastLoginAt" timestamp without time zone,
    "createdAt" timestamp without time zone DEFAULT now() NOT NULL,
    "updatedAt" timestamp without time zone DEFAULT now() NOT NULL,
    "passwordChanged" boolean DEFAULT false NOT NULL,
    "faceDescriptor" text,
    "faceImage" character varying,
    "profileImage" text
);


ALTER TABLE public.users OWNER TO postgres;

--
-- TOC entry 5007 (class 0 OID 16813)
-- Dependencies: 223
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.documents (id, user_id, loan_id, document_type, file_data, file_size, document_status, status_updated_at, status_updated_by, uploaded_by, upload_date, created_at, updated_at, file_name, original_file_name, file_path, mime_type, rejection_reason) FROM stdin;
b76fa81e-93a6-4ebd-96ca-a50f44aac982	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	\N	other	\N	6959	approved	2025-05-24 02:04:23.229	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	2025-05-24 02:03:55.68038	2025-05-24 02:03:55.68038	2025-05-24 02:04:23.25554	2c568b32-65e3-42f2-8c5d-c812a2eabc60.png	scan face page.png	uploads\\2c568b32-65e3-42f2-8c5d-c812a2eabc60.png	image/png	\N
e0f782f9-0699-48f9-8320-cbe3866e4926	44080d1a-3c13-4cc0-8e93-b723edfbf487	\N	other	\N	19889	approved	2025-05-24 02:05:10.189	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	44080d1a-3c13-4cc0-8e93-b723edfbf487	2025-05-24 01:50:56.536452	2025-05-24 01:50:56.536452	2025-05-24 02:05:10.193146	ea504cac-8d5a-4ffc-949f-5387500ed249.png	successful application pending page.png	uploads\\ea504cac-8d5a-4ffc-949f-5387500ed249.png	image/png	\N
\.


--
-- TOC entry 5003 (class 0 OID 16480)
-- Dependencies: 219
-- Data for Name: loans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.loans (id, amount, "interestRate", purpose, status, "termInMonths", "amountPaid", "approvedAt", "disbursedAt", "dueDate", "paidAt", "createdAt", "updatedAt", "userId", collateral, "lastPenaltyDate") FROM stdin;
6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	200.00	20.00	other	disbursed	2	0.00	2025-05-29 00:21:52.176	2025-05-29 00:22:37.25	2025-07-29 00:22:37.25	\N	2025-05-29 00:14:50.470094	2025-05-30 12:21:45.280904	1f202b6d-4880-47cd-872b-e58c29a9e0c8	phone	\N
551a8ada-c077-4a58-a920-b6a99b87d6d8	100.00	10.00	other	paid	1	110.00	2025-06-23 01:50:04.062	2025-06-23 01:50:55.794	2025-07-23 01:50:55.794	2025-06-23 02:35:07.84	2025-05-30 14:02:06.937309	2025-06-23 02:35:07.844379	44080d1a-3c13-4cc0-8e93-b723edfbf487	smart watch	\N
\.


--
-- TOC entry 5008 (class 0 OID 16931)
-- Dependencies: 224
-- Data for Name: mobile_money_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.mobile_money_transactions (id, provider, type, status, amount, currency, phone_number, external_id, reference_id, financial_transaction_id, payer_message, payee_note, failure_reason, metadata, callback_received, callback_data, processed_at, completed_at, failed_at, created_at, updated_at, user_id, loan_id, transaction_id) FROM stdin;
\.


--
-- TOC entry 5005 (class 0 OID 16673)
-- Dependencies: 221
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notifications (id, message, type, status, "recipientId", "isAllUsers", "scheduledFor", "isRead", "createdAt", "updatedAt", "createdById", title, "isAdminOnly") FROM stdin;
7f4705a5-b178-43df-89d5-bd054e2f0d12	Your loan application for E200.00 has been approved. The funds will be disbursed soon.	loan	sent	1f202b6d-4880-47cd-872b-e58c29a9e0c8	f	\N	t	2025-05-29 00:21:52.237194	2025-05-29 00:22:17.680911	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Application Approved	f
e97950ca-5062-4105-85e8-9e23f581107f	Your loan of E110.00 has been disbursed. The due date for repayment is 29/07/2025.	loan	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-05-29 15:49:02.745723	2025-05-30 08:36:49.998477	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Disbursed	f
393834e4-7240-4fd7-b2a5-7554f0fca95e	Your loan application for E100.00 has been approved. The funds will be disbursed soon.	loan	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-05-29 23:12:30.423366	2025-05-30 08:36:49.998477	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Application Approved	f
0a03ee55-3f62-4a74-9588-bfb4c8c9fd71	Your loan of E100.00 has been disbursed. The due date for repayment is 29/07/2025.	loan	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-05-29 23:12:39.522003	2025-05-30 08:36:49.998477	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Disbursed	f
c7d1b741-67ff-47bc-b7e7-772be210d3b5	A new loan application for E110 has been submitted by Samkelo Magagula. Purpose: other	loan	sent	\N	f	\N	t	2025-05-29 15:46:29.962587	2025-05-30 10:45:19.687456	44080d1a-3c13-4cc0-8e93-b723edfbf487	New Loan Application	t
30a75e97-8a94-45fc-b402-47038b9f93bc	Your loan application for E100.00 has been approved. The funds will be disbursed soon.	loan	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-05-30 13:59:11.995636	2025-06-23 01:45:35.742814	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Application Approved	f
df68c9a2-d470-4549-b7ed-df3d11c59f71	Your loan of E100.00 has been disbursed. The due date for repayment is 23/07/2025.	loan	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-06-23 01:50:56.200142	2025-06-23 02:13:21.142752	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Disbursed	f
b6b0668b-c097-46be-90bc-a1f630806d63	Your payment of E29.99 has been processed successfully.	payment	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-06-23 02:12:49.201044	2025-06-23 02:13:21.142752	\N	Payment Successful	f
8fb70c4e-7a7b-4117-a905-0490d515349d	Your loan of E200.00 has been disbursed. The due date for repayment is 29/07/2025.	loan	sent	1f202b6d-4880-47cd-872b-e58c29a9e0c8	f	\N	t	2025-05-29 00:22:37.380445	2025-05-29 00:23:15.25422	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Disbursed	f
96e476c0-4ece-40e5-be6a-27a32d2958fb	Your loan application for E110.00 has been approved. The funds will be disbursed soon.	loan	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-05-29 15:48:03.911432	2025-05-30 08:36:49.998477	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Application Approved	f
292021d9-69fb-4bbe-b4b7-21a60eaf3915	A new loan application for E100 has been submitted by Samkelo Magagula. Purpose: other	loan	sent	\N	f	\N	t	2025-05-29 21:32:13.513243	2025-05-30 10:45:19.687456	44080d1a-3c13-4cc0-8e93-b723edfbf487	New Loan Application	t
99ef26d5-ebc2-4dbe-a42c-07e3fa5738f6	A new loan application for E100 has been submitted by Samkelo Magagula. Purpose: education	loan	sent	\N	f	\N	t	2025-05-28 16:28:58.068543	2025-05-30 10:45:19.687456	44080d1a-3c13-4cc0-8e93-b723edfbf487	New Loan Application	t
074eb33f-9e3a-428f-a9fa-74ad7eb0525b	A new loan application for E200 has been submitted by Evereth Brown. Purpose: other	loan	sent	\N	f	\N	t	2025-05-29 00:14:50.60205	2025-05-30 10:45:19.687456	1f202b6d-4880-47cd-872b-e58c29a9e0c8	New Loan Application	t
e9ebf21c-a888-4a47-8e0b-396c1f0b000b	A new loan application for E100 has been submitted by Samkelo Magagula. Purpose: other	loan	sent	\N	f	\N	t	2025-05-30 13:00:11.406857	2025-06-23 01:49:40.72656	44080d1a-3c13-4cc0-8e93-b723edfbf487	New Loan Application	t
5259f484-d500-4728-9cf9-321944bdf5c4	A new loan application for E100 has been submitted by Samkelo Magagula. Purpose: other	loan	sent	\N	f	\N	t	2025-05-30 14:02:07.019712	2025-06-23 01:49:40.72656	44080d1a-3c13-4cc0-8e93-b723edfbf487	New Loan Application	t
998da7ab-9d94-48e1-a334-3491c69aa9c5	Your loan application for E100.00 has been approved. The funds will be disbursed soon.	loan	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-06-23 01:50:04.120813	2025-06-23 02:13:21.142752	b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Loan Application Approved	f
fe8e39b6-da7d-48e6-8925-9061a31c57ad	Your payment of E60 has been processed successfully.	payment	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-06-23 01:52:30.739628	2025-06-23 02:13:21.142752	\N	Payment Successful	f
911e24d7-a651-4519-b583-aa6f711ce1dd	Your payment of E80 has been processed successfully.	payment	sent	44080d1a-3c13-4cc0-8e93-b723edfbf487	f	\N	t	2025-06-23 02:35:07.830699	2025-06-23 02:35:56.518763	\N	Payment Successful	f
\.


--
-- TOC entry 5006 (class 0 OID 16765)
-- Dependencies: 222
-- Data for Name: otps; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.otps (id, code, "phoneNumber", purpose, status, "expiresAt", "verifiedAt", attempts, "userId", "createdAt") FROM stdin;
b5a2273d-2e19-4214-a0f7-f2c921c5d383	394026	+26879484541	phone_verification	expired	2025-04-25 01:04:36.086	\N	0	44080d1a-3c13-4cc0-8e93-b723edfbf487	2025-04-25 00:54:36.091296
a0788eed-ccda-46c8-bcd2-e71c68a906e0	791059	+26879484541	phone_verification	pending	2025-04-25 01:12:42.255	\N	0	44080d1a-3c13-4cc0-8e93-b723edfbf487	2025-04-25 01:02:42.26508
53736b76-f48f-4118-b8c1-334e427ec30e	733057	+26879484541	phone_verification	verified	2025-04-25 01:12:42.334	2025-04-25 01:06:20.517	1	44080d1a-3c13-4cc0-8e93-b723edfbf487	2025-04-25 01:02:42.336437
a6d61861-ebe3-4780-9128-f6f1236f8956	817025	+26879484541	phone_verification	expired	2025-04-25 02:42:24.96	\N	0	e39e99e2-aa60-404f-b603-95d191a8833a	2025-04-25 02:32:24.96591
2796d218-0b51-41d5-8e0a-26d6848afbe7	879115	+26879484541	phone_verification	expired	2025-04-25 02:42:26.03	\N	2	e39e99e2-aa60-404f-b603-95d191a8833a	2025-04-25 02:32:26.031534
4d055ee0-8d74-42a2-98d1-027fc19bbc3e	690019	+26879484541	phone_verification	verified	2025-04-25 02:44:01.98	2025-04-25 02:34:39.542	2	e39e99e2-aa60-404f-b603-95d191a8833a	2025-04-25 02:34:01.982918
\.


--
-- TOC entry 5004 (class 0 OID 16497)
-- Dependencies: 220
-- Data for Name: transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.transactions (id, type, amount, status, reference, description, metadata, "completedAt", "failedAt", "failureReason", "createdAt", "updatedAt", "userId") FROM stdin;
7df1d5a5-307d-45d3-b7ca-20ece2d33ea0	loan_repayment	240.00	completed	REPAY-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan repayment of E240	{"mtnTransactionId":"0a2f1688-05ed-4765-82b1-658ad78260ad","paymentMethod":"mobile_money","provider":"MTN"}	2025-05-29 03:01:18.809	\N	\N	2025-05-29 03:01:17.140186	2025-05-29 03:01:18.845696	1f202b6d-4880-47cd-872b-e58c29a9e0c8
8364abcd-79b3-4722-a36d-ac27c04b0b33	loan_disbursement	200.00	completed	DISBURSE-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan disbursement for $200.00	\N	2025-05-29 00:22:37.252	\N	\N	2025-05-29 00:22:37.254088	2025-05-29 00:22:37.254088	1f202b6d-4880-47cd-872b-e58c29a9e0c8
5bf74386-5b73-43e4-8d6d-3fb676ad3760	loan_repayment	100.00	completed	REPAY-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan repayment of $100	\N	2025-05-29 00:25:20.362	\N	\N	2025-05-29 00:25:20.363342	2025-05-29 00:25:20.363342	1f202b6d-4880-47cd-872b-e58c29a9e0c8
779358e4-1cc6-4b1e-9982-765a1aebf8e9	loan_repayment	100.00	completed	REPAY-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan repayment of $100	\N	2025-05-29 00:32:38.971	\N	\N	2025-05-29 00:32:38.972711	2025-05-29 00:32:38.972711	1f202b6d-4880-47cd-872b-e58c29a9e0c8
874fdc31-a203-4224-8501-5cf0d5e49769	loan_repayment	40.00	failed	REPAY-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan repayment of E40	\N	\N	\N	fetch failed	2025-05-29 01:44:34.065541	2025-05-29 01:44:45.585768	1f202b6d-4880-47cd-872b-e58c29a9e0c8
b390de96-daf3-4963-baf8-554d88805173	loan_repayment	100.00	completed	REPAY-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan repayment of E100	{"mtnTransactionId":"e7b8cf10-dd23-4b01-8fa7-7cf0aca39952","paymentMethod":"mobile_money","provider":"MTN"}	2025-05-29 02:58:34.539	\N	\N	2025-05-29 02:58:30.948568	2025-05-29 02:58:34.555187	1f202b6d-4880-47cd-872b-e58c29a9e0c8
dff43ff1-a88b-4c84-8ce1-489062c41b78	loan_repayment	20.00	completed	REPAY-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan repayment of E20	{"mtnTransactionId":"cb3e815c-91de-47a7-828d-59abe3a568cc","paymentMethod":"mobile_money","provider":"MTN"}	2025-05-29 03:01:52.428	\N	\N	2025-05-29 03:01:51.10523	2025-05-29 03:01:52.439812	1f202b6d-4880-47cd-872b-e58c29a9e0c8
58394aa4-05ff-40cb-bf4c-f57bc72a62e0	loan_repayment	240.00	completed	REPAY-6865ccb2-a5ab-4fd5-bb60-ae1d6273fd47	Loan repayment of E240	{"paymentMethod":"cash"}	2025-05-29 03:02:24.827	\N	\N	2025-05-29 03:02:24.804237	2025-05-29 03:02:24.838541	1f202b6d-4880-47cd-872b-e58c29a9e0c8
1079d35a-fa73-4fe6-ba2a-9a886f811c96	loan_disbursement	110.00	completed	DISBURSE-61d94cba-03ac-48c3-8441-4dd6f8f87e26	Loan disbursement for $110.00	\N	2025-05-29 15:49:02.369	\N	\N	2025-05-29 15:49:02.371288	2025-05-29 15:49:02.371288	44080d1a-3c13-4cc0-8e93-b723edfbf487
1ce5ca03-cb1f-49f4-9408-9fb24e83b53b	loan_disbursement	100.00	completed	DISBURSE-a326b4ab-b7dd-4b00-a777-f810016eb843	Loan disbursement for E100.00	{"mtnTransactionId":"b1f0e15b-77c7-4009-b44e-9ac6ee2a3728","paymentMethod":"mobile_money","provider":"MTN","loanId":"a326b4ab-b7dd-4b00-a777-f810016eb843"}	2025-05-29 23:12:39.152	\N	\N	2025-05-29 23:12:39.102893	2025-05-29 23:12:39.160067	44080d1a-3c13-4cc0-8e93-b723edfbf487
9d9b8882-3324-43d7-978e-c0a2bbcf3e74	loan_repayment	50.00	completed	REPAY-61d94cba-03ac-48c3-8441-4dd6f8f87e26	Loan repayment of E50	{"mtnTransactionId":"389e4014-79a6-4c9f-8f79-5161f32ce3f0","paymentMethod":"mobile_money","provider":"MTN"}	2025-05-29 15:53:07.846	\N	\N	2025-05-29 15:53:04.111606	2025-05-29 15:53:07.850422	44080d1a-3c13-4cc0-8e93-b723edfbf487
71286652-2cd7-43ff-bcd6-2fd2fb3563e1	loan_repayment	60.00	completed	REPAY-551a8ada-c077-4a58-a920-b6a99b87d6d8	Loan repayment of E60	{"paymentMethod":"mobile_money","simulatedPayment":true,"phoneNumber":"46733123453"}	2025-06-23 01:52:30.679	\N	\N	2025-06-23 01:52:30.335613	2025-06-23 01:52:30.685714	44080d1a-3c13-4cc0-8e93-b723edfbf487
055ac5df-6dc7-4e04-bbf0-35622ae81746	loan_repayment	120.00	completed	REPAY-a326b4ab-b7dd-4b00-a777-f810016eb843	Loan repayment of E120	{"loanId":"a326b4ab-b7dd-4b00-a777-f810016eb843","mtnTransactionId":"1d4f8520-8c88-46de-a83f-757eb2d4b57f","paymentMethod":"mobile_money","provider":"MTN","payerPhone":"+26879484541","repaymentType":"loan_repayment"}	2025-05-30 00:38:05.444	\N	\N	2025-05-30 00:38:04.846785	2025-05-30 00:38:05.511652	44080d1a-3c13-4cc0-8e93-b723edfbf487
be807558-c64d-4197-83fc-50067697f43e	loan_repayment	29.99	completed	REPAY-551a8ada-c077-4a58-a920-b6a99b87d6d8	Loan repayment of E29.99	{"paymentMethod":"mobile_money","simulatedPayment":true,"phoneNumber":"46733123453"}	2025-06-23 02:12:49.171	\N	\N	2025-06-23 02:12:49.067058	2025-06-23 02:12:49.182721	44080d1a-3c13-4cc0-8e93-b723edfbf487
0e8e6950-b5aa-4e91-a856-271929e43002	loan_repayment	100.00	completed	REPAY-a326b4ab-b7dd-4b00-a777-f810016eb843	Loan repayment of E100	{"loanId":"a326b4ab-b7dd-4b00-a777-f810016eb843","mtnTransactionId":"e99dfa28-22b1-4820-bd2b-302f3b039b12","paymentMethod":"mobile_money","provider":"MTN","payerPhone":"+26879484541","repaymentType":"loan_repayment"}	2025-05-30 08:38:58.719	\N	\N	2025-05-30 08:38:58.416868	2025-05-30 08:38:58.765713	44080d1a-3c13-4cc0-8e93-b723edfbf487
1b2f6221-d08b-4806-8e33-79c58266cbdf	loan_repayment	80.00	completed	REPAY-551a8ada-c077-4a58-a920-b6a99b87d6d8	Loan repayment of E80	{"paymentMethod":"mobile_money","simulatedPayment":true,"phoneNumber":"46733123453"}	2025-06-23 02:35:07.802	\N	\N	2025-06-23 02:35:07.693242	2025-06-23 02:35:07.810235	44080d1a-3c13-4cc0-8e93-b723edfbf487
7d1c12a6-51b5-46cd-899a-c9f873a97a38	loan_disbursement	100.00	completed	DISBURSE-551a8ada-c077-4a58-a920-b6a99b87d6d8	Loan disbursement for $100.00	\N	2025-06-23 01:50:55.796	\N	\N	2025-06-23 01:50:55.797272	2025-06-23 01:50:55.797272	44080d1a-3c13-4cc0-8e93-b723edfbf487
\.


--
-- TOC entry 5002 (class 0 OID 16461)
-- Dependencies: 218
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, "fullName", email, "phoneNumber", password, role, status, "studentId", "isEmailVerified", "isPhoneVerified", "isFaceVerified", "lastLoginAt", "createdAt", "updatedAt", "passwordChanged", "faceDescriptor", "faceImage", "profileImage") FROM stdin;
f438da20-20b2-450c-819f-6a5fd66771e4	Tester User	<EMAIL>	+2687967890	$2a$10$FzufyJIz0BZ168FAtHCWAuTPbf3vaUgqXdMZSTfSRHYDFTzBmloUy	customer	pending	ST12245	f	f	f	\N	2025-03-20 23:57:29.522038	2025-03-20 23:57:29.522038	f	\N	\N	\N
c8a9f1ac-032e-4253-839b-cf4bc42cc545	Another Test User	<EMAIL>	+1234567892	$2a$10$MhbH2lZGWdWDfdFHksFacOGpN0j4lg9.ssGYwsCqBlP1icIniUuPq	customer	pending	ST12347	f	f	f	\N	2025-03-22 22:34:58.493737	2025-03-22 22:34:58.493737	f	\N	\N	\N
bc7cefa7-c346-4414-acfd-d36c7df94612	Another TestUser2	<EMAIL>	+1134567892	$2a$10$tDZOHVTQ.XoFNzkiAtbq9Ov27z6vEzBhGMl1y9ZYfHV89Bb7ut2AS	customer	active	ST12347	f	f	f	\N	2025-03-22 22:45:18.112698	2025-03-22 22:45:18.112698	f	\N	\N	\N
a2dc7464-a62d-4458-9a96-545b8dc61635	Another User2	<EMAIL>	+1124567892	$2a$10$h50BPpT3awaENdDdYXWjSe9F0BJQbQKahrEpMHpjFXWVs9RQwZBsa	customer	active	ST12247	t	t	t	2025-03-22 22:58:50.583	2025-03-22 22:57:33.476363	2025-03-22 23:08:20.805113	f	\N	\N	\N
28041547-c550-4c38-9f2b-f1981982088b	some User1	<EMAIL>	+1122367892	$2a$10$Zf31gALe7yIL7c/uWDQC/.GreMCkS0Py7y08ul7y/0FOz4wqHtdAq	customer	active	ST12236	f	f	f	\N	2025-03-22 23:24:04.042209	2025-03-22 23:24:04.042209	f	\N	\N	\N
2ac343cc-f862-411f-8cb9-a8807a9128c8	Admin tester	<EMAIL>	+1334567890	$2a$10$N9qo8uLOickgx2ZMRZoMyeIjZAgcfl7p92ldGxad68LJZdL17lhWy	admin	active	\N	t	t	f	\N	2025-04-15 00:53:00.827276	2025-04-15 00:53:00.827276	t	\N	\N	\N
c51eab8b-7fdb-4177-8322-99ce1986e011	Test1 User	<EMAIL>	+1234567893	$2a$10$DI.tinQe2Dmo3TF6iheHCuVeaFmAATSonpSfz3P943.Uz5XbM0CCq	customer	active	ST12348	f	f	f	2025-03-23 01:55:07.961	2025-03-22 23:30:19.207569	2025-03-23 01:55:08.037731	t	\N	\N	\N
9905be88-8e54-4339-851c-49d0a538af24	Admin test	<EMAIL>	+2224567890	$2b$10$O8VX9cxDk3HN.hZ5Xm//7.RXR2N5PN5K/iOvBLEILPKULSVNLSKDO	admin	active	\N	t	t	f	\N	2025-04-15 00:45:01.648748	2025-04-15 00:45:01.648748	t	\N	\N	\N
3cabf42c-b07e-44a5-b64f-f45e0a4c1782	SimpleAdmin	<EMAIL>	+1424567890	$2b$10$O8VX9cxDk3HN.hZ5Xm//7.RXR2N5PN5K/iOvBLEILPKULSVNLSKDO	admin	active	\N	t	t	f	\N	2025-04-15 01:28:47.685982	2025-04-15 01:28:47.685982	t	\N	\N	\N
b0a01af3-aee3-4caf-af5c-2dadcd408121	Simple-Admin	<EMAIL>	+1444567890	$2a$10$N9qo8uLOickgx2ZMRZoMyeIjZAgcfl7p92ldGxad68LJZdL17lhWy	admin	active	\N	t	t	f	\N	2025-04-15 01:51:15.05999	2025-04-15 01:51:15.05999	t	\N	\N	\N
4b7d1e13-b7f3-4fed-adb9-2aec75087757	Samu Magagula	<EMAIL>	+26879484542	$2b$10$/sQMLaL2NJ/VDgW7y99ky.RpJWMAH8z..R3D2HePt7Y5AIT54AY0G	customer	active	202002222	f	f	f	2025-05-27 02:44:08.553	2025-03-23 03:15:22.307995	2025-05-27 02:44:08.650382	t	\N	\N	\N
c38f9e9f-c076-4d13-8ff0-684d14590464	Jack Nelson	<EMAIL>	+5514567890	$2a$10$w9CbXZQdaiP5gq0TuIJuRe69wTgdaSlejf6V13LwxKyR0FL2FqyQ.	customer	active	202102424	f	f	f	\N	2025-05-24 03:08:03.318838	2025-05-24 03:08:03.318838	f	\N	\N	\N
a3ec2f0d-4324-4ea1-b4c4-90758b8237bc	Lord  V	<EMAIL>	+1234566890	$2a$10$ICOXgOWtO.4L3i1rOYI7neXHI3iDz/BV6JDFzDvRcJtPVkYU0EaFO	customer	active	2020012223	f	f	f	2025-05-27 02:45:47.786	2025-04-17 02:18:08.620039	2025-05-27 02:45:47.832363	f	\N	\N	\N
1f202b6d-4880-47cd-872b-e58c29a9e0c8	Evereth Brown	<EMAIL>	+46733123453	$2a$10$.HH.CKVtqZKf5QTzSrLPwu05DwZlJ68DepDJ366mQheCdLkSy7JLC	customer	active	202101234	f	f	t	2025-05-30 12:18:49.875	2025-05-28 16:38:21.406289	2025-05-30 12:18:49.967496	f	[-0.13469547033309937,0.19653832912445068,0.14811940491199493,0.02735171839594841,-0.001115010236389935,-0.014902439899742603,0.0025701988488435745,-0.007687276694923639,0.14295917749404907,-0.08009456843137741,0.26690906286239624,-0.018013210967183113,-0.17176181077957153,-0.08048602938652039,0.04755723848938942,0.15054553747177124,-0.19140982627868652,-0.08811524510383606,-0.10032757371664047,-0.07657779008150101,0.05500797927379608,0.10862364619970322,-0.053505804389715195,0.024063605815172195,-0.04992504417896271,-0.300616979598999,-0.03404632583260536,-0.12294849753379822,0.0883060023188591,-0.09536949545145035,0.0029709357768297195,0.0005481081898324192,-0.12440474331378937,-0.0024278636556118727,-0.07366190105676651,-0.04027087986469269,0.04772535338997841,0.013361051678657532,0.11605213582515717,0.028298361226916313,-0.149810329079628,0.011641796678304672,0.009186968207359314,0.3238787055015564,0.2253575176000595,-0.002854094607755542,-0.005460078828036785,0.05576073005795479,-0.018498430028557777,-0.17604932188987732,0.01579260267317295,0.0915801152586937,0.20028960704803467,0.07903233170509338,0.035133879631757736,-0.1516474485397339,-0.08808372169733047,0.03753158822655678,-0.1147436797618866,0.09969097375869751,0.07944817841053009,-0.11345787346363068,-0.026545466855168343,-0.010695123113691807,0.2808760106563568,0.00027597282314673066,-0.13675159215927124,-0.10425351560115814,0.12905974686145782,-0.1002868190407753,-0.045929476618766785,0.08746645599603653,-0.12575939297676086,-0.09469816833734512,-0.2154701054096222,0.12232447415590286,0.352059006690979,0.07255549728870392,-0.2544862627983093,-0.014002135954797268,-0.14536896347999573,-0.0018733803881332278,0.02073163166642189,0.08379395306110382,-0.020810112357139587,-0.052124835550785065,-0.1045648455619812,-0.01820071041584015,0.15373967587947845,0.007497552316635847,-0.03097980096936226,0.2227640300989151,0.0008291027625091374,-0.09626413136720657,-0.033591460436582565,-0.061739712953567505,-0.0002666381769813597,-0.05095615237951279,-0.05976888909935951,-0.06153208017349243,-0.05429839715361595,-0.1276034563779831,-0.08435537666082382,0.07343355566263199,-0.1936013400554657,0.16688133776187897,0.06304743140935898,0.03610723465681076,0.015705112367868423,0.09989863634109497,-0.08369851857423782,-0.03317424654960632,0.19176068902015686,-0.19566191732883453,0.1603289693593979,0.17010243237018585,0.08194418251514435,0.13558420538902283,0.0008694028947502375,0.13855861127376556,-0.036030806601047516,-0.0008236191351898015,-0.1004168689250946,-0.0582280196249485,0.046725817024707794,-0.07306334376335144,0.052550774067640305,0.05983492359519005]	\N	\N
7d96b646-befb-4b21-a44c-051c15fdfc83	Simphiwe Dlamini	<EMAIL>	+26878605716	$2a$10$mSTUCHA1Iagf8TfpcR6VzOjGzvyY0GNXFRSpu6qVtTSOyhXjaSiJW	customer	active	202102463	f	t	t	2025-05-05 22:54:15.047	2025-05-05 22:53:04.330158	2025-05-05 23:01:26.577115	f	[-0.1530679613351822,0.057180047035217285,0.039575204253196716,-0.04710566997528076,-0.053843311965465546,-0.06496009975671768,0.08264464884996414,-0.11107735335826874,0.22810448706150055,-0.10792668163776398,0.23786561191082,-0.0005651608225889504,-0.20543931424617767,-0.10963967442512512,0.026400404050946236,0.19068656861782074,-0.14777663350105286,-0.1759423166513443,-0.08924936503171921,-0.07350090891122818,0.02229938469827175,-0.03336089104413986,-0.062247104942798615,0.13374494016170502,-0.11114270240068436,-0.316106915473938,-0.11844970285892487,-0.12770962715148926,-0.02247338369488716,-0.044167954474687576,0.020103631541132927,0.14120472967624664,-0.1610536426305771,-0.029643138870596886,-0.018354114145040512,0.013038067147135735,0.01760846935212612,-0.06735396385192871,0.13717813789844513,-0.00741174491122365,-0.20586520433425903,-0.051886700093746185,0.08560570329427719,0.23750321567058563,0.15653641521930695,-0.07723794132471085,0.07096988707780838,-0.03087344579398632,0.013414557091891766,-0.24184063076972961,-0.0004620589315891266,0.1051522046327591,0.16274328529834747,0.05880625173449516,0.04996293783187866,-0.19176746904850006,-0.07197428494691849,0.04821193218231201,-0.17610523104667664,0.01854782924056053,-0.022322138771414757,-0.08215422928333282,-0.17220617830753326,-0.09218209981918335,0.22639060020446777,0.17973466217517853,-0.14554181694984436,-0.14196588099002838,0.25566771626472473,-0.1525205820798874,-0.03860410302877426,0.08558304607868195,-0.15197107195854187,-0.19512738287448883,-0.25235897302627563,0.04492803290486336,0.37460920214653015,0.13328669965267181,-0.19004134833812714,0.05794661492109299,-0.22023993730545044,0.03300604969263077,-0.07669208198785782,0.06782932579517365,-0.013970271684229374,0.021612612530589104,-0.059257108718156815,0.036881424486637115,0.13491138815879822,-0.01268826425075531,0.009979852475225925,0.23934468626976013,0.021679632365703583,-0.06131279096007347,-0.03871973231434822,0.007360563147813082,0.005624143406748772,-0.04843705892562866,-0.11379852890968323,-0.06080400571227074,0.004908016417175531,-0.0307612307369709,-0.009307760745286942,0.0739174634218216,-0.24824708700180054,0.13485746085643768,0.07261496782302856,-0.014305582270026207,0.009123018942773342,0.058996494859457016,-0.059064481407403946,-0.04441564157605171,0.12664978206157684,-0.265002578496933,0.13303712010383606,0.15679308772087097,0.006353199947625399,0.16553165018558502,-0.018788594752550125,0.0936414897441864,-0.0265659149736166,-0.14706915616989136,-0.14022795855998993,-0.013157370500266552,0.0855845957994461,-0.04746636003255844,-0.050990208983421326,0.03789027780294418]	\N	\N
e31c3065-ef50-4b60-9fc2-07df16d6dc4c	Levi Smith	<EMAIL>	+26879484544	$2b$10$f3qzRBSqcfF6XLaeiFj7nutrBBJ/HgHhbNF9kU6g5pZ20SARYA3Cm	customer	active	202001313	f	t	t	2025-05-27 20:27:15.073	2025-04-12 04:13:42.471	2025-05-27 20:27:15.206155	f	[-0.10376639664173126,0.21694894134998322,0.13327540457248688,0.010947857052087784,0.014189032837748528,-0.031043462455272675,0.017619863152503967,-0.05256986990571022,0.13639678061008453,-0.10089744627475739,0.31042081117630005,-0.02716633677482605,-0.18128414452075958,-0.08291631937026978,0.06394659727811813,0.14052096009254456,-0.19659584760665894,-0.10454940795898438,-0.09931675344705582,-0.0619577020406723,0.06212428957223892,0.07752416282892227,-0.019968373700976372,0.03279988467693329,-0.06829192489385605,-0.301331102848053,-0.0657234862446785,-0.08664044737815857,0.1016623005270958,-0.11915385723114014,0.0318998359143734,-0.001268093241378665,-0.1326109915971756,0.008562253788113594,-0.08111286908388138,-0.0370790995657444,0.04742850363254547,-0.022016173228621483,0.12618954479694366,0.005870997440069914,-0.1562778502702713,-0.020893292501568794,0.026618067175149918,0.31479576230049133,0.2243746966123581,-0.009458338841795921,-0.005510237999260426,0.04747788980603218,-0.016486525535583496,-0.15605370700359344,0.022562077268958092,0.06775855273008347,0.2121603637933731,0.08480515331029892,0.024066051468253136,-0.1506439447402954,-0.06478890776634216,0.04241228848695755,-0.12348394840955734,0.09153273701667786,0.07677608728408813,-0.11816703528165817,-0.012052932754158974,0.010377608239650726,0.28822290897369385,0.007862336933612823,-0.14637833833694458,-0.08495191484689713,0.16223202645778656,-0.10016539692878723,-0.05342813953757286,0.07423575222492218,-0.12305239588022232,-0.1044926792383194,-0.2256251871585846,0.09266465157270432,0.33177924156188965,0.06633120030164719,-0.262952983379364,-0.01592966355383396,-0.11226700246334076,0.008764802478253841,0.0024268238339573145,0.107845738530159,-0.05477401986718178,-0.026832982897758484,-0.1456822156906128,-0.0019045006483793259,0.16842955350875854,0.009747459553182125,-0.051949772983789444,0.2560039162635803,-0.013434510678052902,-0.08208338171243668,0.008567786775529385,-0.02296648547053337,-0.018899712711572647,-0.051453229039907455,-0.08110077679157257,-0.06191374361515045,-0.020081067457795143,-0.13353636860847473,-0.10445687919855118,0.10963872820138931,-0.17300552129745483,0.19186176359653473,0.060269344598054886,0.0496952198445797,0.019591083750128746,0.10065429657697678,-0.07103127986192703,-0.05921119078993797,0.17306527495384216,-0.18460185825824738,0.1392812430858612,0.16275230050086975,0.07167311012744904,0.13113810122013092,0.008208456449210644,0.15601713955402374,-0.022604944184422493,-0.01195622980594635,-0.1075863391160965,-0.06053107976913452,0.03106076270341873,-0.08047591149806976,0.06854452192783356,0.06801647692918777]	\N	\N
e39e99e2-aa60-404f-b603-95d191a8833a	Mphilo Magagula	<EMAIL>	+26879484546	$2b$10$/yKI5gDewbYMTETdTLhiK.CMt84f60d.veeF3PXwIMabcj5QXbNt6	customer	active	202001111	f	t	t	2025-05-26 19:56:56.597	2025-03-23 03:05:39.204878	2025-05-26 19:56:56.7126	f	[-0.1520725041627884,0.2302975356578827,0.12997956573963165,-0.0036659175530076027,0.008299597539007664,0.0016237953677773476,-0.02332628332078457,-0.061637572944164276,0.11719974130392075,-0.0867864191532135,0.2749550938606262,-0.007057783659547567,-0.17547595500946045,-0.0963524803519249,0.06599237024784088,0.15670624375343323,-0.25408732891082764,-0.10955280810594559,-0.10411575436592102,-0.05915955454111099,0.04018073529005051,0.0670386329293251,-0.06210894510149956,0.035910528153181076,-0.0659308210015297,-0.3157167136669159,-0.06159643456339836,-0.11803743243217468,0.07521028816699982,-0.09294351190328598,0.013098092749714851,-0.061796415597200394,-0.14650127291679382,0.023347530514001846,-0.08746670931577682,-0.020560218021273613,0.07594646513462067,-0.02862589620053768,0.1303219199180603,0.011291155591607094,-0.1444331854581833,0.014040070585906506,0.002214662032201886,0.3120731711387634,0.23606638610363007,-0.02708282880485058,0.014532956294715405,0.06460356712341309,0.0072683305479586124,-0.17554937303066254,-0.007835831493139267,0.09472092241048813,0.18856242299079895,0.07545095682144165,0.009667482227087021,-0.1670559197664261,-0.06771077960729599,0.04433468356728554,-0.1149306371808052,0.08545827120542526,0.07269608229398727,-0.12981504201889038,0.0004163985140621662,0.025013171136379242,0.3156127333641052,0.03586249053478241,-0.1697094887495041,-0.10981544107198715,0.12877076864242554,-0.09041417390108109,-0.04223859682679176,0.03377096727490425,-0.13906629383563995,-0.10714919120073318,-0.27416476607322693,0.12117796391248703,0.36541351675987244,0.06910908222198486,-0.3048509955406189,-0.03088812343776226,-0.16258035600185394,0.01611764170229435,0.058219946920871735,0.11939189583063126,-0.024857716634869576,-0.038584280759096146,-0.14712107181549072,-0.00039916843525134027,0.19597040116786957,0.015354250557720661,-0.04288123920559883,0.23514683544635773,-0.003126606112346053,-0.07395069301128387,-0.015864331275224686,-0.014588928781449795,-0.0031145187094807625,-0.06459710747003555,-0.050072889775037766,-0.0023086494766175747,-0.03920062631368637,-0.1092817559838295,-0.093569815158844,0.1146770715713501,-0.16732168197631836,0.1553088277578354,0.06889167428016663,0.0300843957811594,-0.024218853563070297,0.08304086327552795,-0.09581079334020615,-0.04750245437026024,0.19178901612758636,-0.2075214684009552,0.171624556183815,0.20130689442157745,0.040443580597639084,0.14210158586502075,0.018566742539405823,0.1361551433801651,-0.04401300475001335,0.007485552690923214,-0.10087447613477707,-0.06630311906337738,0.024184830486774445,-0.05609916150569916,0.07279849052429199,0.06039619445800781]	\N	\N
44080d1a-3c13-4cc0-8e93-b723edfbf487	Samkelo Magagula	<EMAIL>	+26879484541	$2b$10$pifiUT9Jv1meR6oLXFWQqOS/MScaW4CzBuryQNaQjHJeCv4.7yGHm	customer	active	202001234	f	t	t	2025-06-23 01:19:50.243	2025-03-23 02:44:51.598054	2025-06-23 01:19:50.299313	f	[-0.12328901886940002,0.18472431600093842,0.10602311044931412,0.018072299659252167,0.0346144363284111,-0.031125685200095177,-0.015101026743650436,-0.03730146586894989,0.150705486536026,-0.0767335444688797,0.29716676473617554,-0.009234349243342876,-0.19430038332939148,-0.058326005935668945,0.07040753960609436,0.14462928473949432,-0.2052062749862671,-0.06732381880283356,-0.09406930953264236,-0.040214959532022476,0.05955873429775238,0.07012573629617691,-0.011260504834353924,0.05032752826809883,-0.07214584201574326,-0.2815014719963074,-0.07133695483207703,-0.12055089324712753,0.0839102640748024,-0.13534432649612427,-0.00569954514503479,-0.03190741688013077,-0.13535422086715698,-0.00250439764931798,-0.06505558639764786,-0.0598711334168911,0.03535476326942444,-0.03370600566267967,0.1175297200679779,0.0033298509661108255,-0.1430605947971344,-0.054705701768398285,0.025112884119153023,0.26392897963523865,0.20607033371925354,-0.0018664010567590594,0.03386587277054787,0.0055419085547327995,-0.023818595334887505,-0.15872438251972198,0.02729775570333004,0.06954584270715714,0.1982704997062683,0.09850211441516876,0.05009400472044945,-0.16667422652244568,-0.04978935420513153,0.05384154990315437,-0.09757345169782639,0.1227743849158287,0.08286747336387634,-0.07198937237262726,-0.007168127689510584,0.0435311421751976,0.2849825918674469,0.006657442077994347,-0.1505603939294815,-0.09048887342214584,0.14295455813407898,-0.0876116156578064,-0.053278598934412,0.09546173363924026,-0.10577839612960815,-0.16490577161312103,-0.22446973621845245,0.06996297091245651,0.3125283420085907,0.08219490945339203,-0.2713301479816437,0.005486917216330767,-0.13417328894138336,-0.018706675618886948,0.014532001689076424,0.12034371495246887,-0.03867914155125618,-0.012860101647675037,-0.13555622100830078,-0.002202392555773258,0.17701488733291626,0.014090782031416893,-0.012555941008031368,0.24646958708763123,-0.013020528480410576,-0.09779443591833115,-0.0002742225187830627,0.01583005115389824,-0.028298743069171906,-0.06580818444490433,-0.04669574275612831,-0.07989471405744553,-0.0003821647842414677,-0.10379916429519653,-0.10256721079349518,0.09960510581731796,-0.17622457444667816,0.1777290254831314,0.0506901890039444,0.02608100138604641,0.00823452603071928,0.08109665662050247,-0.04828839376568794,-0.04498930275440216,0.15355321764945984,-0.20226529240608215,0.1391041874885559,0.1525159478187561,0.05374037101864815,0.12918363511562347,-0.0297964159399271,0.1651313602924347,-0.025470109656453133,-0.016494598239660263,-0.10720814764499664,-0.059268027544021606,0.01901298761367798,-0.056483637541532516,0.051828235387802124,0.04452294111251831]	\N	\N
b7de95b7-9602-4af4-a4ef-2f7d5e0a64d0	Razan	<EMAIL>	+3134567890	$2b$10$lciRCxaHhmo1HHmb06NoGe5kX.YMCVFYP5mKti3XSa8govOEw/I5q	admin	active	sam911	t	t	f	2025-06-23 04:03:54.945	2025-04-15 02:17:07.878574	2025-06-23 04:03:55.318294	t	\N	\N	\N
\.


--
-- TOC entry 4836 (class 2606 OID 16776)
-- Name: otps PK_3c3410e4b9c2b9f4f5f7f1e54d2; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.otps
    ADD CONSTRAINT "PK_3c3410e4b9c2b9f4f5f7f1e54d2" PRIMARY KEY (id);


--
-- TOC entry 4840 (class 2606 OID 16944)
-- Name: mobile_money_transactions PK_947ca4b0acabf6d89ed5eb9820b; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mobile_money_transactions
    ADD CONSTRAINT "PK_947ca4b0acabf6d89ed5eb9820b" PRIMARY KEY (id);


--
-- TOC entry 4830 (class 2606 OID 16491)
-- Name: loans PK_loans; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT "PK_loans" PRIMARY KEY (id);


--
-- TOC entry 4832 (class 2606 OID 16507)
-- Name: transactions PK_transactions; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT "PK_transactions" PRIMARY KEY (id);


--
-- TOC entry 4824 (class 2606 OID 16475)
-- Name: users PK_users; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "PK_users" PRIMARY KEY (id);


--
-- TOC entry 4826 (class 2606 OID 16479)
-- Name: users UQ_17d1817f241f10a3dbafb169320; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "UQ_17d1817f241f10a3dbafb169320" UNIQUE ("phoneNumber");


--
-- TOC entry 4842 (class 2606 OID 16946)
-- Name: mobile_money_transactions UQ_7a1f93b160149c00d297903fc0e; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mobile_money_transactions
    ADD CONSTRAINT "UQ_7a1f93b160149c00d297903fc0e" UNIQUE (external_id);


--
-- TOC entry 4844 (class 2606 OID 16948)
-- Name: mobile_money_transactions UQ_8faa71769164fd181cb2ce812b2; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mobile_money_transactions
    ADD CONSTRAINT "UQ_8faa71769164fd181cb2ce812b2" UNIQUE (reference_id);


--
-- TOC entry 4828 (class 2606 OID 16477)
-- Name: users UQ_97672ac88f789774dd47f7c8be3; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE (email);


--
-- TOC entry 4838 (class 2606 OID 16824)
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);


--
-- TOC entry 4834 (class 2606 OID 16686)
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- TOC entry 4850 (class 2606 OID 16894)
-- Name: documents FK_262fa29b7b7c260372c27a59eef; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "FK_262fa29b7b7c260372c27a59eef" FOREIGN KEY (status_updated_by) REFERENCES public.users(id);


--
-- TOC entry 4845 (class 2606 OID 16600)
-- Name: loans FK_4c2ab4e556520045a2285916d45; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.loans
    ADD CONSTRAINT "FK_4c2ab4e556520045a2285916d45" FOREIGN KEY ("userId") REFERENCES public.users(id);


--
-- TOC entry 4854 (class 2606 OID 16959)
-- Name: mobile_money_transactions FK_66763a0ef1e73a2f11641d8d436; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mobile_money_transactions
    ADD CONSTRAINT "FK_66763a0ef1e73a2f11641d8d436" FOREIGN KEY (transaction_id) REFERENCES public.transactions(id);


--
-- TOC entry 4846 (class 2606 OID 16605)
-- Name: transactions FK_6bb58f2b6e30cb51a6504599f41; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT "FK_6bb58f2b6e30cb51a6504599f41" FOREIGN KEY ("userId") REFERENCES public.users(id);


--
-- TOC entry 4849 (class 2606 OID 16785)
-- Name: otps FK_82b0deb105275568cdcef2823eb; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.otps
    ADD CONSTRAINT "FK_82b0deb105275568cdcef2823eb" FOREIGN KEY ("userId") REFERENCES public.users(id);


--
-- TOC entry 4851 (class 2606 OID 16889)
-- Name: documents FK_8fe15ddaca1ccaed6f858ce1e29; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "FK_8fe15ddaca1ccaed6f858ce1e29" FOREIGN KEY (loan_id) REFERENCES public.loans(id);


--
-- TOC entry 4855 (class 2606 OID 16954)
-- Name: mobile_money_transactions FK_ada231ac234cf832a779317816c; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mobile_money_transactions
    ADD CONSTRAINT "FK_ada231ac234cf832a779317816c" FOREIGN KEY (loan_id) REFERENCES public.loans(id);


--
-- TOC entry 4856 (class 2606 OID 16949)
-- Name: mobile_money_transactions FK_b50cb425cb2ee7376e21cb081c5; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.mobile_money_transactions
    ADD CONSTRAINT "FK_b50cb425cb2ee7376e21cb081c5" FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 4852 (class 2606 OID 16899)
-- Name: documents FK_b9e28779ec77ff2223e2da41f6d; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "FK_b9e28779ec77ff2223e2da41f6d" FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- TOC entry 4853 (class 2606 OID 16884)
-- Name: documents FK_c7481daf5059307842edef74d73; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT "FK_c7481daf5059307842edef74d73" FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- TOC entry 4847 (class 2606 OID 16738)
-- Name: notifications FK_db873ba9a123711a4bff527ccd5; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_db873ba9a123711a4bff527ccd5" FOREIGN KEY ("recipientId") REFERENCES public.users(id);


--
-- TOC entry 4848 (class 2606 OID 16743)
-- Name: notifications FK_fcce8c50a375466676d82dcbadd; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT "FK_fcce8c50a375466676d82dcbadd" FOREIGN KEY ("createdById") REFERENCES public.users(id);


-- Completed on 2025-06-24 00:18:32

--
-- PostgreSQL database dump complete
--

