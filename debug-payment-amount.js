// Debug script to investigate payment amount discrepancy
// This script helps identify where E60.00 becomes E0.01

console.log('🔍 Payment Amount Discrepancy Debug');
console.log('====================================');

// Test 1: Frontend amount parsing
console.log('\n1. Testing Frontend Amount Parsing:');
const userInput = "60";
const parsedAmount = parseFloat(userInput);
console.log(`   User Input: "${userInput}"`);
console.log(`   parseFloat(userInput): ${parsedAmount}`);
console.log(`   Type: ${typeof parsedAmount}`);
console.log(`   Is Valid: ${!isNaN(parsedAmount) && parsedAmount > 0}`);

// Test 2: JSON serialization (API call)
console.log('\n2. Testing JSON Serialization:');
const paymentData = { amount: parsedAmount };
const jsonString = JSON.stringify(paymentData);
const parsedBack = JSON.parse(jsonString);
console.log(`   Original: ${paymentData.amount}`);
console.log(`   JSON String: ${jsonString}`);
console.log(`   Parsed Back: ${parsedBack.amount}`);

// Test 3: Backend parsing
console.log('\n3. Testing Backend Parsing:');
const backendAmount = parseFloat(parsedBack.amount);
console.log(`   parseFloat(amount): ${backendAmount}`);
console.log(`   Type: ${typeof backendAmount}`);

// Test 4: Decimal precision issues
console.log('\n4. Testing Decimal Precision:');
const testAmounts = [60, 60.0, 60.00, "60", "60.0", "60.00"];
testAmounts.forEach(amount => {
  const parsed = parseFloat(amount);
  console.log(`   Input: ${amount} (${typeof amount}) -> Parsed: ${parsed} (${typeof parsed})`);
});

// Test 5: Potential currency conversion
console.log('\n5. Testing Potential Currency Conversion:');
const emalangeniAmount = 60; // E60
const potentialConversions = [
  { name: "EUR to SZL (if rate ~0.0167)", value: emalangeniAmount * 0.0167 },
  { name: "USD to SZL (if rate ~0.0167)", value: emalangeniAmount * 0.0167 },
  { name: "Cents to Dollars", value: emalangeniAmount / 100 },
  { name: "Percentage conversion", value: emalangeniAmount / 6000 },
];

potentialConversions.forEach(conversion => {
  console.log(`   ${conversion.name}: ${conversion.value.toFixed(6)}`);
  if (Math.abs(conversion.value - 0.01) < 0.001) {
    console.log(`   ⚠️  POTENTIAL MATCH: ${conversion.name} results in ~0.01`);
  }
});

// Test 6: Database decimal handling
console.log('\n6. Testing Database Decimal Handling:');
console.log('   PostgreSQL DECIMAL(10,2) should handle:');
console.log(`   - Input: 60.00`);
console.log(`   - Storage: 60.00`);
console.log(`   - Output: 60.00`);
console.log('   If this is failing, check TypeORM configuration');

// Test 7: Potential TypeORM issues
console.log('\n7. Potential TypeORM Issues:');
console.log('   Check if TypeORM is converting decimal values incorrectly');
console.log('   Common issues:');
console.log('   - String to number conversion');
console.log('   - Precision loss in JavaScript');
console.log('   - Database driver issues');

// Test 8: Debugging recommendations
console.log('\n8. Debugging Recommendations:');
console.log('   Add console.log statements in:');
console.log('   1. Frontend: Before API call');
console.log('   2. Backend Controller: req.body.amount');
console.log('   3. Backend Service: amount parameter');
console.log('   4. Before database save: transaction.amount');
console.log('   5. After database save: savedTransaction.amount');

console.log('\n🎯 Most Likely Causes:');
console.log('1. Currency conversion (E60 -> $0.01 if wrong exchange rate)');
console.log('2. Cents/dollars confusion (60 cents = $0.60, but stored as 0.01?)');
console.log('3. TypeORM decimal precision issue');
console.log('4. Database constraint or trigger');
console.log('5. Middleware modifying the request');

console.log('\n🔧 Next Steps:');
console.log('1. Add logging to trace the amount through the entire flow');
console.log('2. Check database directly after payment');
console.log('3. Verify TypeORM entity configuration');
console.log('4. Test with different amounts (1, 10, 100)');
console.log('5. Check for any middleware or interceptors');
