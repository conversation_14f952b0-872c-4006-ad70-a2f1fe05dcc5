@startuml System Architecture
!theme plain
skinparam linetype ortho

package "Presentation Layer (Frontend)" {
  [Next.js 15 App Router] as A1
  [React 19 Components] as A2
  [Tailwind CSS/shadcn UI] as A3
  [face-api.js Client] as A4
  [Form Handling] as A5
  [React Hook Form] as A5a
  [useActionState] as A5b
  
  A1 --> A2
  A2 --> A3
  A2 --> A4
  A2 --> A5
  A5 --> A5a
  A5 --> A5b
}

package "Application Layer (Backend)" {
  [Express.js Server] as B1
  [Authentication Service] as B2
  [Loan Service] as B3
  [User Service] as B4
  [Transaction Service] as B5
  [Notification Service] as B6
  [JWT Auth] as B2a
  [Face Verification] as B2b
  [OTP Service] as B2c
  [Middleware] as B7
  [Auth Middleware] as B7a
  [Validation] as B7b
  [Rate Limiting] as B7c
  
  B1 --> B2
  B1 --> B3
  B1 --> B4
  B1 --> B5
  B1 --> B6
  
  B2 --> B2a
  B2 --> B2b
  B2 --> B2c
  
  B7 --> B7a
  B7 --> B7b
  B7 --> B7c
}

package "Data Layer" {
  database "PostgreSQL Database" as C1
  [TypeORM] as C2
  [User Entity] as C3
  [Loan Entity] as C4
  [Transaction Entity] as C5
  [Notification Entity] as C6
  [OTP Entity] as C7
  
  C1 --> C2
  C2 --> C3
  C2 --> C4
  C2 --> C5
  C2 --> C6
  C2 --> C7
}

' Connections between layers
A1 -[thickness=2]-> B1 : API Requests
B1 -[thickness=2]-> C2 : Data Access
B2b --> C3 : Face Descriptors
B2c --> C7 : OTP Codes
B3 --> C4 : Loan Data
B4 --> C3 : User Data
B5 --> C5 : Transaction Data
B6 --> C6 : Notifications

@enduml