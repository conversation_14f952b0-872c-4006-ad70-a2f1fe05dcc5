const crypto = require('crypto');

// Generate a salt
const generateSalt = () => {
  return crypto.randomBytes(16).toString('base64');
};

// Generate a hash using PBKDF2 (similar to bcrypt)
const generateHash = (password, salt) => {
  return crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('base64');
};

// Generate a bcrypt-like hash string
const generateBcryptLikeHash = (password) => {
  const salt = generateSalt();
  const hash = generateHash(password, salt);
  return `$2b$10$${salt}$${hash}`;
};

// Generate a hash for "admin111"
const hash = generateBcryptLikeHash('admin111');
console.log(hash);
