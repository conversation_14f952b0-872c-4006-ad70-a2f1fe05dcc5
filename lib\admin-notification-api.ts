import { API_URL } from './api';

// Helper function to handle API responses
const handleResponse = async (response: Response) => {
  if (response.ok) {
    const data = await response.json();
    return data;
  }

  const errorData = await response.json().catch(() => ({
    message: `API error: ${response.status}`
  }));

  throw new Error(errorData.message || `API error: ${response.status}`);
};

// Admin notification API calls
export const adminNotificationApi = {
  // Get admin notifications
  getAdminNotifications: async (page = 1, limit = 10, read?: boolean) => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) throw new Error('Admin authentication required');

      let url = `${API_URL}/notifications/admin/admin-notifications?page=${page}&limit=${limit}`;
      
      if (read !== undefined) {
        url += `&read=${read}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });

      return handleResponse(response);
    } catch (error) {
      console.error('Get admin notifications error:', error);
      throw error;
    }
  },
  
  // Mark notification as read
  markNotificationAsRead: async (notificationId: string) => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) throw new Error('Admin authentication required');

      const response = await fetch(`${API_URL}/notifications/user/${notificationId}/read`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Mark notification as read error:', error);
      throw error;
    }
  },

  // Mark all notifications as read
  markAllNotificationsAsRead: async () => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) throw new Error('Admin authentication required');

      const response = await fetch(`${API_URL}/notifications/user/read-all`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Mark all notifications as read error:', error);
      throw error;
    }
  }
};
