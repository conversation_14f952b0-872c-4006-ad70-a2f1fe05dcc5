import { handleResponse } from './admin-api'
import { API_URL } from './api'

export interface Transaction {
  id: string
  userId: string
  userName: string
  userPhoneNumber?: string
  loanId?: string
  amount: number
  type: "repayment" | "disbursement" | "deposit" | "withdrawal"
  method: "mobile_money" | "bank_transfer" | "cash"
  status: "completed" | "pending" | "failed" | "cancelled"
  date: string
  reference?: string
  description?: string
  completedAt?: string
  failedAt?: string
  failureReason?: string
  metadata?: any
}

export interface TransactionFilters {
  page?: number
  limit?: number
  search?: string
  type?: string
  status?: string
}

export interface TransactionResponse {
  transactions: Transaction[]
  total: number
  page: number
  limit: number
  pages: number
}

export interface TransactionStats {
  totalRepayments: number
  totalDisbursements: number
  pendingAmount: number
  totalTransactions: number
  completedTransactions: number
  failedTransactions: number
}

class AdminTransactionsAPI {
  private getAuthHeaders() {
    const token = localStorage.getItem('adminToken')
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  }

  // Get all transactions with filtering and pagination
  async getAllTransactions(filters: TransactionFilters = {}): Promise<TransactionResponse> {
    try {
      const queryParams = new URLSearchParams()

      if (filters.page) queryParams.append('page', filters.page.toString())
      if (filters.limit) queryParams.append('limit', filters.limit.toString())
      if (filters.search) queryParams.append('search', filters.search)
      if (filters.type && filters.type !== 'all') queryParams.append('type', filters.type)
      if (filters.status && filters.status !== 'all') queryParams.append('status', filters.status)

      const url = `${API_URL}/transactions/admin/all-transactions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

      console.log('Fetching transactions from:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      })

      const result = await handleResponse(response)

      // Ensure the response has the expected structure
      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error('Invalid response structure from transactions API')
      }
    } catch (error) {
      console.error('Error in getAllTransactions:', error)
      throw error
    }
  }

  // Update transaction status
  async updateTransactionStatus(
    transactionId: string,
    status: string,
    metadata?: any,
    failureReason?: string
  ): Promise<Transaction> {
    try {
      const response = await fetch(`${API_URL}/transactions/${transactionId}/status`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          status,
          metadata,
          failureReason
        }),
      })

      const result = await handleResponse(response)

      // Return the transaction data from the response
      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error('Invalid response structure from transaction status update API')
      }
    } catch (error) {
      console.error('Error updating transaction status:', error)
      throw error
    }
  }

  // Get transaction statistics
  async getTransactionStatistics(transactions: Transaction[]): Promise<TransactionStats> {
    // Calculate statistics from the transactions data
    const totalRepayments = transactions
      .filter(t => t.type === 'repayment' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0)

    const totalDisbursements = transactions
      .filter(t => t.type === 'disbursement' && t.status === 'completed')
      .reduce((sum, t) => sum + t.amount, 0)

    const pendingAmount = transactions
      .filter(t => t.status === 'pending')
      .reduce((sum, t) => sum + t.amount, 0)

    const totalTransactions = transactions.length
    const completedTransactions = transactions.filter(t => t.status === 'completed').length
    const failedTransactions = transactions.filter(t => t.status === 'failed').length

    return {
      totalRepayments,
      totalDisbursements,
      pendingAmount,
      totalTransactions,
      completedTransactions,
      failedTransactions
    }
  }

  // Export transactions to CSV
  async exportTransactions(filters: TransactionFilters = {}): Promise<Blob> {
    try {
      const queryParams = new URLSearchParams()

      if (filters.search) queryParams.append('search', filters.search)
      if (filters.type && filters.type !== 'all') queryParams.append('type', filters.type)
      if (filters.status && filters.status !== 'all') queryParams.append('status', filters.status)

      // Get all transactions without pagination for export
      queryParams.append('limit', '10000')

      const url = `${API_URL}/transactions/admin/all-transactions?${queryParams.toString()}`

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      })

      const result = await handleResponse(response)

      // Ensure the response has the expected structure
      let transactions: Transaction[]
      if (result.success && result.data && result.data.transactions) {
        transactions = result.data.transactions
      } else if (Array.isArray(result)) {
        transactions = result
      } else {
        throw new Error('Invalid response structure from export transactions API')
      }

      // Convert to CSV
      const csvContent = this.convertToCSV(transactions)
      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    } catch (error) {
      console.error('Error exporting transactions:', error)
      throw error
    }
  }

  // Helper method to convert transactions to CSV
  private convertToCSV(transactions: Transaction[]): string {
    const headers = [
      'Transaction ID',
      'User Name',
      'User Phone',
      'Loan ID',
      'Amount',
      'Type',
      'Method',
      'Status',
      'Date',
      'Reference',
      'Description'
    ]

    const csvRows = [
      headers.join(','),
      ...transactions.map(transaction => [
        transaction.id,
        `"${transaction.userName}"`,
        transaction.userPhoneNumber || '',
        transaction.loanId || '',
        transaction.amount,
        transaction.type,
        transaction.method,
        transaction.status,
        new Date(transaction.date).toLocaleString(),
        transaction.reference || '',
        `"${transaction.description || ''}"`
      ].join(','))
    ]

    return csvRows.join('\n')
  }

  // Format currency based on system settings
  formatCurrency(amount: number): string {
    // Check if we're in sandbox mode (Euro) or production (Emalangeni)
    const isSandbox = process.env.NEXT_PUBLIC_CURRENCY_SANDBOX_MODE === 'true'
    const currency = isSandbox ? '€' : 'E'

    return `${currency}${amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`
  }

  // Get transaction type display name
  getTypeDisplayName(type: string): string {
    switch (type) {
      case 'repayment':
        return 'Loan Repayment'
      case 'disbursement':
        return 'Loan Disbursement'
      case 'deposit':
        return 'Deposit'
      case 'withdrawal':
        return 'Withdrawal'
      default:
        return type.charAt(0).toUpperCase() + type.slice(1)
    }
  }

  // Get status color class
  getStatusColorClass(status: string): string {
    switch (status) {
      case 'completed':
        return 'bg-green-500'
      case 'pending':
        return 'bg-amber-500'
      case 'failed':
        return 'bg-red-500'
      case 'cancelled':
        return 'bg-gray-500'
      default:
        return 'bg-gray-500'
    }
  }

  // Get method color class
  getMethodColorClass(method: string): string {
    switch (method) {
      case 'mobile_money':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'bank_transfer':
        return 'bg-purple-100 text-purple-800 border-purple-300'
      case 'cash':
        return 'bg-green-100 text-green-800 border-green-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  // Get method display name
  getMethodDisplayName(method: string): string {
    switch (method) {
      case 'mobile_money':
        return 'Mobile Money'
      case 'bank_transfer':
        return 'Bank Transfer'
      case 'cash':
        return 'Cash'
      default:
        return method.charAt(0).toUpperCase() + method.slice(1)
    }
  }
}

export const adminTransactionsAPI = new AdminTransactionsAPI()
export default adminTransactionsAPI
