"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authApi } from './api';
import { toast } from 'sonner';

export interface User {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  role: string;
  userId?: string;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  isFaceVerified?: boolean;
  profileImage?: string;
  faceDescriptor?: string; // Stored as a JSON string of the Float32Array
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  login: (userId: string, password: string) => Promise<void>;
  logout: () => void;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  updateProfile: (profileData: Partial<User>) => Promise<void>;
  uploadProfileImage: (imageFile: File) => Promise<void>;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check for saved token on mount
    const savedToken = localStorage.getItem('token');
    const savedUser = localStorage.getItem('user');

    if (savedToken && savedUser) {
      try {
        setToken(savedToken);
        setUser(JSON.parse(savedUser));
      } catch (e) {
        // Invalid saved user data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    }

    setIsLoading(false);
  }, []);

  const login = async (userId: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authApi.login(userId, password);

      console.log('Login response in context:', response);

      if (response.success && response.data) {
        const { token, user, passwordChangeRequired } = response.data;

        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(user));

        setToken(token);
        setUser(user);

        // Check if password needs to be changed
        if (passwordChangeRequired) {
          toast.warning('Please change your default password', {
            description: response.data.message || 'You are using a default password. Please change it for security purposes.'
          });
        }
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to login');
      toast.error('Login failed', {
        description: err.message || 'An error occurred during login'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setToken(null);
    setUser(null);
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authApi.changePassword(currentPassword, newPassword);

      if (response.success) {
        // Update user data to show password has been changed
        if (user) {
          const updatedUser = { ...user, passwordChanged: true };
          localStorage.setItem('user', JSON.stringify(updatedUser));
          setUser(updatedUser);
        }

        toast.success('Password changed successfully');
      } else {
        throw new Error(response.message || 'Failed to change password');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to change password');
      toast.error('Failed to change password', {
        description: err.message || 'An error occurred'
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (profileData: Partial<User>) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authApi.updateProfile(profileData);

      if (response.success) {
        // Update user data with the new profile information
        if (user) {
          const updatedUser = { ...user, ...response.data };
          localStorage.setItem('user', JSON.stringify(updatedUser));
          setUser(updatedUser);
        }

        toast.success('Profile updated successfully');
      } else {
        throw new Error(response.message || 'Failed to update profile');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update profile');
      toast.error('Failed to update profile', {
        description: err.message || 'An error occurred'
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const uploadProfileImage = async (imageFile: File) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authApi.uploadProfileImage(imageFile);

      if (response.success) {
        // Update user data with the new profile image
        if (user && response.data && response.data.profileImage) {
          const updatedUser = { ...user, profileImage: response.data.profileImage };
          localStorage.setItem('user', JSON.stringify(updatedUser));
          setUser(updatedUser);
        }

        toast.success('Profile image updated successfully');
      } else {
        throw new Error(response.message || 'Failed to upload profile image');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to upload profile image');
      toast.error('Failed to upload profile image', {
        description: err.message || 'An error occurred'
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        isLoading,
        error,
        login,
        logout,
        changePassword,
        updateProfile,
        uploadProfileImage,
        isAuthenticated: !!token,
        isAdmin: user?.role === 'ADMIN' || user?.role === 'STAFF',
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};