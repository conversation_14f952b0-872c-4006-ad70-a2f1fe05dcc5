import * as faceapi from 'face-api.js';
import * as tf from '@tensorflow/tfjs';

export class FaceRecognitionService {
  private isInitialized = false;
  private isInitializing = false;
  private loadingPromise: Promise<boolean> | null = null;

  private checkBrowserCompatibility(): { compatible: boolean; reason?: string } {
    if (typeof window === 'undefined') {
      return { compatible: false, reason: 'Browser environment not available' };
    }

    try {
      // Check for WebGL support
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        return { compatible: false, reason: 'WebGL is not supported in this browser' };
      }

      // Check for camera access
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        return { compatible: false, reason: 'Camera access is not supported in this browser' };
      }

      return { compatible: true };
    } catch (error) {
      console.error('Error checking browser compatibility:', error);
      return { compatible: false, reason: 'Error checking browser compatibility' };
    }
  }

  async initialize(): Promise<boolean> {
    // Skip initialization if not in browser environment
    if (typeof window === 'undefined') {
      console.warn('Skipping face recognition initialization - not in browser environment');
      return false;
    }

    // First check browser compatibility
    const compatibility = this.checkBrowserCompatibility();
    if (!compatibility.compatible) {
      console.error('Browser compatibility issue:', compatibility.reason);
      return false;
    }

    if (this.isInitialized) {
      console.log('Face recognition already initialized');
      return true;
    }

    if (this.loadingPromise) {
      console.log('Face recognition initialization already in progress');
      return this.loadingPromise;
    }

    this.loadingPromise = (async () => {
      if (this.isInitializing) {
        console.log('Face recognition initialization already in progress (inner check)');
        return false;
      }

      try {
        this.isInitializing = true;
        console.log('Initializing TensorFlow.js backend...');

        // Initialize TensorFlow.js with WebGL backend
        await tf.setBackend('webgl');
        await tf.ready();
        console.log('TensorFlow.js backend initialized:', tf.getBackend());

        // Configure face detector options
        console.log('Configuring face detector options');
        const options = new faceapi.TinyFaceDetectorOptions({
          inputSize: 320, // smaller size for better performance
          scoreThreshold: 0.5
        });

        // Load models sequentially to avoid memory issues
        try {
          console.log('Loading tiny face detector model...');
          await faceapi.nets.tinyFaceDetector.loadFromUri('/models');
          console.log('Tiny face detector model loaded successfully');
        } catch (e) {
          console.error('Failed to load tiny face detector model:', e);
          throw new Error('Failed to load tiny face detector model');
        }

        try {
          console.log('Loading face landmark model...');
          await faceapi.nets.faceLandmark68Net.loadFromUri('/models');
          console.log('Face landmark model loaded successfully');
        } catch (e) {
          console.error('Failed to load face landmark model:', e);
          throw new Error('Failed to load face landmark model');
        }

        try {
          console.log('Loading face recognition model...');
          await faceapi.nets.faceRecognitionNet.loadFromUri('/models');
          console.log('Face recognition model loaded successfully');
        } catch (e) {
          console.error('Failed to load face recognition model:', e);
          throw new Error('Failed to load face recognition model');
        }

        // Verify models are loaded
        console.log('Verifying models loaded correctly...');
        if (!faceapi.nets.tinyFaceDetector.isLoaded) {
          throw new Error('Failed to load tiny face detector model');
        }
        if (!faceapi.nets.faceLandmark68Net.isLoaded) {
          throw new Error('Failed to load face landmark model');
        }
        if (!faceapi.nets.faceRecognitionNet.isLoaded) {
          throw new Error('Failed to load face recognition model');
        }

        console.log('Face recognition models loaded successfully');
        this.isInitialized = true;
        return true;
      } catch (error) {
        console.error('Error loading face recognition models:', error);
        this.isInitialized = false;
        throw error;
      } finally {
        this.isInitializing = false;
        this.loadingPromise = null;
      }
    })();

    return this.loadingPromise;
  }

  async detectFace(input: HTMLImageElement | HTMLVideoElement): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        throw new Error('Face recognition service not initialized');
      }

      console.log('Detecting face...');
      const options = new faceapi.TinyFaceDetectorOptions({
        inputSize: 416,
        scoreThreshold: 0.5
      });

      const detections = await faceapi.detectAllFaces(input, options);
      console.log(`Detected ${detections.length} face(s)`);
      return detections.length > 0;
    } catch (error) {
      console.error('Error detecting face:', error);
      throw error;
    }
  }

  async getFaceDescriptor(input: HTMLImageElement | HTMLVideoElement): Promise<Float32Array> {
    try {
      if (!this.isInitialized) {
        throw new Error('Face recognition service not initialized');
      }

      console.log('Getting face descriptor...');
      const options = new faceapi.TinyFaceDetectorOptions({
        inputSize: 416,
        scoreThreshold: 0.5
      });

      const detection = await faceapi.detectSingleFace(input, options)
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (!detection) {
        throw new Error('No face detected in the image');
      }

      console.log('Face descriptor extracted successfully');
      return detection.descriptor;
    } catch (error) {
      console.error('Error getting face descriptor:', error);
      throw error;
    }
  }

  async verifyFace(
    input: HTMLImageElement | HTMLVideoElement,
    referenceDescriptor: Float32Array
  ): Promise<{ verified: boolean; similarity: number }> {
    try {
      if (!this.isInitialized) {
        console.error('Face recognition service not initialized');
        throw new Error('Face recognition service not initialized');
      }

      console.log('Verifying face...');
      console.log('Reference descriptor length:', referenceDescriptor.length);

      // Get the current face descriptor
      console.log('Getting current face descriptor from input...');
      const currentDescriptor = await this.getFaceDescriptor(input);
      console.log('Current descriptor obtained, length:', currentDescriptor.length);

      // Calculate the distance between the two descriptors
      console.log('Calculating euclidean distance...');
      const distance = faceapi.euclideanDistance(currentDescriptor, referenceDescriptor);
      console.log('Distance calculated:', distance);

      // Convert distance to similarity score (0-1)
      const similarity = 1 - distance;
      console.log('Similarity calculated:', similarity);

      // Determine if the face is verified based on similarity threshold
      const threshold = 0.6; // 60% similarity threshold
      const verified = similarity > threshold;

      console.log(`Face verification result: ${verified ? 'Verified' : 'Not verified'} (similarity: ${(similarity * 100).toFixed(1)}%, threshold: ${(threshold * 100).toFixed(1)}%)`);
      return { verified, similarity };
    } catch (error) {
      console.error('Error verifying face:', error);
      throw error;
    }
  }
}