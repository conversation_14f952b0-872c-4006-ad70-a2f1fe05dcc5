import * as ort from 'onnxruntime-web';

export type LivenessCheckResult = {
  isLive: boolean;
  message: string;
  confidence?: number;
};

export type FaceComparisonResult = {
  isMatch: boolean;
  message: string;
  similarity?: number;
};

export type FaceDetection = {
  score: number;
  box: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
};

class InsightFaceService {
  private modelsLoaded: boolean = false;
  private sessions: {
    detection: ort.InferenceSession | null;
    recognition: ort.InferenceSession | null;
    liveness: ort.InferenceSession | null;
  } = {
    detection: null,
    recognition: null,
    liveness: null
  };

  private readonly MODEL_PATHS = {
    detection: '/models/insightface/detection.onnx',
    recognition: '/models/insightface/recognition.onnx',
    liveness: '/models/insightface/liveness.onnx'
  };

  async initialize(): Promise<boolean> {
    try {
      if (this.modelsLoaded) {
        return true;
      }

      console.log('Initializing InsightFace models...');
      
      // Load detection model
      this.sessions.detection = await ort.InferenceSession.create(this.MODEL_PATHS.detection, {
        executionProviders: ['wasm'],
        graphOptimizationLevel: 'all'
      });
      console.log('Detection model loaded');

      // Load recognition model
      this.sessions.recognition = await ort.InferenceSession.create(this.MODEL_PATHS.recognition, {
        executionProviders: ['wasm'],
        graphOptimizationLevel: 'all'
      });
      console.log('Recognition model loaded');

      // Load liveness model
      this.sessions.liveness = await ort.InferenceSession.create(this.MODEL_PATHS.liveness, {
        executionProviders: ['wasm'],
        graphOptimizationLevel: 'all'
      });
      console.log('Liveness model loaded');

      this.modelsLoaded = true;
      return true;
    } catch (error) {
      console.error('Error initializing InsightFace models:', error);
      return false;
    }
  }

  private async preprocessImage(imageElement: HTMLImageElement | HTMLVideoElement, targetSize: [number, number]): Promise<ort.Tensor> {
    const canvas = document.createElement('canvas');
    const [width, height] = targetSize;
    canvas.width = width;
    canvas.height = height;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not get 2D context from canvas');
    }
    
    // Draw and resize image
    ctx.drawImage(imageElement, 0, 0, width, height);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, width, height);
    
    // Convert to float32 and normalize
    const float32Data = new Float32Array(width * height * 3);
    for (let i = 0; i < imageData.data.length; i += 4) {
      const idx = i / 4;
      float32Data[idx] = (imageData.data[i] - 127.5) / 128.0;     // R
      float32Data[idx + width * height] = (imageData.data[i + 1] - 127.5) / 128.0;     // G
      float32Data[idx + 2 * width * height] = (imageData.data[i + 2] - 127.5) / 128.0; // B
    }
    
    return new ort.Tensor('float32', float32Data, [1, 3, height, width]);
  }

  async detectFace(imageElement: HTMLImageElement | HTMLVideoElement): Promise<FaceDetection | null> {
    try {
      if (!this.modelsLoaded) {
        await this.initialize();
      }

      if (!this.sessions.detection) {
        console.error('Detection model not loaded');
        return null;
      }

      // Preprocess image for detection model (640x640)
      const inputTensor = await this.preprocessImage(imageElement, [640, 640]);
      
      // Run detection
      const results = await this.sessions.detection.run({ input: inputTensor });
      
      if (!results.boxes || !results.scores) {
        console.error('Invalid detection model output');
        return null;
      }

      const boxes = results.boxes.data as Float32Array;
      const scores = results.scores.data as Float32Array;
      
      // Find best detection
      let maxScore = 0;
      let bestBox = null;
      
      for (let i = 0; i < scores.length; i++) {
        if (scores[i] > maxScore) {
          maxScore = scores[i];
          const x1 = boxes[i * 4];
          const y1 = boxes[i * 4 + 1];
          const x2 = boxes[i * 4 + 2];
          const y2 = boxes[i * 4 + 3];
          bestBox = {
            x: x1,
            y: y1,
            width: x2 - x1,
            height: y2 - y1
          };
        }
      }

      if (!bestBox || maxScore < 0.5) {
        console.log('No face detected or confidence too low');
        return null;
      }

      return {
        score: maxScore,
        box: bestBox
      };
    } catch (error) {
      console.error('Error during face detection:', error);
      return null;
    }
  }

  async getFaceDescriptor(imageElement: HTMLImageElement | HTMLVideoElement): Promise<Float32Array | null> {
    try {
      if (!this.modelsLoaded) {
        await this.initialize();
      }

      if (!this.sessions.recognition) {
        console.error('Recognition model not loaded');
        return null;
      }

      // First detect face
      const detection = await this.detectFace(imageElement);
      if (!detection) {
        console.warn('No face detected for feature extraction');
        return null;
      }

      // Crop and preprocess face for recognition model (112x112)
      const inputTensor = await this.preprocessImage(imageElement, [112, 112]);
      
      // Run recognition
      const results = await this.sessions.recognition.run({ input: inputTensor });
      
      if (!results.output) {
        console.error('Invalid recognition model output');
        return null;
      }

      return results.output.data as Float32Array;
    } catch (error) {
      console.error('Error getting face descriptor:', error);
      return null;
    }
  }

  async checkLiveness(videoElement: HTMLVideoElement): Promise<LivenessCheckResult> {
    try {
      if (!this.modelsLoaded) {
        await this.initialize();
      }

      if (!this.sessions.liveness) {
        console.error('Liveness model not loaded');
        return {
          isLive: false,
          message: 'Liveness detection model not available'
        };
      }

      // First detect face
      const detection = await this.detectFace(videoElement);
      if (!detection) {
        return {
          isLive: false,
          message: 'No face detected during liveness check'
        };
      }

      // Preprocess for liveness model (224x224)
      const inputTensor = await this.preprocessImage(videoElement, [224, 224]);
      
      // Run liveness detection
      const results = await this.sessions.liveness.run({ input: inputTensor });
      
      if (!results.output) {
        console.error('Invalid liveness model output');
        return {
          isLive: false,
          message: 'Error processing liveness check'
        };
      }

      const score = results.output.data[0] as number;
      const isLive = score > 0.5;

      return {
        isLive,
        message: isLive ? 'Liveness check passed' : 'Liveness check failed',
        confidence: score
      };
    } catch (error) {
      console.error('Error during liveness check:', error);
      return {
        isLive: false,
        message: 'Error during liveness check: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  async compareFaces(
    referenceDescriptor: Float32Array,
    currentDescriptor: Float32Array
  ): Promise<FaceComparisonResult> {
    try {
      if (referenceDescriptor.length !== currentDescriptor.length) {
        throw new Error('Face descriptors have different lengths');
      }

      // Calculate cosine similarity
      let dotProduct = 0;
      let norm1 = 0;
      let norm2 = 0;

      for (let i = 0; i < referenceDescriptor.length; i++) {
        dotProduct += referenceDescriptor[i] * currentDescriptor[i];
        norm1 += referenceDescriptor[i] * referenceDescriptor[i];
        norm2 += currentDescriptor[i] * currentDescriptor[i];
      }

      const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
      const isMatch = similarity > 0.6; // Adjust threshold as needed

      return {
        isMatch,
        message: isMatch ? 'Face match successful' : 'Face does not match the reference',
        similarity
      };
    } catch (error) {
      console.error('Error comparing faces:', error);
      return {
        isMatch: false,
        message: 'Error comparing faces: ' + (error instanceof Error ? error.message : String(error))
      };
    }
  }

  storeReferenceDescriptor(userId: string, descriptor: Float32Array): void {
    // Store in localStorage for demo purposes
    // In production, use a secure backend storage
    localStorage.setItem(`face_descriptor_${userId}`, JSON.stringify(Array.from(descriptor)));
  }

  getReferenceDescriptor(userId: string): Float32Array | null {
    const stored = localStorage.getItem(`face_descriptor_${userId}`);
    if (!stored) return null;
    return new Float32Array(JSON.parse(stored));
  }

  hasReferenceDescriptor(userId: string): boolean {
    return localStorage.getItem(`face_descriptor_${userId}`) !== null;
  }
}

// Export a singleton instance
export const insightFaceService = new InsightFaceService(); 