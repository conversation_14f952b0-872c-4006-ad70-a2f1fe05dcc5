import * as faceapi from 'face-api.js';

// Simple flag to track loading status
let modelsLoaded = false;
let loadingInProgress = false;
let loadError: Error | null = null;

// Function to load models directly
export async function loadFaceApiModels(): Promise<boolean> {
  // If models are already loaded, return immediately
  if (modelsLoaded) {
    console.log('Models already loaded, skipping load');
    return true;
  }
  
  // If loading is in progress, wait for it to complete
  if (loadingInProgress) {
    console.log('Model loading already in progress, waiting...');
    // Wait for loading to complete (up to 15 seconds)
    for (let i = 0; i < 30; i++) {
      await new Promise(resolve => setTimeout(resolve, 500));
      if (modelsLoaded || loadError) break;
    }
    
    if (modelsLoaded) return true;
    if (loadError) throw loadError;
    throw new Error('Timed out waiting for models to load');
  }
  
  // Start loading
  loadingInProgress = true;
  loadError = null;
  
  try {
    console.log('Starting to load face-api.js models...');
    
    // Load models in parallel for speed
    await Promise.all([
      faceapi.nets.tinyFaceDetector.load('/models'),
      faceapi.nets.faceLandmark68Net.load('/models'),
      faceapi.nets.faceRecognitionNet.load('/models')
    ]);
    
    // Verify models are loaded
    const allLoaded = 
      faceapi.nets.tinyFaceDetector.isLoaded && 
      faceapi.nets.faceLandmark68Net.isLoaded && 
      faceapi.nets.faceRecognitionNet.isLoaded;
    
    if (!allLoaded) {
      throw new Error('Not all models were loaded successfully');
    }
    
    console.log('All face-api.js models loaded successfully');
    modelsLoaded = true;
    return true;
  } catch (error) {
    console.error('Error loading face-api.js models:', error);
    loadError = error instanceof Error ? error : new Error(String(error));
    throw loadError;
  } finally {
    loadingInProgress = false;
  }
}

// Function to check if models are loaded
export function areModelsLoaded(): boolean {
  return modelsLoaded;
}

// Function to get loading status
export function getModelLoadingStatus() {
  return {
    loaded: modelsLoaded,
    loading: loadingInProgress,
    error: loadError
  };
}

// If in browser environment, start loading immediately
if (typeof window !== 'undefined') {
  // Use setTimeout to ensure this runs after the page has loaded
  setTimeout(() => {
    loadFaceApiModels().catch(err => {
      console.error('Background loading of models failed:', err);
    });
  }, 1000);
}
