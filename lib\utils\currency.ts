/**
 * Format a number as <PERSON><PERSON><PERSON><PERSON> l<PERSON> (SZL) or Emalangeni (E) currency
 * @param amount - The amount to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  options: {
    notation?: 'standard' | 'compact',
    symbol?: 'E' | 'SZL',
    decimals?: number
  } = {}
): string {
  const {
    notation = 'standard',
    symbol = 'E',
    decimals = 2
  } = options;

  // Format the number with the specified options
  const formattedNumber = new Intl.NumberFormat('en-SZ', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    notation: notation
  }).format(amount);

  // Return the formatted currency string
  return `${symbol} ${formattedNumber}`;
}

/**
 * Parse a currency string into a number
 * @param currencyString - The currency string to parse
 * @returns The parsed number
 */
export function parseCurrency(currencyString: string): number {
  // Remove currency symbol and any non-numeric characters except decimal point
  const numericString = currencyString.replace(/[^0-9.]/g, '');
  return parseFloat(numericString);
}
