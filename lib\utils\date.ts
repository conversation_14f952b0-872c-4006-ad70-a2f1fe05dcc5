/**
 * Format a date using GMT timezone
 * @param date - The date to format
 * @param options - Formatting options
 * @returns Formatted date string
 */
export function formatDate(
  date: Date | string,
  options: {
    format?: 'short' | 'medium' | 'long' | 'full',
    includeTime?: boolean
  } = {}
): string {
  const {
    format = 'medium',
    includeTime = false
  } = options;

  // Convert string to Date if necessary
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Format options
  const dateFormatOptions: Intl.DateTimeFormatOptions = {
    timeZone: 'GMT',
  };

  // Set date style based on format
  switch (format) {
    case 'short':
      dateFormatOptions.dateStyle = 'short';
      break;
    case 'medium':
      dateFormatOptions.dateStyle = 'medium';
      break;
    case 'long':
      dateFormatOptions.dateStyle = 'long';
      break;
    case 'full':
      dateFormatOptions.dateStyle = 'full';
      break;
  }

  // Add time if requested
  if (includeTime) {
    dateFormatOptions.timeStyle = 'medium';
  }

  // Format the date
  return new Intl.DateTimeFormat('en-GB', dateFormatOptions).format(dateObj);
}

/**
 * Format a relative time (e.g., "2 hours ago")
 * @param date - The date to format
 * @returns Formatted relative time string
 */
export function formatRelativeTime(date: Date | string | null | undefined): string {
  // Handle null or undefined dates
  if (!date) {
    return 'Unknown time';
  }

  // Convert string to Date if necessary
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) {
    return 'Invalid date';
  }

  const now = new Date();
  const diffInMs = now.getTime() - dateObj.getTime();
  const diffInHours = diffInMs / (1000 * 60 * 60);
  const diffInDays = diffInHours / 24;

  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    const hours = Math.floor(diffInHours);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffInDays < 7) {
    const days = Math.floor(diffInDays);
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  } else {
    // For older dates, use the standard date format
    return formatDate(dateObj);
  }
}
