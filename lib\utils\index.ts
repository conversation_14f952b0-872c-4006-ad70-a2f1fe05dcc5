// Export all utility functions
export * from './currency';
export * from './date';

// Default currency and timezone settings
export const DEFAULT_CURRENCY = 'E'; // Emalangeni
export const DEFAULT_TIMEZONE = 'GMT';

// Format a number with commas as thousands separators
export function formatNumber(num: number, decimals = 0): string {
  return new Intl.NumberFormat('en-SZ', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(num);
}

// Generate a human-readable ID from a UUID
export function formatId(uuid: string, prefix = 'ID'): string {
  if (!uuid) return '';
  // Take the last 6 characters of the UUID
  const shortId = uuid.slice(-6).toUpperCase();
  return `${prefix}-${shortId}`;
}

// Format a loan ID
export function formatLoanId(uuid: string): string {
  return formatId(uuid, 'LOAN');
}

// Format a transaction ID
export function formatTransactionId(uuid: string): string {
  return formatId(uuid, 'TXN');
}

// Format a user ID
export function formatUserId(uuid: string): string {
  return formatId(uuid, 'USER');
}
