/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // If it's a server build, ignore TensorFlow.js and face-api.js
    if (isServer) {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@tensorflow/tfjs': false,
        'face-api.js': false,
      }
    }

    // Fix for 'fs' module error in face-api.js
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
      encoding: false
    };
    
    // Add rules for ONNX WASM files
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'asset/resource',
      generator: {
        filename: 'static/wasm/[name][ext]'
      }
    });
    
    return config;
  },
  // Configure headers to allow WASM files to be loaded
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin'
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp'
          }
        ]
      }
    ];
  },
  // Allow WASM files to be properly loaded
  experimental: {
    serverActions: true,
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  // Enable compression
  compress: true,
  // Optimize images
  images: {
    domains: [],
    formats: ['image/avif', 'image/webp'],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Enable React strict mode
  reactStrictMode: true,
  // Enable production source maps
  productionBrowserSourceMaps: true,
}

module.exports = nextConfig;