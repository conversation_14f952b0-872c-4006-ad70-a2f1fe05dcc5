const fs = require('fs');
const https = require('https');
const path = require('path');

// Ensure the models directory exists
const modelsDir = path.join(__dirname, 'models');
if (!fs.existsSync(modelsDir)){
  fs.mkdirSync(modelsDir, { recursive: true });
}

// Define the models to download
const models = [
  {
    name: 'fr_age.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_age.onnx',
  },
  {
    name: 'fr_detect.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_detect.onnx',
  },
  {
    name: 'fr_expression.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_expression.onnx',
  },
  {
    name: 'fr_eye.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_eye.onnx',
  },
  {
    name: 'fr_feature.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_feature.onnx',
  },
  {
    name: 'fr_gender.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_gender.onnx',
  },
  {
    name: 'fr_landmark.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_landmark.onnx',
  },
  {
    name: 'fr_liveness.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_liveness.onnx',
  },
  {
    name: 'fr_pose.onnx',
    url: 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/blob/main/model/fr_pose.onnx',
  },
];

// First delete existing model files to ensure a fresh start
console.log('Deleting existing model files...');
models.forEach(model => {
  const filePath = path.join(modelsDir, model.name);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`Deleted ${filePath}`);
  }
});

// Download each model
console.log('Starting download of fresh model files...');
models.forEach(model => {
  const filePath = path.join(modelsDir, model.name);
  console.log(`Downloading ${model.url} to ${filePath}`);
  
  const file = fs.createWriteStream(filePath);
  
  https.get(model.url, (response) => {
    response.pipe(file);
    
    file.on('finish', () => {
      file.close();
      console.log(`Downloaded ${model.name} successfully`);
    });
  }).on('error', (err) => {
    fs.unlinkSync(filePath);
    console.error(`Error downloading ${model.name}:`, err.message);
  });
});

console.log('Model download process started. Please wait for all downloads to complete...'); 