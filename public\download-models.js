const fs = require('fs');
const https = require('https');
const path = require('path');

const modelsDir = path.join(__dirname, 'models');

// Create models directory if it doesn't exist
if (!fs.existsSync(modelsDir)) {
  fs.mkdirSync(modelsDir, { recursive: true });
}

// Models to download
const models = [
    'fr_age.onnx',
    'fr_detect.onnx',
    'fr_expression.onnx',
    'fr_feature.onnx',
    'fr_gender.onnx',
    'fr_landmark.onnx',
    'fr_liveness.onnx',
    'fr_pose.onnx',
];

// Base URL for models
const baseUrl = 'https://github.com/Faceplugin-ltd/FaceRecognition-LivenessDetection-Javascript/tree/main/model';

// Download function
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    console.log(`Downloading ${url} to ${dest}`);
    const file = fs.createWriteStream(dest);
    https.get(url, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(dest, () => {});
      reject(err);
    });
  });
}

// Download all models
async function downloadModels() {
  for (const model of models) {
    const url = baseUrl + model;
    const dest = path.join(modelsDir, model);
    await downloadFile(url, dest);
  }
  console.log('All models downloaded successfully!');
}

downloadModels().catch(console.error); 