# Face-API.js Models

This directory should contain the Face-API.js model files. You need to download these files from the Face-API.js GitHub repository and place them in this directory.

## Required Models

1. tiny_face_detector_model-weights_manifest.json
2. tiny_face_detector_model-shard1
3. face_landmark_68_model-weights_manifest.json
4. face_landmark_68_model-shard1
5. face_recognition_model-weights_manifest.json
6. face_recognition_model-shard1
7. face_recognition_model-shard2
8. face_expression_model-weights_manifest.json
9. face_expression_model-shard1

## How to Download

You can download these models from the Face-API.js GitHub repository:
https://github.com/justadudewhohacks/face-api.js/tree/master/weights

Or you can use the following command to download them:

```bash
mkdir -p models
cd models
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/tiny_face_detector_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/tiny_face_detector_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_landmark_68_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_landmark_68_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-shard1
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_recognition_model-shard2
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_expression_model-weights_manifest.json
wget https://github.com/justadudewhohacks/face-api.js/raw/master/weights/face_expression_model-shard1

