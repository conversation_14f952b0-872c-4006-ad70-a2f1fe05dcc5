const fs = require('fs');
const path = require('path');

// Create directories if they don't exist
const publicDir = path.join(__dirname, '../public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

const staticWasmDir = path.join(publicDir, 'static/wasm');
if (!fs.existsSync(staticWasmDir)) {
  fs.mkdirSync(staticWasmDir, { recursive: true });
}

// Source paths
const sourceDir = path.join(__dirname, '../node_modules/onnxruntime-web/dist');
const wasmFiles = [
  'ort-wasm-simd-threaded.wasm',
  'ort-wasm-simd-threaded.jsep.wasm'
];

// Copy files
wasmFiles.forEach(file => {
  const sourcePath = path.join(sourceDir, file);
  const publicPath = path.join(publicDir, file);
  const staticPath = path.join(staticWasmDir, file);
  
  if (fs.existsSync(sourcePath)) {
    // Copy to public root (for backward compatibility)
    fs.copyFileSync(sourcePath, publicPath);
    console.log(`Copied ${file} to ${publicPath}`);
    
    // Copy to static/wasm directory (for next.js webpack configuration)
    fs.copyFileSync(sourcePath, staticPath);
    console.log(`Copied ${file} to ${staticPath}`);
  } else {
    console.warn(`Source file not found: ${sourcePath}`);
  }
});

console.log('WASM files copied successfully'); 