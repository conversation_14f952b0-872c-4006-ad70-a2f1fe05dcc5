const https = require('https');
const fs = require('fs');
const path = require('path');

const modelsDir = path.join(__dirname, '../public/models');
const models = [
  {
    name: 'tiny_face_detector_model-weights_manifest.json',
    url: 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/tiny_face_detector_model-weights_manifest.json'
  },
  {
    name: 'tiny_face_detector_model-shard1',
    url: 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/tiny_face_detector_model-shard1'
  },
  {
    name: 'face_landmark_68_model-weights_manifest.json',
    url: 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_landmark_68_model-weights_manifest.json'
  },
  {
    name: 'face_landmark_68_model-shard1',
    url: 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_landmark_68_model-shard1'
  },
  {
    name: 'face_recognition_model-weights_manifest.json',
    url: 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_recognition_model-weights_manifest.json'
  },
  {
    name: 'face_recognition_model-shard1',
    url: 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights/face_recognition_model-shard1'
  }
];

// Create models directory if it doesn't exist
if (!fs.existsSync(modelsDir)) {
  fs.mkdirSync(modelsDir, { recursive: true });
}

// Clean up old models
console.log('Cleaning up old models...');
fs.readdirSync(modelsDir).forEach(file => {
  const filePath = path.join(modelsDir, file);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }
});

// Download function with redirect handling
function downloadFile(url, dest) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, response => {
      // Handle redirects
      if (response.statusCode === 301 || response.statusCode === 302) {
        console.log(`Following redirect for ${url} to ${response.headers.location}`);
        downloadFile(response.headers.location, dest).then(resolve).catch(reject);
        return;
      }

      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode} ${response.statusMessage}`));
        return;
      }

      const file = fs.createWriteStream(dest);
      response.pipe(file);

      file.on('finish', () => {
        file.close();
        // Verify file exists and has content
        fs.stat(dest, (err, stats) => {
          if (err) {
            reject(err);
            return;
          }
          if (stats.size === 0) {
            reject(new Error(`Downloaded file ${dest} is empty`));
            return;
          }
          resolve();
        });
      });

      file.on('error', err => {
        fs.unlink(dest, () => reject(err));
      });
    });

    request.on('error', err => {
      reject(err);
    });

    // Set timeout
    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error(`Download timeout for ${url}`));
    });
  });
}

// Download all models with retry logic
async function downloadModels() {
  console.log('Starting model downloads...');
  for (const model of models) {
    const dest = path.join(modelsDir, model.name);
    console.log(`Downloading ${model.name}...`);
    
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        await downloadFile(model.url, dest);
        
        // Verify JSON files
        if (model.name.endsWith('.json')) {
          const content = fs.readFileSync(dest, 'utf8');
          JSON.parse(content); // Will throw if invalid JSON
        }
        
        console.log(`Successfully downloaded ${model.name}`);
        break;
      } catch (error) {
        attempts++;
        console.error(`Attempt ${attempts}/${maxAttempts} failed for ${model.name}:`, error.message);
        
        if (attempts === maxAttempts) {
          console.error(`Failed to download ${model.name} after ${maxAttempts} attempts`);
          process.exit(1);
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
  console.log('All downloads completed!');
}

downloadModels().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
}); 