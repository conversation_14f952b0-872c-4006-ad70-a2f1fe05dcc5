declare module 'onnxruntime-web' {
  export interface Tensor {
    data: Float32Array | Uint8Array | Int8Array | any;
    dims: number[];
    type: string;
  }

  export interface InferenceSessionOptions {
    executionProviders?: string[];
    graphOptimizationLevel?: 'disabled' | 'basic' | 'extended' | 'all';
    enableCpuMemArena?: boolean;
    enableMemPattern?: boolean;
    executionMode?: 'sequential' | 'parallel';
    logLevel?: 'verbose' | 'info' | 'warning' | 'error' | 'fatal';
  }

  export class InferenceSession {
    static create(path: string, options?: InferenceSessionOptions): Promise<InferenceSession>;
    run(feeds: { [key: string]: Tensor }): Promise<{ [key: string]: Tensor }>;
  }

  export class Tensor {
    constructor(type: string, data: Float32Array | Uint8Array | Int8Array | any, dims: number[]);
    data: Float32Array | Uint8Array | Int8Array | any;
    dims: number[];
    type: string;
  }

  export const env: {
    wasm: {
      wasmPaths: string | Record<string, string>;
      numThreads: number;
    }
  };
} 